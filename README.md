# 业财一体化系统

## 项目介绍

业财一体化系统是一个基于Vue 3和Ant Design Vue开发的企业级应用，旨在整合企业的业务流程和财务管理，实现从客户开发、项目管理、合同执行到财务核算的全流程数字化管理。

## 技术栈

- 前端框架：Vue 3 (使用Composition API)
- UI组件库：Ant Design Vue
- CSS框架：Tailwind CSS
- 包管理器：pnpm
- 构建工具：Vite
- 状态管理：Pinia
- 路由：Vue Router
- HTTP客户端：Axios
- 图表库：ECharts

## 功能模块

系统包含以下10个主要功能模块：

1. 客户与销售机会管理模块
2. 项目管理模块
3. 合同管理模块
4. 销售管理模块
5. 采购与库存管理模块
6. 财务核算模块
7. 预算与成本管理模块
8. 人力管理模块
9. 报表与分析模块
10. 文件管理模块

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 代码检查

```bash
pnpm lint
```

## 目录结构

```
业财一体化系统/
├── src/
│   ├── api/                 # API请求
│   ├── assets/              # 项目资源
│   ├── components/          # 公共组件
│   ├── composables/         # 组合式函数
│   ├── config/              # 配置文件
│   ├── directives/          # 自定义指令
│   ├── hooks/               # 自定义Hooks
│   ├── layouts/             # 布局组件
│   ├── router/              # 路由配置
│   ├── stores/              # Pinia状态管理
│   ├── utils/               # 工具函数
│   ├── views/               # 页面组件(按功能模块组织)
│   │   ├── customer/        # 客户与销售机会管理
│   │   ├── project/         # 项目管理
│   │   ├── contract/        # 合同管理
│   │   ├── sales/           # 销售管理
│   │   ├── purchase/        # 采购与库存管理
│   │   ├── finance/         # 财务核算
│   │   ├── budget/          # 预算与成本管理
│   │   ├── hr/              # 人力管理
│   │   ├── report/          # 报表与分析
│   │   ├── file/            # 文件管理
│   │   ├── dashboard/       # 仪表盘
│   │   └── system/          # 系统设置
│   ├── App.vue              # 根组件
│   └── main.ts              # 入口文件
```

## 开发计划

- [x] 项目初始化与技术栈集成
- [x] 基础布局和路由设置
- [ ] 登录页面
- [ ] 仪表盘页面
- [ ] 客户列表页面
- [ ] 客户详情页面
- [ ] 客户新增与编辑页面
- [ ] 销售机会管理页面
- [ ] 项目管理相关页面
- [ ] 合同管理相关页面
- [ ] 销售管理相关页面
- [ ] 采购与库存管理相关页面
- [ ] 财务核算相关页面
- [ ] 预算与成本管理相关页面
- [ ] 人力管理相关页面
- [ ] 报表与分析相关页面
- [ ] 文件管理相关页面
- [ ] 系统设置相关页面

## 使用指南

### 登录系统

目前系统提供测试账号：
- 用户名：admin
- 密码：admin123

## 贡献者

- 开发团队

## 许可证

本项目采用MIT许可证 