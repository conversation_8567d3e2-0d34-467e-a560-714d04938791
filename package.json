{"name": "erp-finance-system", "version": "1.0.0", "description": "业财一体化系统 - 整合企业业务流程和财务管理的全流程数字化管理系统", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build-only": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "keywords": ["ERP", "财务管理", "业务管理", "CRM", "项目管理"], "author": "", "license": "MIT", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.0.0", "axios": "^1.8.3", "dayjs": "^1.11.13", "echarts": "^5.6.0", "pinia": "^3.0.1", "signature_pad": "^5.0.7", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/babel-plugin-jsx": "^1.4.0", "autoprefixer": "10.4.17", "eslint": "^9.22.0", "less": "^4.2.2", "postcss": "8.4.35", "prettier": "^3.5.3", "sass-embedded": "^1.86.3", "tailwindcss": "3.4.1", "typescript": "^5.7.3", "vite": "^6.2.2", "vue-tsc": "^2.2.8"}}