# 📐 编码规则与业务规范

## 1. 编码规则

### 1.1 客户编码 (Client Code)
- **目的：** 唯一标识系统中的每个客户（包括企业客户和政府机构）
- **规则：** `[前缀] + [两位年份] + [月日] + [流水号]`
- **格式说明：**
  - **前缀 (1位字符)：** `C` (企业客户), `G` (政府客户)
  - **两位年份 (2位数字)：** `YY` (例如 `25` 代表 2025年)
  - **月日 (4位数字)：** `MMDD` (例如 `0606` 代表 6月6日)
  - **流水号 (3位数字)：** `NNN` (当天内递增序号，从 `001` 开始，不足三位补零)
- **示例：**
  - `C250606001` (2025年6月6日创建的第1个企业客户)
  - `G250607002` (2025年6月7日创建的第2个政府客户)
- **总长度：** 10位
- **生成方式：** 系统自动生成

### 1.2 供应商编码 (Vendor Code)
- **目的：** 唯一标识系统中的每个供应商
- **规则：** `[前缀] + [两位年份] + [月日] + [流水号]`
- **格式说明：**
  - **前缀 (1位字符)：** `V` (供应商)
  - **两位年份 (2位数字)：** `YY`
  - **月日 (4位数字)：** `MMDD`
  - **流水号 (3位数字)：** `NNN` (当天内递增序号，从 `001` 开始，不足三位补零)
- **示例：** `V250606001` (2025年6月6日创建的第1个供应商)
- **总长度：** 10位
- **生成方式：** 系统自动生成

### 1.3 项目编码 (Project Code)
- **目的：** 唯一标识系统中的每个项目，并明确该项目是为哪个客户服务的
- **规则：** `[项目前缀] + [全局项目流水号] + [分隔符] + [客户编码]`
- **格式说明：**
  - **项目前缀 (1位字符)：** `P` (项目)
  - **全局项目流水号 (3位数字)：** `NNN` (系统内所有项目统一递增的序号，从 `001` 开始，不足三位补零。此流水号全局唯一，不按日期或客户重置)
  - **分隔符 (1位字符)：** `-`
  - **客户编码 (10位字符)：** 该项目所关联的**客户编码**
- **示例：**
  - `P001-C250606001` (系统内第1个项目，为客户 `C250606001` 服务的)
  - `P002-G250607002` (系统内第2个项目，为政府客户 `G250607002` 服务的)
- **总长度：** 15位 (1 + 3 + 1 + 10)
- **生成方式：** 系统自动生成，创建项目时必须选择一个客户

### 1.4 合同编号 (Contract Number)
- **目的：** 唯一标识系统中的每份合同，明确其销售/采购性质，并通过项目编码清晰地追溯到所属项目及其客户
- **规则：** `[合同类型前缀] + [合同在项目下的流水号] + [分隔符] + [项目编码]`

#### 1.4.1 销售合同 (SAL)
- **规则：** `SAL[合同在项目下的流水号]-[项目编码]`
- **格式说明：**
  - **合同类型前缀 (3位字符)：** `SAL`
  - **合同在项目下的流水号 (3位数字)：** `NNN` (在同一特定项目下，合同的递增序号，从 `001` 开始)
  - **分隔符 (1位字符)：** `-`
  - **项目编码 (15位字符)：** 完整嵌入该销售合同所属项目的编码
- **示例：** `SAL001-P001-C250606001` (为项目 `P001-C250606001` 签署的第1份销售合同)
- **总长度：** 22位 (3 + 3 + 1 + 15)

#### 1.4.2 采购合同 (PUR)
- **规则：** `PUR[合同在项目下的流水号]-[项目编码]`
- **格式说明：** 同销售合同
- **示例：** `PUR001-P001-C250606001` (为项目 `P001-C250606001` 签署的第1份采购合同)
- **总长度：** 22位

#### 1.4.3 其他类型合同 (OTH)
- **规则：** `OTH[合同在项目下的流水号]-[项目编码]`
- **示例：** `OTH001-P001-C250606001` (为项目 `P001-C250606001` 签署的第1份其他类型合同)
- **总长度：** 22位

## 2. 业务状态定义

### 2.1 项目状态分类
项目的状态通常是线性的，反映了项目进展到哪个阶段：

- **准备中/待启动 (Pending/Planning):**
  - 含义：项目已立项或初步确定，但尚未正式开始执行
  - 特点：尚未进入实际操作阶段
  - 示例：销售合同已签署，正在等待客户提供启动资料

- **进行中 (In Progress/Active):**
  - 含义：项目已正式启动，并正在按照计划执行各项任务和活动
  - 特点：核心工作阶段，资源投入最大
  - 示例：正在开发软件、提供服务、进行施工等

- **暂停 (On Hold/Paused):**
  - 含义：项目由于某种原因暂时停止，但预计未来会恢复
  - 特点：非正常中断，但有恢复的可能性
  - 示例：客户临时要求暂停项目，等待后续通知

- **已完成 (Completed/Closed):**
  - 含义：项目的所有主要目标和交付物均已达成，并通过了验收
  - 特点：主要工作已结束，等待最终的结算、归档和总结
  - 示例：软件已交付并验收，等待最终付款

- **已取消/已终止 (Cancelled/Terminated):**
  - 含义：项目在完成之前，由于各种原因被正式取消或终止
  - 特点：非正常结束，项目不再有活动
  - 示例：客户单方面取消合同；项目评估后决定不再继续

---