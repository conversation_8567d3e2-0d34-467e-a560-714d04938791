# 合同管理模块优化说明

## 优化内容

### 1. 新增合同时的供应商选择功能

#### 功能描述
- 当合同类别选择为"采购合同"时，系统会显示供应商选择下拉框
- 供应商数据从供应商管理模块获取
- 支持搜索和筛选功能

#### 实现细节
- **动态表单字段**：根据合同类别（销售合同/采购合同）显示不同的选择字段
  - 销售合同：显示"客户名称"输入框
  - 采购合同：显示"供应商"下拉选择框
  - 其他类别：显示"合作方名称"输入框

- **供应商下拉框特性**：
  - 支持搜索：可按供应商名称或编号搜索
  - 显示格式：供应商名称 (供应商编号)
  - 异步加载：选择采购合同时自动加载供应商列表
  - 清空选项：支持清空选择

- **表单验证**：
  - 动态验证规则：根据合同类别动态调整必填字段
  - 采购合同时供应商为必选项

#### 技术实现
- 使用Vue 3 Composition API的computed属性实现动态表单验证
- 集成供应商Store获取供应商数据
- 使用v-if条件渲染实现字段切换

### 2. 合同管理表格字段动态配置功能

#### 功能描述
- 支持自定义显示哪些表格字段
- 支持拖拽调整字段显示顺序
- 支持调整字段宽度
- 配置信息持久化保存

#### 实现细节
- **字段配置界面**：
  - 模态框形式的配置界面
  - 直观的拖拽排序功能
  - 复选框控制字段显示/隐藏
  - 数字输入框调整字段宽度

- **配置功能**：
  - 重置默认：恢复系统默认字段配置
  - 保存模板：将当前配置保存为模板
  - 本地存储：配置自动保存到localStorage

- **约束条件**：
  - 操作列固定显示，不可隐藏
  - 字段宽度限制在80-500px之间

#### 技术实现
- 使用vuedraggable库实现拖拽功能
- 使用localStorage实现配置持久化
- 使用computed属性动态计算可见列

## 文件修改清单

### 主要修改文件
1. **src/views/contracts/Create.vue**
   - 添加供应商选择功能
   - 实现动态表单字段切换
   - 集成供应商Store

2. **src/views/contracts/List.vue**
   - 添加字段配置按钮
   - 实现字段配置弹框
   - 添加拖拽排序功能
   - 实现配置持久化

3. **src/types/contract.ts**
   - 更新合同接口定义
   - 添加供应商相关字段

### 新增依赖
- **vuedraggable@next**: 用于实现拖拽功能

## 使用说明

### 新增采购合同
1. 进入合同管理页面
2. 点击"新增合同"按钮
3. 选择合同类别为"采购合同"
4. 在供应商下拉框中选择对应的供应商
5. 填写其他必要信息并保存

### 配置表格字段
1. 进入合同管理列表页面
2. 点击"字段配置"按钮
3. 在弹出的配置界面中：
   - 拖拽调整字段顺序
   - 勾选/取消勾选控制字段显示
   - 调整字段宽度
4. 点击"确定"保存配置

### 重置配置
- 在字段配置界面点击"重置默认"按钮
- 或点击"保存模板"保存当前配置为模板

## 技术特点

1. **响应式设计**：适配不同屏幕尺寸
2. **用户体验**：直观的拖拽操作和实时预览
3. **数据持久化**：配置自动保存，刷新页面不丢失
4. **类型安全**：完整的TypeScript类型定义
5. **模块化设计**：功能独立，易于维护和扩展

## 注意事项

1. 供应商数据依赖供应商管理模块，需确保供应商模块正常运行
2. 字段配置保存在浏览器本地存储中，清除浏览器数据会丢失配置
3. 拖拽功能需要现代浏览器支持
4. 表格字段配置仅影响当前用户，不同用户的配置相互独立 