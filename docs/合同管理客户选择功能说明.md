# 合同管理客户选择功能说明

## 功能概述

在合同管理模块的新增合同功能中，实现了根据合同类别动态显示不同的选择字段：
- **销售合同**：显示客户选择下拉框（从客户管理中选择）
- **采购合同**：显示供应商选择下拉框（从供应商管理中选择）
- **其他类型**：显示合作方名称输入框

## 主要功能

### 1. 客户选择功能（销售合同）
- 从客户管理模块加载所有客户数据
- 下拉框显示格式：`客户名称 (客户编码)`
- 支持搜索客户名称和编码
- 支持清空选择
- 可直接新增客户

### 2. 供应商选择功能（采购合同）
- 从供应商管理模块加载所有供应商数据
- 下拉框显示格式：`供应商名称 (供应商编码)`
- 支持搜索供应商名称和编码
- 支持清空选择

### 3. 动态字段切换
- 根据选择的合同类别自动切换显示字段
- 切换时会清空之前选择的值

## 使用方法

### 新增销售合同
1. 访问合同管理页面：`http://192.168.31.130:3002/contracts`
2. 点击"新增合同"按钮
3. 选择"合同类别"为"销售合同"
4. 在"客户名称"字段中：
   - 点击下拉框选择已有客户
   - 或使用搜索功能快速查找客户
   - 或点击"新增客户"按钮添加新客户

### 新增采购合同
1. 访问合同管理页面
2. 点击"新增合同"按钮
3. 选择"合同类别"为"采购合同"
4. 在"供应商"字段中：
   - 点击下拉框选择已有供应商
   - 或使用搜索功能快速查找供应商

## 技术实现

### 前端实现
- **文件位置**：`src/views/contracts/List.vue`
- **数据源**：
  - 客户数据：`useCustomerStore().fetchCustomers()`
  - 供应商数据：`useSupplierStore().fetchSuppliers()`
- **搜索功能**：支持按名称和编码模糊搜索
- **数据加载**：页面初始化时预加载所有客户和供应商数据

### 数据结构
```typescript
// 合同表单数据
{
  category: 'sales' | 'procurement', // 合同类别
  customerId: string,                // 客户ID（销售合同）
  supplierId: string,                // 供应商ID（采购合同）
  partnerName: string,               // 合作方名称（其他）
  // ... 其他字段
}
```

### 关键代码片段
```vue
<!-- 根据合同类别动态显示字段 -->
<a-form-item 
  v-if="contractForm.category === 'sales'" 
  label="客户名称" 
  name="customerId"
>
  <a-input-group compact>
    <a-select 
      v-model:value="contractForm.customerId" 
      placeholder="请选择客户" 
      allowClear 
      show-search
      :filter-option="filterCustomerOption"
    >
      <a-select-option 
        v-for="customer in customerOptions" 
        :key="customer.id" 
        :value="customer.id"
      >
        {{ customer.name }} ({{ customer.code || customer.id }})
      </a-select-option>
    </a-select>
    <a-button @click="openCustomerModal">新增客户</a-button>
  </a-input-group>
</a-form-item>

<a-form-item 
  v-if="contractForm.category === 'procurement'" 
  label="供应商" 
  name="supplierId"
>
  <a-select 
    v-model:value="contractForm.supplierId" 
    placeholder="请选择供应商" 
    allowClear 
    show-search
    :filter-option="filterSupplierOption"
  >
    <a-select-option 
      v-for="supplier in supplierOptions" 
      :key="supplier.id" 
      :value="supplier.id"
    >
      {{ supplier.name }} ({{ supplier.code }})
    </a-select-option>
  </a-select>
</a-form-item>
```

## 注意事项

1. **数据预加载**：页面加载时会自动获取所有客户和供应商数据
2. **性能考虑**：当前设置为加载1000条记录，如果数据量更大需要考虑分页加载
3. **搜索功能**：支持中文和编码的模糊搜索
4. **表单验证**：选择客户或供应商为必填项
5. **数据同步**：新增客户后会自动刷新下拉选项

## 后续优化

1. **懒加载**：考虑实现下拉框的懒加载以提升性能
2. **缓存机制**：添加客户和供应商数据的缓存机制
3. **高级搜索**：支持按客户类型、行业等条件筛选
4. **批量操作**：支持批量选择多个客户或供应商（如果需要） 