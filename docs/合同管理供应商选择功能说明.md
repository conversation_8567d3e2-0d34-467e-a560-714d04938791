# 合同管理模块 - 供应商选择功能说明

## 📋 功能概述

在合同管理模块的新增合同功能中，当用户选择"采购合同"类别时，系统会自动显示供应商选择字段，用户可以从供应商管理模块中选择相应的供应商。

## 🎯 功能特性

### 1. 动态字段显示
- **智能切换**：根据合同类别自动显示对应的选择字段
  - `销售合同` → 显示"客户名称"输入框
  - `采购合同` → 显示"供应商"选择框
  - `其他类型` → 显示"合作方名称"输入框

### 2. 供应商数据集成
- **数据来源**：直接从供应商管理模块获取数据
- **实时加载**：选择采购合同时自动加载供应商列表
- **数据格式**：显示"供应商名称 (供应商编号)"的格式

### 3. 智能搜索和过滤
- **搜索支持**：支持按供应商名称和编号搜索
- **模糊匹配**：不区分大小写的模糊搜索
- **实时过滤**：输入时实时过滤结果

### 4. 表单验证
- **动态验证**：根据合同类别动态调整验证规则
- **必填校验**：选择采购合同时，供应商字段为必填
- **友好提示**：提供清晰的错误提示信息

## 🔧 技术实现

### 1. 数据结构
```typescript
// 表单数据结构
const formData = {
  category: 'procurement', // 合同类别
  supplierId: 'V2024001', // 供应商ID
  // ... 其他字段
}

// 合同类型定义
interface Contract {
  supplierId?: string;     // 供应商ID
  supplierName?: string;   // 供应商名称
  category: 'sales' | 'procurement'; // 合同类别
  // ... 其他字段
}
```

### 2. 核心功能代码

#### 动态字段渲染
```vue
<a-form-item 
  v-if="formData.category === 'procurement'" 
  label="供应商" 
  name="supplierId"
>
  <a-select 
    v-model:value="formData.supplierId" 
    placeholder="请选择供应商" 
    allowClear 
    show-search
    :filter-option="filterSupplierOption"
    :loading="loadingSuppliers"
  >
    <a-select-option 
      v-for="supplier in supplierOptions" 
      :key="supplier.id" 
      :value="supplier.id"
    >
      {{ supplier.name }} ({{ supplier.code }})
    </a-select-option>
  </a-select>
</a-form-item>
```

#### 合同类别变化处理
```javascript
const handleCategoryChange = (category: string) => {
  // 清空相关字段
  formData.customerName = '';
  formData.supplierId = undefined;
  formData.partnerName = '';
  
  // 如果选择采购合同，加载供应商列表
  if (category === 'procurement') {
    loadSupplierOptions();
  }
};
```

#### 供应商数据加载
```javascript
const loadSupplierOptions = async () => {
  try {
    loadingSuppliers.value = true;
    const result = await supplierStore.fetchSuppliers({
      current: 1,
      pageSize: 1000 // 获取所有供应商
    });
    supplierOptions.value = result.data;
  } catch (error) {
    console.error('加载供应商列表失败:', error);
    message.error('加载供应商列表失败');
  } finally {
    loadingSuppliers.value = false;
  }
};
```

#### 动态表单验证
```javascript
const formRules = computed(() => {
  const baseRules = {
    // ... 基础验证规则
  };

  // 根据合同类别添加相应的验证规则
  if (formData.category === 'procurement') {
    baseRules.supplierId = [
      { required: true, message: '请选择供应商', trigger: 'change' }
    ];
  }

  return baseRules;
});
```

## 🎨 用户界面

### 1. 界面布局
- **响应式设计**：表单采用栅格布局，适配不同屏幕尺寸
- **清晰分组**：合同基本信息统一分组显示
- **视觉一致**：与其他表单字段保持一致的样式

### 2. 交互体验
- **流畅切换**：合同类别切换时平滑显示/隐藏字段
- **加载状态**：供应商加载时显示loading状态
- **错误处理**：网络错误时显示友好提示

### 3. 可访问性
- **键盘导航**：支持Tab键导航
- **屏幕阅读器**：提供适当的aria标签
- **颜色对比**：确保足够的颜色对比度

## 📱 使用流程

### 1. 创建采购合同
1. 访问合同管理 → 点击"新增合同"
2. 填写合同名称和编号
3. **选择合同类别为"采购合同"**
4. 系统自动显示供应商选择字段
5. 从下拉列表中选择供应商
6. 继续填写其他必要信息
7. 保存合同

### 2. 供应商搜索
1. 在供应商选择框中输入关键词
2. 系统自动过滤匹配的供应商
3. 支持按名称或编号搜索
4. 选择合适的供应商

## 🔍 测试验证

### 1. 功能测试
- ✅ 合同类别切换正常
- ✅ 供应商数据加载正常
- ✅ 搜索过滤功能正常
- ✅ 表单验证规则正确
- ✅ 数据保存功能正常

### 2. 兼容性测试
- ✅ Chrome浏览器兼容
- ✅ Firefox浏览器兼容
- ✅ Safari浏览器兼容
- ✅ 移动端响应式布局

### 3. 性能测试
- ✅ 供应商列表加载性能良好
- ✅ 搜索过滤响应及时
- ✅ 大量数据下表现稳定

## 📈 后续优化

### 1. 功能增强
- [ ] 支持供应商快速新增
- [ ] 添加供应商详情预览
- [ ] 支持多供应商选择
- [ ] 添加供应商评级显示

### 2. 性能优化
- [ ] 实现供应商数据懒加载
- [ ] 添加本地缓存机制
- [ ] 优化搜索算法

### 3. 用户体验
- [ ] 添加最近使用的供应商
- [ ] 支持供应商收藏功能
- [ ] 优化移动端交互

## 📝 注意事项

1. **数据依赖**：此功能依赖供应商管理模块的数据
2. **权限控制**：需要有供应商查看权限才能加载数据
3. **网络异常**：网络异常时会显示错误提示，请检查网络连接
4. **数据同步**：供应商信息更新后，需要刷新页面获取最新数据

## 🎯 总结

供应商选择功能成功集成到合同管理模块中，实现了：
- **智能化**：根据合同类别自动显示相应字段
- **集成化**：与供应商管理模块无缝集成
- **用户友好**：提供搜索、过滤、验证等完整功能
- **可扩展**：为后续功能扩展预留了接口

该功能大大提升了采购合同创建的效率和准确性，为用户提供了更好的操作体验。 