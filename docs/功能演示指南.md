# 功能演示指南

## 系统访问

**开发服务器地址**: http://localhost:3004 或 http://**************:3004

## 新增功能演示

### 1. 合同管理模块优化

#### 供应商选择功能
1. 访问合同管理页面：`/contracts`
2. 点击"新增合同"按钮
3. 在"合同类别"中选择"采购合同"
4. 观察"供应商"下拉框自动出现
5. 下拉框支持搜索供应商名称和编号
6. 选择供应商后可以继续填写其他信息

#### 表格字段配置功能
1. 在合同列表页面点击"字段配置"按钮
2. 在弹出的配置界面中：
   - 拖拽调整字段顺序
   - 勾选/取消勾选控制字段显示
   - 调整字段宽度（80-500px）
3. 点击"确定"保存配置
4. 观察表格实时更新显示效果
5. 刷新页面验证配置持久化

### 2. 客户管理模块字段配置

1. 访问客户管理页面：`/customer`
2. 点击"字段配置"按钮
3. 尝试以下操作：
   - 隐藏"纳税人识别号"字段
   - 调整"客户名称"列宽度
   - 拖拽"联系方式"到第二位
4. 保存配置并验证效果
5. 点击"重置默认"恢复初始配置

### 3. 供应商管理模块字段配置

1. 访问供应商管理页面：`/supplier`
2. 点击"字段配置"按钮
3. 测试配置功能：
   - 隐藏"所属行业"字段
   - 调整"供应商名称"列宽度
   - 重新排列字段顺序
4. 使用"保存模板"功能
5. 验证配置在页面刷新后保持

### 4. 项目管理模块字段配置

1. 访问项目管理页面：`/projects`
2. 点击"字段配置"按钮
3. 演示完整配置流程：
   - 自定义字段显示
   - 调整字段顺序和宽度
   - 保存并验证配置效果

## 功能特色展示

### 1. 动态表单字段切换
- 在合同新增页面演示根据合同类别动态显示不同字段
- 展示表单验证规则的动态调整

### 2. 拖拽交互体验
- 演示流畅的拖拽操作
- 展示拖拽过程中的视觉反馈

### 3. 配置持久化
- 演示配置保存后的持久性
- 展示不同模块配置的独立性

### 4. 响应式设计
- 在不同屏幕尺寸下测试界面适配
- 展示移动端友好的交互设计

## 技术亮点

### 1. 统一的用户体验
- 四个模块采用完全一致的配置界面
- 统一的交互逻辑和视觉设计
- 一致的数据存储和管理机制

### 2. 高性能实现
- 使用Vue 3 Composition API优化性能
- 计算属性实现高效的响应式更新
- 最小化DOM操作提升用户体验

### 3. 可扩展架构
- 模块化的代码结构便于维护
- 通用的配置组件可复用到其他模块
- 灵活的存储机制支持功能扩展

## 测试场景

### 1. 基础功能测试
- [ ] 字段显示/隐藏切换
- [ ] 字段顺序拖拽调整
- [ ] 字段宽度数值设置
- [ ] 配置保存和加载
- [ ] 默认配置重置

### 2. 交互体验测试
- [ ] 拖拽操作的流畅性
- [ ] 界面响应的及时性
- [ ] 错误处理的友好性
- [ ] 移动端适配效果

### 3. 数据持久化测试
- [ ] 页面刷新后配置保持
- [ ] 浏览器重启后配置保持
- [ ] 不同模块配置独立性
- [ ] 配置数据格式兼容性

### 4. 边界情况测试
- [ ] localStorage不可用时的降级
- [ ] 配置数据损坏时的恢复
- [ ] 极端宽度值的处理
- [ ] 所有字段隐藏的防护

## 用户操作指南

### 快速上手
1. 进入任意管理模块列表页面
2. 点击左上角"字段配置"按钮
3. 在配置界面中自定义表格显示
4. 点击"确定"保存配置

### 高级功能
1. **拖拽排序**: 按住字段项左侧的拖拽图标进行拖拽
2. **精确宽度**: 在宽度输入框中输入具体数值
3. **批量操作**: 快速勾选/取消多个字段
4. **模板管理**: 使用"保存模板"和"重置默认"功能

### 最佳实践
1. **合理隐藏**: 根据实际业务需要隐藏不常用字段
2. **适当宽度**: 根据内容长度设置合适的列宽
3. **逻辑排序**: 按照业务流程逻辑排列字段顺序
4. **定期优化**: 根据使用习惯定期调整配置

## 故障排除

### 常见问题
1. **配置不生效**: 检查浏览器是否支持localStorage
2. **拖拽不响应**: 确认浏览器支持拖拽API
3. **页面显示异常**: 尝试重置为默认配置
4. **配置丢失**: 检查浏览器是否清除了本地数据

### 解决方案
1. **清除缓存**: 清除浏览器缓存和本地存储
2. **重置配置**: 使用"重置默认"功能恢复初始状态
3. **更新浏览器**: 使用最新版本的现代浏览器
4. **检查控制台**: 查看浏览器控制台的错误信息

## 反馈渠道

如果在使用过程中遇到问题或有改进建议，请通过以下方式反馈：

1. **功能建议**: 在系统中提交功能改进建议
2. **Bug报告**: 详细描述问题现象和复现步骤
3. **用户体验**: 分享使用感受和优化建议
4. **性能反馈**: 报告性能问题和改进需求 