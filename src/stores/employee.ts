import { defineStore } from 'pinia';
import { reactive, ref, computed } from 'vue';

// 员工状态枚举
export enum EmployeeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ONBOARDING = 'onboarding',
  RESIGNED = 'resigned',
}

// 薪酬调整状态枚举
export enum SalaryAdjustmentStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

// 薪酬发放状态枚举
export enum SalaryPaymentStatus {
  UNPAID = 'unpaid',
  PAID = 'paid',
}

// 员工部门枚举
export enum Department {
  RD = 'rd',
  SALES = 'sales',
  MARKETING = 'marketing',
  OPERATIONS = 'operations',
  FINANCE = 'finance',
  HR = 'hr',
  ADMIN = 'admin',
}

// 员工职位枚举
export enum Position {
  STAFF = 'staff',
  LEAD = 'lead',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  VP = 'vp',
}

// 员工接口
export interface Employee {
  id: string;
  name: string;
  code: string;
  departmentId: Department;
  positionId: Position;
  hireDate: string;
  status: EmployeeStatus;
  contactNumber: string;
  email: string;
  contractId?: string;
  contractFile?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 薪酬调整接口
export interface SalaryAdjustment {
  id: string;
  employeeId: string;
  employeeName: string;
  amount: number;
  reason: string;
  effectiveDate: string;
  status: SalaryAdjustmentStatus;
  createdAt: string;
  updatedAt: string;
}

// 薪酬发放记录接口
export interface SalaryPayment {
  id: string;
  employeeId: string;
  employeeName: string;
  amount: number;
  paymentDate: string;
  status: SalaryPaymentStatus;
  notes?: string;
  createdAt: string;
}

// 员工离职接口
export interface Resignation {
  id: string;
  employeeId: string;
  resignationDate: string;
  reason: string;
  attachmentFile?: string;
  createdAt: string;
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 生成员工工号
const generateEmployeeCode = (department: Department) => {
  const prefix = department.toUpperCase();
  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  const dateCode = new Date().getFullYear().toString().substr(2, 2);
  return `${prefix}${dateCode}${randomNum}`;
};

// 获取部门名称
export const getDepartmentName = (departmentId: Department) => {
  const departmentMap = {
    [Department.RD]: '研发部',
    [Department.SALES]: '销售部',
    [Department.MARKETING]: '市场部',
    [Department.OPERATIONS]: '运营部',
    [Department.FINANCE]: '财务部',
    [Department.HR]: '人力资源部',
    [Department.ADMIN]: '行政部',
  };
  return departmentMap[departmentId] || '未知部门';
};

// 获取职位名称
export const getPositionName = (positionId: Position) => {
  const positionMap = {
    [Position.STAFF]: '员工',
    [Position.LEAD]: '主管',
    [Position.MANAGER]: '经理',
    [Position.DIRECTOR]: '总监',
    [Position.VP]: '副总裁',
  };
  return positionMap[positionId] || '未知职位';
};

// 获取员工状态名称和颜色
export const getEmployeeStatusInfo = (status: EmployeeStatus) => {
  const statusMap = {
    [EmployeeStatus.ACTIVE]: { name: '在职', color: 'green' },
    [EmployeeStatus.INACTIVE]: { name: '未启用', color: 'orange' },
    [EmployeeStatus.ONBOARDING]: { name: '入职中', color: 'blue' },
    [EmployeeStatus.RESIGNED]: { name: '离职', color: 'red' },
  };
  return statusMap[status] || { name: '未知状态', color: 'default' };
};

// 获取薪酬调整状态名称和颜色
export const getSalaryAdjustmentStatusInfo = (status: SalaryAdjustmentStatus) => {
  const statusMap = {
    [SalaryAdjustmentStatus.DRAFT]: { name: '草稿', color: 'default' },
    [SalaryAdjustmentStatus.PENDING]: { name: '审批中', color: 'processing' },
    [SalaryAdjustmentStatus.APPROVED]: { name: '已批准', color: 'success' },
    [SalaryAdjustmentStatus.REJECTED]: { name: '已拒绝', color: 'error' },
  };
  return statusMap[status] || { name: '未知状态', color: 'default' };
};

// 获取薪酬发放状态名称和颜色
export const getSalaryPaymentStatusInfo = (status: SalaryPaymentStatus) => {
  const statusMap = {
    [SalaryPaymentStatus.UNPAID]: { name: '未发放', color: 'warning' },
    [SalaryPaymentStatus.PAID]: { name: '已发放', color: 'success' },
  };
  return statusMap[status] || { name: '未知状态', color: 'default' };
};

export const useEmployeeStore = defineStore('employee', () => {
  // 状态
  const employees = ref<Employee[]>([]);
  const salaryAdjustments = ref<SalaryAdjustment[]>([]);
  const salaryPayments = ref<SalaryPayment[]>([]);
  const resignations = ref<Resignation[]>([]);
  const loading = ref(false);

  // 当前选择的员工ID
  const selectedEmployeeId = ref<string | null>(null);

  // 新建员工表单
  const newEmployeeForm = reactive({
    name: '',
    departmentId: Department.RD,
    positionId: Position.STAFF,
    hireDate: '',
    contactNumber: '',
    email: '',
    contractId: '',
    contractFile: '',
    notes: '',
    currentStep: 0, // 入职步骤
  });

  // 获取员工列表
  const fetchEmployees = async () => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 如果没有数据，生成模拟数据
      if (employees.value.length === 0) {
        employees.value = generateMockEmployees();
      }
    } catch (error) {
      console.error('获取员工列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 获取员工详情
  const fetchEmployeeById = async (id: string) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const employee = employees.value.find(e => e.id === id);
      selectedEmployeeId.value = id;
      return employee;
    } catch (error) {
      console.error('获取员工详情失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 创建员工
  const createEmployee = async (formData: Partial<Employee>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = new Date().toISOString();
      const department = formData.departmentId || Department.RD;
      
      const newEmployee: Employee = {
        id: generateId(),
        name: formData.name || '',
        code: generateEmployeeCode(department),
        departmentId: department,
        positionId: formData.positionId || Position.STAFF,
        hireDate: formData.hireDate || now.split('T')[0],
        status: EmployeeStatus.ONBOARDING,
        contactNumber: formData.contactNumber || '',
        email: formData.email || '',
        contractId: formData.contractId,
        contractFile: formData.contractFile,
        notes: formData.notes,
        createdAt: now,
        updatedAt: now,
      };
      
      employees.value.push(newEmployee);
      return newEmployee;
    } catch (error) {
      console.error('创建员工失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 更新员工信息
  const updateEmployee = async (id: string, formData: Partial<Employee>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = employees.value.findIndex(e => e.id === id);
      if (index !== -1) {
        const now = new Date().toISOString();
        employees.value[index] = {
          ...employees.value[index],
          ...formData,
          updatedAt: now,
        };
        return employees.value[index];
      }
      return null;
    } catch (error) {
      console.error('更新员工信息失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 员工离职
  const resignEmployee = async (employeeId: string, resignationData: Omit<Resignation, 'id' | 'employeeId' | 'createdAt'>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = new Date().toISOString();
      
      // 更新员工状态为离职
      const index = employees.value.findIndex(e => e.id === employeeId);
      if (index !== -1) {
        employees.value[index].status = EmployeeStatus.RESIGNED;
        employees.value[index].updatedAt = now;
        
        // 记录离职信息
        const resignation: Resignation = {
          id: generateId(),
          employeeId,
          resignationDate: resignationData.resignationDate,
          reason: resignationData.reason,
          attachmentFile: resignationData.attachmentFile,
          createdAt: now,
        };
        
        resignations.value.push(resignation);
        return { employee: employees.value[index], resignation };
      }
      return null;
    } catch (error) {
      console.error('员工离职处理失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 创建薪酬调整申请
  const createSalaryAdjustment = async (adjustmentData: Omit<SalaryAdjustment, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = new Date().toISOString();
      
      // 查找员工名称
      const employee = employees.value.find(e => e.id === adjustmentData.employeeId);
      if (!employee) return null;
      
      const newAdjustment: SalaryAdjustment = {
        id: generateId(),
        employeeId: adjustmentData.employeeId,
        employeeName: employee.name,
        amount: adjustmentData.amount,
        reason: adjustmentData.reason,
        effectiveDate: adjustmentData.effectiveDate,
        status: SalaryAdjustmentStatus.PENDING,
        createdAt: now,
        updatedAt: now,
      };
      
      salaryAdjustments.value.push(newAdjustment);
      return newAdjustment;
    } catch (error) {
      console.error('创建薪酬调整申请失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 更新薪酬调整状态
  const updateSalaryAdjustmentStatus = async (id: string, status: SalaryAdjustmentStatus) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = salaryAdjustments.value.findIndex(adj => adj.id === id);
      if (index !== -1) {
        const now = new Date().toISOString();
        salaryAdjustments.value[index].status = status;
        salaryAdjustments.value[index].updatedAt = now;
        return salaryAdjustments.value[index];
      }
      return null;
    } catch (error) {
      console.error('更新薪酬调整状态失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 创建薪酬发放记录
  const createSalaryPayment = async (paymentData: Omit<SalaryPayment, 'id' | 'createdAt'>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = new Date().toISOString();
      
      // 查找员工名称
      const employee = employees.value.find(e => e.id === paymentData.employeeId);
      if (!employee) return null;
      
      const newPayment: SalaryPayment = {
        id: generateId(),
        employeeId: paymentData.employeeId,
        employeeName: employee.name,
        amount: paymentData.amount,
        paymentDate: paymentData.paymentDate,
        status: paymentData.status || SalaryPaymentStatus.UNPAID,
        notes: paymentData.notes,
        createdAt: now,
      };
      
      salaryPayments.value.push(newPayment);
      return newPayment;
    } catch (error) {
      console.error('创建薪酬发放记录失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 批量更新薪酬发放状态
  const batchUpdatePaymentStatus = async (ids: string[], status: SalaryPaymentStatus) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedPayments = [];
      
      for (const id of ids) {
        const index = salaryPayments.value.findIndex(payment => payment.id === id);
        if (index !== -1) {
          salaryPayments.value[index].status = status;
          updatedPayments.push(salaryPayments.value[index]);
        }
      }
      
      return updatedPayments;
    } catch (error) {
      console.error('批量更新薪酬发放状态失败:', error);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 计算属性：按部门分组的员工统计
  const employeesByDepartment = computed(() => {
    const result: Record<Department, Employee[]> = {
      [Department.RD]: [],
      [Department.SALES]: [],
      [Department.MARKETING]: [],
      [Department.OPERATIONS]: [],
      [Department.FINANCE]: [],
      [Department.HR]: [],
      [Department.ADMIN]: [],
    };
    
    employees.value.forEach(employee => {
      if (result[employee.departmentId]) {
        result[employee.departmentId].push(employee);
      }
    });
    
    return result;
  });

  // 计算属性：在职员工数量
  const activeEmployeesCount = computed(() => {
    return employees.value.filter(e => e.status === EmployeeStatus.ACTIVE).length;
  });

  // 计算属性：入职中员工数量
  const onboardingEmployeesCount = computed(() => {
    return employees.value.filter(e => e.status === EmployeeStatus.ONBOARDING).length;
  });

  // 计算属性：离职员工数量
  const resignedEmployeesCount = computed(() => {
    return employees.value.filter(e => e.status === EmployeeStatus.RESIGNED).length;
  });

  // 生成模拟员工数据
  const generateMockEmployees = (): Employee[] => {
    const mockEmployees: Employee[] = [];
    const departments = Object.values(Department);
    const positions = Object.values(Position);
    const statuses = [EmployeeStatus.ACTIVE, EmployeeStatus.ACTIVE, EmployeeStatus.ACTIVE, EmployeeStatus.ONBOARDING, EmployeeStatus.RESIGNED];
    
    for (let i = 0; i < 35; i++) {
      const departmentId = departments[Math.floor(Math.random() * departments.length)];
      const now = new Date().toISOString();
      const hireDate = new Date(Date.now() - Math.floor(Math.random() * 1000 * 3600 * 24 * 365 * 3)).toISOString().split('T')[0];
      
      mockEmployees.push({
        id: generateId(),
        name: ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二'][Math.floor(Math.random() * 10)] + i,
        code: generateEmployeeCode(departmentId),
        departmentId,
        positionId: positions[Math.floor(Math.random() * positions.length)],
        hireDate,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        contactNumber: `1${Math.floor(Math.random() * 9) + 1}${Math.floor(Math.random() * 10000000000).toString().padStart(9, '0')}`,
        email: `employee${i}@example.com`,
        contractId: `CONTRACT-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        notes: `这是员工${i}的备注信息`,
        createdAt: now,
        updatedAt: now,
      });
    }
    
    return mockEmployees;
  };

  // 生成模拟薪酬调整数据
  const generateMockSalaryAdjustments = (): SalaryAdjustment[] => {
    const mockAdjustments: SalaryAdjustment[] = [];
    const statuses = Object.values(SalaryAdjustmentStatus);
    
    for (let i = 0; i < 20; i++) {
      const employee = employees.value[Math.floor(Math.random() * employees.value.length)];
      if (!employee) continue;
      
      const now = new Date().toISOString();
      const effectiveDate = new Date(Date.now() + Math.floor(Math.random() * 1000 * 3600 * 24 * 30)).toISOString().split('T')[0];
      
      mockAdjustments.push({
        id: generateId(),
        employeeId: employee.id,
        employeeName: employee.name,
        amount: Math.floor(Math.random() * 5000) + 1000,
        reason: ['绩效考核', '职位晋升', '年度调薪', '特殊贡献', '市场调整'][Math.floor(Math.random() * 5)],
        effectiveDate,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        createdAt: now,
        updatedAt: now,
      });
    }
    
    return mockAdjustments;
  };

  // 生成模拟薪酬发放记录
  const generateMockSalaryPayments = (): SalaryPayment[] => {
    const mockPayments: SalaryPayment[] = [];
    const statuses = Object.values(SalaryPaymentStatus);
    
    for (let i = 0; i < 50; i++) {
      const employee = employees.value[Math.floor(Math.random() * employees.value.length)];
      if (!employee) continue;
      
      const now = new Date().toISOString();
      const paymentDate = new Date(Date.now() - Math.floor(Math.random() * 1000 * 3600 * 24 * 180)).toISOString().split('T')[0];
      
      mockPayments.push({
        id: generateId(),
        employeeId: employee.id,
        employeeName: employee.name,
        amount: Math.floor(Math.random() * 10000) + 5000,
        paymentDate,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        notes: i % 3 === 0 ? '包含绩效奖金' : undefined,
        createdAt: now,
      });
    }
    
    return mockPayments;
  };

  // 重置新建员工表单
  const resetNewEmployeeForm = () => {
    Object.assign(newEmployeeForm, {
      name: '',
      departmentId: Department.RD,
      positionId: Position.STAFF,
      hireDate: '',
      contactNumber: '',
      email: '',
      contractId: '',
      contractFile: '',
      notes: '',
      currentStep: 0,
    });
  };

  // 初始化方法
  const initialize = async () => {
    await fetchEmployees();
    
    // 如果没有薪酬调整数据，生成模拟数据
    if (salaryAdjustments.value.length === 0) {
      salaryAdjustments.value = generateMockSalaryAdjustments();
    }
    
    // 如果没有薪酬发放记录，生成模拟数据
    if (salaryPayments.value.length === 0) {
      salaryPayments.value = generateMockSalaryPayments();
    }
  };

  return {
    // 状态
    employees,
    salaryAdjustments,
    salaryPayments,
    resignations,
    loading,
    selectedEmployeeId,
    newEmployeeForm,
    
    // 计算属性
    employeesByDepartment,
    activeEmployeesCount,
    onboardingEmployeesCount,
    resignedEmployeesCount,
    
    // 方法
    fetchEmployees,
    fetchEmployeeById,
    createEmployee,
    updateEmployee,
    resignEmployee,
    createSalaryAdjustment,
    updateSalaryAdjustmentStatus,
    createSalaryPayment,
    batchUpdatePaymentStatus,
    resetNewEmployeeForm,
    initialize,
  };
}); 