import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { Department } from './employee';

// 考勤类型枚举
export enum AttendanceType {
  NORMAL = 'normal',
  LEAVE = 'leave',
  OVERTIME = 'overtime',
  BUSINESS_TRIP = 'business_trip',
  LATE = 'late',
  EARLY_LEAVE = 'early_leave',
  ABSENT = 'absent'
}

// 请假类型枚举
export enum LeaveType {
  ANNUAL = 'annual',
  SICK = 'sick',
  PERSONAL = 'personal',
  MARRIAGE = 'marriage',
  MATERNITY = 'maternity',
  PATERNITY = 'paternity',
  BEREAVEMENT = 'bereavement',
  UNPAID = 'unpaid'
}

// 请假审批状态枚举
export enum LeaveStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

// 考勤记录接口
export interface AttendanceRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  type: AttendanceType;
  hours: number;
  projectId?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 请假申请接口
export interface LeaveApplication {
  id: string;
  employeeId: string;
  employeeName: string;
  leaveType: LeaveType;
  startDate: string;
  endDate: string;
  days: number;
  reason: string;
  status: LeaveStatus;
  projectConflicts?: ProjectConflict[];
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

// 项目冲突接口
export interface ProjectConflict {
  projectId: string;
  projectName: string;
  impactedHours: number;
}

// 假期余额接口
export interface LeaveBalance {
  employeeId: string;
  annual: number;
  sick: number;
  personal: number;
  other: number;
  updatedAt: string;
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 获取考勤类型名称和颜色
export const getAttendanceTypeInfo = (type: AttendanceType) => {
  const typeMap = {
    [AttendanceType.NORMAL]: { name: '正常出勤', color: 'green' },
    [AttendanceType.LEAVE]: { name: '请假', color: 'orange' },
    [AttendanceType.OVERTIME]: { name: '加班', color: 'blue' },
    [AttendanceType.BUSINESS_TRIP]: { name: '出差', color: 'purple' },
    [AttendanceType.LATE]: { name: '迟到', color: 'red' },
    [AttendanceType.EARLY_LEAVE]: { name: '早退', color: 'red' },
    [AttendanceType.ABSENT]: { name: '旷工', color: 'red' }
  };
  return typeMap[type] || { name: '未知', color: 'default' };
};

// 获取请假类型名称和颜色
export const getLeaveTypeInfo = (type: LeaveType) => {
  const typeMap = {
    [LeaveType.ANNUAL]: { name: '年假', color: 'green' },
    [LeaveType.SICK]: { name: '病假', color: 'orange' },
    [LeaveType.PERSONAL]: { name: '事假', color: 'purple' },
    [LeaveType.MARRIAGE]: { name: '婚假', color: 'pink' },
    [LeaveType.MATERNITY]: { name: '产假', color: 'magenta' },
    [LeaveType.PATERNITY]: { name: '陪产假', color: 'magenta' },
    [LeaveType.BEREAVEMENT]: { name: '丧假', color: 'grey' },
    [LeaveType.UNPAID]: { name: '无薪假', color: 'red' }
  };
  return typeMap[type] || { name: '未知', color: 'default' };
};

// 获取请假审批状态名称和颜色
export const getLeaveStatusInfo = (status: LeaveStatus) => {
  const statusMap = {
    [LeaveStatus.DRAFT]: { name: '草稿', color: 'default' },
    [LeaveStatus.PENDING]: { name: '审批中', color: 'processing' },
    [LeaveStatus.APPROVED]: { name: '已批准', color: 'success' },
    [LeaveStatus.REJECTED]: { name: '已拒绝', color: 'error' },
    [LeaveStatus.CANCELLED]: { name: '已取消', color: 'default' }
  };
  return statusMap[status] || { name: '未知状态', color: 'default' };
};

export const useAttendanceStore = defineStore('attendance', () => {
  // 状态
  const attendanceRecords = ref<AttendanceRecord[]>([]);
  const leaveApplications = ref<LeaveApplication[]>([]);
  const leaveBalances = ref<LeaveBalance[]>([]);
  const loading = ref(false);

  // 获取员工考勤记录
  const fetchAttendanceRecords = async (employeeId?: string, startDate?: string, endDate?: string) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 如果没有数据，生成模拟数据
      if (attendanceRecords.value.length === 0) {
        attendanceRecords.value = generateMockAttendanceRecords();
      }
      
      // 根据条件筛选
      let filteredRecords = [...attendanceRecords.value];
      
      if (employeeId) {
        filteredRecords = filteredRecords.filter(record => record.employeeId === employeeId);
      }
      
      if (startDate) {
        filteredRecords = filteredRecords.filter(record => record.date >= startDate);
      }
      
      if (endDate) {
        filteredRecords = filteredRecords.filter(record => record.date <= endDate);
      }
      
      return filteredRecords;
    } catch (error) {
      console.error('获取考勤记录失败:', error);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 创建考勤记录
  const createAttendanceRecord = async (recordData: Omit<AttendanceRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const now = new Date().toISOString();
      
      const newRecord: AttendanceRecord = {
        id: generateId(),
        employeeId: recordData.employeeId,
        employeeName: recordData.employeeName,
        date: recordData.date,
        type: recordData.type,
        hours: recordData.hours,
        projectId: recordData.projectId,
        notes: recordData.notes,
        createdAt: now,
        updatedAt: now
      };
      
      attendanceRecords.value.push(newRecord);
      return newRecord;
    } catch (error) {
      console.error('创建考勤记录失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 批量导入考勤记录
  const batchImportAttendanceRecords = async (records: Omit<AttendanceRecord, 'id' | 'createdAt' | 'updatedAt'>[]) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const now = new Date().toISOString();
      const newRecords: AttendanceRecord[] = [];
      
      for (const record of records) {
        const newRecord: AttendanceRecord = {
          id: generateId(),
          employeeId: record.employeeId,
          employeeName: record.employeeName,
          date: record.date,
          type: record.type,
          hours: record.hours,
          projectId: record.projectId,
          notes: record.notes,
          createdAt: now,
          updatedAt: now
        };
        
        newRecords.push(newRecord);
      }
      
      attendanceRecords.value.push(...newRecords);
      return newRecords;
    } catch (error) {
      console.error('批量导入考勤记录失败:', error);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 获取员工请假申请
  const fetchLeaveApplications = async (employeeId?: string, status?: LeaveStatus) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 如果没有数据，生成模拟数据
      if (leaveApplications.value.length === 0) {
        leaveApplications.value = generateMockLeaveApplications();
      }
      
      // 根据条件筛选
      let filteredApplications = [...leaveApplications.value];
      
      if (employeeId) {
        filteredApplications = filteredApplications.filter(app => app.employeeId === employeeId);
      }
      
      if (status) {
        filteredApplications = filteredApplications.filter(app => app.status === status);
      }
      
      return filteredApplications;
    } catch (error) {
      console.error('获取请假申请失败:', error);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 创建请假申请
  const createLeaveApplication = async (applicationData: Omit<LeaveApplication, 'id' | 'status' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = new Date().toISOString();
      
      const newApplication: LeaveApplication = {
        id: generateId(),
        employeeId: applicationData.employeeId,
        employeeName: applicationData.employeeName,
        leaveType: applicationData.leaveType,
        startDate: applicationData.startDate,
        endDate: applicationData.endDate,
        days: applicationData.days,
        reason: applicationData.reason,
        status: LeaveStatus.PENDING,
        projectConflicts: applicationData.projectConflicts,
        createdAt: now,
        updatedAt: now
      };
      
      leaveApplications.value.push(newApplication);
      return newApplication;
    } catch (error) {
      console.error('创建请假申请失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 更新请假申请状态
  const updateLeaveApplicationStatus = async (id: string, status: LeaveStatus, approverInfo?: { approvedBy: string, rejectionReason?: string }) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = leaveApplications.value.findIndex(app => app.id === id);
      if (index !== -1) {
        const now = new Date().toISOString();
        
        leaveApplications.value[index] = {
          ...leaveApplications.value[index],
          status,
          approvedBy: approverInfo?.approvedBy,
          approvedAt: status === LeaveStatus.APPROVED || status === LeaveStatus.REJECTED ? now : undefined,
          rejectionReason: approverInfo?.rejectionReason,
          updatedAt: now
        };
        
        // 如果是批准状态，更新员工的假期余额
        if (status === LeaveStatus.APPROVED) {
          await updateLeaveBalance(
            leaveApplications.value[index].employeeId,
            leaveApplications.value[index].leaveType,
            leaveApplications.value[index].days
          );
          
          // 创建相应的考勤记录
          await createLeaveAttendanceRecords(leaveApplications.value[index]);
        }
        
        return leaveApplications.value[index];
      }
      
      return null;
    } catch (error) {
      console.error('更新请假申请状态失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 获取员工假期余额
  const fetchLeaveBalance = async (employeeId: string) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 查找员工的假期余额
      let balance = leaveBalances.value.find(b => b.employeeId === employeeId);
      
      // 如果没有找到，则创建一个新的
      if (!balance) {
        const now = new Date().toISOString();
        balance = {
          employeeId,
          annual: 10, // 默认10天年假
          sick: 5, // 默认5天病假
          personal: 3, // 默认3天事假
          other: 0,
          updatedAt: now
        };
        
        leaveBalances.value.push(balance);
      }
      
      return balance;
    } catch (error) {
      console.error('获取假期余额失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 更新假期余额
  const updateLeaveBalance = async (employeeId: string, leaveType: LeaveType, days: number) => {
    // 获取员工的假期余额
    const balance = await fetchLeaveBalance(employeeId);
    if (!balance) return null;
    
    const now = new Date().toISOString();
    
    // 根据请假类型扣减相应的余额
    switch (leaveType) {
      case LeaveType.ANNUAL:
        balance.annual -= days;
        break;
      case LeaveType.SICK:
        balance.sick -= days;
        break;
      case LeaveType.PERSONAL:
        balance.personal -= days;
        break;
      default:
        balance.other -= days;
        break;
    }
    
    balance.updatedAt = now;
    
    return balance;
  };

  // 创建请假对应的考勤记录
  const createLeaveAttendanceRecords = async (leaveApplication: LeaveApplication) => {
    const startDate = new Date(leaveApplication.startDate);
    const endDate = new Date(leaveApplication.endDate);
    const recordsToCreate: Omit<AttendanceRecord, 'id' | 'createdAt' | 'updatedAt'>[] = [];
    
    // 循环生成每一天的考勤记录
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      // 跳过周末
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) continue;
      
      recordsToCreate.push({
        employeeId: leaveApplication.employeeId,
        employeeName: leaveApplication.employeeName,
        date: date.toISOString().split('T')[0],
        type: AttendanceType.LEAVE,
        hours: 8, // 默认8小时/天
        notes: `${getLeaveTypeInfo(leaveApplication.leaveType).name}: ${leaveApplication.reason}`
      });
    }
    
    // 批量创建考勤记录
    if (recordsToCreate.length > 0) {
      await batchImportAttendanceRecords(recordsToCreate);
    }
  };

  // 检查项目冲突
  const checkProjectConflicts = async (employeeId: string, startDate: string, endDate: string) => {
    loading.value = true;
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟从项目管理模块获取数据
      const conflicts: ProjectConflict[] = [
        {
          projectId: 'PRJ-' + Math.floor(Math.random() * 1000).toString().padStart(3, '0'),
          projectName: ['系统开发项目', '产品升级项目', '客户实施项目', '技术支持项目'][Math.floor(Math.random() * 4)],
          impactedHours: Math.floor(Math.random() * 20) + 4
        }
      ];
      
      return conflicts.length > 0 ? conflicts : [];
    } catch (error) {
      console.error('检查项目冲突失败:', error);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 计算属性：考勤统计（按部门）
  const attendanceStatsByDepartment = computed(() => {
    const stats: Record<string, { total: number, leave: number, overtime: number, absent: number }> = {};
    
    // 初始化各部门统计
    Object.values(Department).forEach(dept => {
      stats[dept] = { total: 0, leave: 0, overtime: 0, absent: 0 };
    });
    
    // 统计每个考勤记录
    attendanceRecords.value.forEach(record => {
      // 假设有一个方法从员工ID获取部门ID
      const deptId = 'rd'; // 这里需要实际实现
      
      if (stats[deptId]) {
        stats[deptId].total++;
        
        if (record.type === AttendanceType.LEAVE) {
          stats[deptId].leave++;
        } else if (record.type === AttendanceType.OVERTIME) {
          stats[deptId].overtime++;
        } else if (record.type === AttendanceType.ABSENT) {
          stats[deptId].absent++;
        }
      }
    });
    
    return stats;
  });

  // 计算属性：待审批的请假申请数量
  const pendingLeaveCount = computed(() => {
    return leaveApplications.value.filter(app => app.status === LeaveStatus.PENDING).length;
  });

  // 生成模拟考勤记录
  const generateMockAttendanceRecords = (): AttendanceRecord[] => {
    const mockRecords: AttendanceRecord[] = [];
    const types = Object.values(AttendanceType);
    
    // 生成过去30天的考勤记录，每天约20条
    for (let day = 0; day < 30; day++) {
      const date = new Date();
      date.setDate(date.getDate() - day);
      const dateStr = date.toISOString().split('T')[0];
      
      // 跳过周末
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) continue;
      
      // 每天生成约20条记录
      for (let i = 0; i < 20; i++) {
        const now = new Date().toISOString();
        const type = types[Math.floor(Math.random() * types.length)];
        
        mockRecords.push({
          id: generateId(),
          employeeId: `emp-${Math.floor(Math.random() * 35) + 1}`,
          employeeName: ['张三', '李四', '王五', '赵六', '钱七'][Math.floor(Math.random() * 5)] + i,
          date: dateStr,
          type,
          hours: type === AttendanceType.OVERTIME ? Math.floor(Math.random() * 4) + 2 : 8,
          projectId: Math.random() > 0.3 ? `PRJ-${Math.floor(Math.random() * 100) + 1}` : undefined,
          notes: type === AttendanceType.LEAVE ? '请假原因' : undefined,
          createdAt: now,
          updatedAt: now
        });
      }
    }
    
    return mockRecords;
  };

  // 生成模拟请假申请
  const generateMockLeaveApplications = (): LeaveApplication[] => {
    const mockApplications: LeaveApplication[] = [];
    const leaveTypes = Object.values(LeaveType);
    const statuses = Object.values(LeaveStatus);
    
    for (let i = 0; i < 25; i++) {
      const now = new Date().toISOString();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + Math.floor(Math.random() * 10));
      
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 5) + 1);
      
      const days = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24)) + 1;
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      mockApplications.push({
        id: generateId(),
        employeeId: `emp-${Math.floor(Math.random() * 35) + 1}`,
        employeeName: ['张三', '李四', '王五', '赵六', '钱七'][Math.floor(Math.random() * 5)] + i,
        leaveType: leaveTypes[Math.floor(Math.random() * leaveTypes.length)],
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        days,
        reason: ['家庭原因', '个人事务', '身体不适', '年假计划', '探亲访友'][Math.floor(Math.random() * 5)],
        status,
        projectConflicts: Math.random() > 0.7 ? [
          {
            projectId: 'PRJ-' + Math.floor(Math.random() * 100),
            projectName: '项目' + Math.floor(Math.random() * 10),
            impactedHours: Math.floor(Math.random() * 20) + 4
          }
        ] : undefined,
        approvedBy: status === LeaveStatus.APPROVED || status === LeaveStatus.REJECTED ? '审批人' + Math.floor(Math.random() * 3) : undefined,
        approvedAt: status === LeaveStatus.APPROVED || status === LeaveStatus.REJECTED ? now : undefined,
        rejectionReason: status === LeaveStatus.REJECTED ? '与项目进度冲突' : undefined,
        createdAt: now,
        updatedAt: now
      });
    }
    
    return mockApplications;
  };

  // 初始化方法
  const initialize = async () => {
    if (attendanceRecords.value.length === 0) {
      await fetchAttendanceRecords();
    }
    
    if (leaveApplications.value.length === 0) {
      await fetchLeaveApplications();
    }
  };

  return {
    // 状态
    attendanceRecords,
    leaveApplications,
    leaveBalances,
    loading,
    
    // 计算属性
    attendanceStatsByDepartment,
    pendingLeaveCount,
    
    // 方法
    fetchAttendanceRecords,
    createAttendanceRecord,
    batchImportAttendanceRecords,
    fetchLeaveApplications,
    createLeaveApplication,
    updateLeaveApplicationStatus,
    fetchLeaveBalance,
    checkProjectConflicts,
    initialize
  };
}); 