import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { notification } from 'ant-design-vue'
import type { Project, ProjectStatus, ProjectType } from '../types/project'
import { ProjectStatus as ProjectStatusEnum } from '../types/project'

export const useProjectStore = defineStore('project', () => {
  // State
  const projects = ref<Project[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getProjectById = computed(() => {
    return (id: string) => projects.value.find(project => project.id === id)
  })

  const getProjectsByStatus = computed(() => {
    return (status: ProjectStatus) => projects.value.filter(project => project.status === status)
  })

  const getProjectsByType = computed(() => {
    return (type: ProjectType) => projects.value.filter(project => project.type === type)
  })

  // Actions
  const fetchProjects = async () => {
    loading.value = true
    error.value = null
    try {
      // Mock API call - replace with actual API implementation
      // const response = await fetch('/api/projects')
      // projects.value = await response.json()
      
      // For now, using mock data
      setTimeout(() => {
        projects.value = generateMockProjects()
        loading.value = false
      }, 500)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch projects'
      loading.value = false
      notification.error({
        message: 'Error',
        description: error.value
      })
    }
  }

  const createProject = async (project: Omit<Project, 'id' | 'createdAt'>) => {
    loading.value = true
    error.value = null
    try {
      // Mock API call - replace with actual API implementation
      // const response = await fetch('/api/projects', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(project)
      // })
      // const newProject = await response.json()
      
      // For now, creating mock project
      const newProject: Project = {
        id: `PRJ-${Date.now()}`,
        createdAt: new Date().toISOString(),
        ...project
      }
      
      projects.value.push(newProject)
      notification.success({
        message: 'Success',
        description: 'Project created successfully'
      })
      loading.value = false
      return newProject
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create project'
      loading.value = false
      notification.error({
        message: 'Error',
        description: error.value
      })
      throw error.value
    }
  }

  const updateProject = async (id: string, updates: Partial<Omit<Project, 'id'>>) => {
    loading.value = true
    error.value = null
    try {
      // Mock API call - replace with actual API implementation
      // const response = await fetch(`/api/projects/${id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(updates)
      // })
      // const updatedProject = await response.json()
      
      // For now, updating locally
      const index = projects.value.findIndex(p => p.id === id)
      if (index === -1) {
        throw new Error('Project not found')
      }
      
      projects.value[index] = {
        ...projects.value[index],
        ...updates,
      }
      
      notification.success({
        message: 'Success',
        description: 'Project updated successfully'
      })
      loading.value = false
      return projects.value[index]
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update project'
      loading.value = false
      notification.error({
        message: 'Error',
        description: error.value
      })
      throw error.value
    }
  }

  const deleteProject = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      // Mock API call - replace with actual API implementation
      // await fetch(`/api/projects/${id}`, { method: 'DELETE' })
      
      // For now, deleting locally
      const index = projects.value.findIndex(p => p.id === id)
      if (index === -1) {
        throw new Error('Project not found')
      }
      
      projects.value.splice(index, 1)
      notification.success({
        message: 'Success',
        description: 'Project deleted successfully'
      })
      loading.value = false
      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete project'
      loading.value = false
      notification.error({
        message: 'Error',
        description: error.value
      })
      throw error.value
    }
  }

  const updateProjectStatus = async (id: string, status: ProjectStatus) => {
    return updateProject(id, { status })
  }

  // Helper function to generate mock data
  const generateMockProjects = (): Project[] => {
    return [
      {
        id: 'PRJ-001',
        name: '财务系统升级项目',
        description: '升级现有财务系统以支持新的会计标准和报表需求',
        type: 'IT',
        status: ProjectStatusEnum.IN_PROGRESS,
        budget: 250000,
        startDate: '2023-01-15',
        endDate: '2023-06-30',
        manager: '张明',
        department: '信息技术部',
        priority: 'high',
        progress: 45,
        createdAt: '2023-01-10T08:00:00Z'
      },
      {
        id: 'PRJ-002',
        name: '市场调研与分析',
        description: '针对新产品线进行市场调研和竞争分析',
        type: 'Marketing',
        status: ProjectStatusEnum.PLANNING,
        budget: 80000,
        startDate: '2023-02-01',
        endDate: '2023-04-15',
        manager: '李红',
        department: '市场部',
        priority: 'medium',
        progress: 20,
        createdAt: '2023-01-20T10:30:00Z'
      },
      {
        id: 'PRJ-003',
        name: '供应链优化项目',
        description: '优化供应链流程，减少交货时间和成本',
        type: 'Operations',
        status: ProjectStatusEnum.COMPLETED,
        budget: 150000,
        startDate: '2022-09-01',
        endDate: '2023-01-31',
        manager: '王强',
        department: '运营部',
        priority: 'high',
        progress: 100,
        createdAt: '2022-08-15T09:15:00Z'
      },
      {
        id: 'PRJ-004',
        name: '员工培训计划',
        description: '为所有部门开发综合培训计划',
        type: 'HR',
        status: ProjectStatusEnum.IN_PROGRESS,
        budget: 50000,
        startDate: '2023-03-01',
        endDate: '2023-05-31',
        manager: '刘静',
        department: '人力资源部',
        priority: 'low',
        progress: 60,
        createdAt: '2023-02-20T14:00:00Z'
      },
      {
        id: 'PRJ-005',
        name: '新产品开发',
        description: '开发针对中小企业的新产品线',
        type: 'R&D',
        status: ProjectStatusEnum.PENDING_APPROVAL,
        budget: 350000,
        startDate: '2023-04-01',
        endDate: '2023-10-31',
        manager: '赵伟',
        department: '研发部',
        priority: 'high',
        progress: 0,
        createdAt: '2023-03-15T11:45:00Z'
      }
    ]
  }

  return {
    // State
    projects,
    loading,
    error,
    
    // Getters
    getProjectById,
    getProjectsByStatus,
    getProjectsByType,
    
    // Actions
    fetchProjects,
    createProject,
    updateProject,
    deleteProject,
    updateProjectStatus
  }
}) 