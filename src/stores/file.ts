import { defineStore } from 'pinia';
import { ref, computed, reactive } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  type File, type BusinessFile, type IntellectualPropertyFile, 
  type CertificateFile, type LegalFile, type FileVersion,
  type Permission, type FileOperationLog, type FileStatistics,
  type PaginationInfo, type FileSearchFilter,
  FileType, FileCategory, FileStatus, IPStatus, BusinessFileType,
  IntellectualPropertyType, PermissionType, CodeReviewStatus
} from '@/types/file';

// 引入模拟数据生成函数
import { 
  generateMockFiles, 
  generateMockFileVersions, 
  generateMockFilePermissions, 
  generateMockOperationLogs, 
  generateMockStatistics 
} from './fileMocks';

// 文件管理Store
export const useFileStore = defineStore('file', () => {
  // 状态
  const loading = ref(false);
  const uploading = ref(false);
  const files = ref<File[]>([]);
  const businessFiles = ref<BusinessFile[]>([]);
  const intellectualPropertyFiles = ref<IntellectualPropertyFile[]>([]);
  const certificateFiles = ref<CertificateFile[]>([]);
  const legalFiles = ref<LegalFile[]>([]);
  const currentFile = ref<File | null>(null);
  const fileVersions = ref<FileVersion[]>([]);
  const filePermissions = ref<Permission[]>([]);
  const operationLogs = ref<FileOperationLog[]>([]);
  const statistics = ref<FileStatistics | null>(null);
  const errorMessage = ref('');

  // 分页配置
  const pagination = reactive<PaginationInfo>({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total) => `共 ${total} 条`
  });

  // 筛选条件
  const filter = reactive<FileSearchFilter>({
    keyword: '',
    category: undefined,
    fileType: undefined,
    startDate: '',
    endDate: '',
    projectId: '',
    status: undefined,
    tags: [],
    uploadBy: ''
  });

  // 计算属性
  const filesByCategory = computed(() => {
    const result: Record<FileCategory, File[]> = {
      [FileCategory.BUSINESS]: [],
      [FileCategory.INTELLECTUAL]: [],
      [FileCategory.CERTIFICATE]: [],
      [FileCategory.LEGAL]: []
    };
    
    files.value.forEach(file => {
      if (result[file.category]) {
        result[file.category].push(file);
      }
    });
    
    return result;
  });

  const expiringFiles = computed(() => {
    return files.value.filter(file => file.status === FileStatus.EXPIRING_SOON);
  });

  const fileById = computed(() => {
    return (id: string) => files.value.find(file => file.id === id) || null;
  });

  // 获取全部文件
  const fetchFiles = async () => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      files.value = generateMockFiles();
      pagination.total = files.value.length;
      
      // 更新分类文件
      updateCategoryFiles();
      
      loading.value = false;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '获取文件列表失败';
      console.error('获取文件列表失败', error);
    }
  };
  
  // 根据ID获取文件
  const fetchFileById = async (id: string) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const file = files.value.find(f => f.id === id);
      if (file) {
        currentFile.value = file;
      } else {
        throw new Error('文件不存在');
      }
      
      loading.value = false;
      return currentFile.value;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '获取文件详情失败';
      console.error('获取文件详情失败', error);
      return null;
    }
  };
  
  // 获取文件版本历史
  const fetchFileVersions = async (fileId: string) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      fileVersions.value = generateMockFileVersions(fileId);
      
      loading.value = false;
      return fileVersions.value;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '获取文件版本历史失败';
      console.error('获取文件版本历史失败', error);
      return [];
    }
  };
  
  // 获取文件权限
  const fetchFilePermissions = async (fileId: string) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      filePermissions.value = generateMockFilePermissions(fileId);
      
      loading.value = false;
      return filePermissions.value;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '获取文件权限设置失败';
      console.error('获取文件权限设置失败', error);
      return [];
    }
  };
  
  // 获取文件操作日志
  const fetchFileOperationLogs = async (fileId: string) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      operationLogs.value = generateMockOperationLogs(fileId);
      
      loading.value = false;
      return operationLogs.value;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '获取文件操作日志失败';
      console.error('获取文件操作日志失败', error);
      return [];
    }
  };
  
  // 获取文件统计信息
  const fetchFileStatistics = async () => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      statistics.value = generateMockStatistics();
      
      loading.value = false;
      return statistics.value;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '获取文件统计信息失败';
      console.error('获取文件统计信息失败', error);
      return null;
    }
  };
  
  // 更新分类文件列表
  const updateCategoryFiles = () => {
    businessFiles.value = files.value
      .filter(file => file.category === FileCategory.BUSINESS)
      .map(file => file as BusinessFile);
      
    intellectualPropertyFiles.value = files.value
      .filter(file => file.category === FileCategory.INTELLECTUAL)
      .map(file => file as IntellectualPropertyFile);
      
    certificateFiles.value = files.value
      .filter(file => file.category === FileCategory.CERTIFICATE)
      .map(file => file as CertificateFile);
      
    legalFiles.value = files.value
      .filter(file => file.category === FileCategory.LEGAL)
      .map(file => file as LegalFile);
  };

  // 上传文件
  const uploadFile = async (file: File) => {
    uploading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 生成ID和Code
      const newFile = {
        ...file,
        id: `F${Math.floor(Math.random() * 10000)}`,
        code: `FILE-${Math.floor(Math.random() * 100000)}`,
        uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        createdBy: '当前用户',
        status: FileStatus.ACTIVE
      };
      
      files.value.unshift(newFile);
      updateCategoryFiles();
      
      uploading.value = false;
      message.success('文件上传成功');
      return newFile;
    } catch (error) {
      uploading.value = false;
      errorMessage.value = '文件上传失败';
      console.error('文件上传失败', error);
      message.error('文件上传失败');
      return null;
    }
  };
  
  // 更新文件
  const updateFile = async (id: string, data: Partial<File>) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = files.value.findIndex(file => file.id === id);
      if (index === -1) {
        throw new Error('文件不存在');
      }
      
      files.value[index] = {
        ...files.value[index],
        ...data,
        updatedTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updatedBy: '当前用户'
      };
      
      if (currentFile.value && currentFile.value.id === id) {
        currentFile.value = files.value[index];
      }
      
      updateCategoryFiles();
      
      loading.value = false;
      message.success('文件更新成功');
      return files.value[index];
    } catch (error) {
      loading.value = false;
      errorMessage.value = '文件更新失败';
      console.error('文件更新失败', error);
      message.error('文件更新失败');
      return null;
    }
  };
  
  // 删除文件
  const deleteFile = async (id: string) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = files.value.findIndex(file => file.id === id);
      if (index === -1) {
        throw new Error('文件不存在');
      }
      
      files.value.splice(index, 1);
      updateCategoryFiles();
      
      if (currentFile.value && currentFile.value.id === id) {
        currentFile.value = null;
      }
      
      loading.value = false;
      message.success('文件删除成功');
      return true;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '文件删除失败';
      console.error('文件删除失败', error);
      message.error('文件删除失败');
      return false;
    }
  };
  
  // 搜索文件
  const searchFiles = async (searchFilter: Partial<FileSearchFilter> = {}) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 更新筛选条件
      Object.assign(filter, searchFilter);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 本地筛选模拟
      const allFiles = generateMockFiles();
      
      files.value = allFiles.filter(file => {
        // 关键字筛选
        if (filter.keyword && !file.name.toLowerCase().includes(filter.keyword.toLowerCase()) && 
            !file.code.toLowerCase().includes(filter.keyword.toLowerCase())) {
          return false;
        }
        
        // 分类筛选
        if (filter.category && file.category !== filter.category) {
          return false;
        }
        
        // 文件类型筛选
        if (filter.fileType && file.type !== filter.fileType) {
          return false;
        }
        
        // 状态筛选
        if (filter.status && file.status !== filter.status) {
          return false;
        }
        
        // 项目ID筛选
        if (filter.projectId && file.projectId !== filter.projectId) {
          return false;
        }
        
        // 上传人筛选
        if (filter.uploadBy && file.createdBy !== filter.uploadBy) {
          return false;
        }
        
        // 日期范围筛选
        if (filter.startDate && filter.endDate) {
          const uploadDate = dayjs(file.uploadTime);
          const startDate = dayjs(filter.startDate);
          const endDate = dayjs(filter.endDate);
          
          if (uploadDate.isBefore(startDate) || uploadDate.isAfter(endDate)) {
            return false;
          }
        }
        
        // 标签筛选
        if (filter.tags && filter.tags.length > 0) {
          if (!file.tags || !file.tags.some(tag => filter.tags!.includes(tag))) {
            return false;
          }
        }
        
        return true;
      });
      
      pagination.total = files.value.length;
      updateCategoryFiles();
      
      loading.value = false;
      return files.value;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '搜索文件失败';
      console.error('搜索文件失败', error);
      return [];
    }
  };
  
  // 重置筛选条件
  const resetFilter = () => {
    Object.assign(filter, {
      keyword: '',
      category: undefined,
      fileType: undefined,
      startDate: '',
      endDate: '',
      projectId: '',
      status: undefined,
      tags: [],
      uploadBy: ''
    });
  };
  
  // 设置文件权限
  const setFilePermission = async (fileId: string, permission: Omit<Permission, 'id' | 'fileId' | 'createdBy' | 'createdTime'>) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newPermission: Permission = {
        id: `P${Math.floor(Math.random() * 10000)}`,
        fileId,
        createdBy: '当前用户',
        createdTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        ...permission
      };
      
      filePermissions.value.push(newPermission);
      
      loading.value = false;
      message.success('设置文件权限成功');
      return newPermission;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '设置文件权限失败';
      console.error('设置文件权限失败', error);
      message.error('设置文件权限失败');
      return null;
    }
  };
  
  // 删除文件权限
  const deleteFilePermission = async (permissionId: string) => {
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = filePermissions.value.findIndex(p => p.id === permissionId);
      if (index === -1) {
        throw new Error('权限设置不存在');
      }
      
      filePermissions.value.splice(index, 1);
      
      loading.value = false;
      message.success('删除文件权限成功');
      return true;
    } catch (error) {
      loading.value = false;
      errorMessage.value = '删除文件权限失败';
      console.error('删除文件权限失败', error);
      message.error('删除文件权限失败');
      return false;
    }
  };
  
  return {
    // 状态
    loading,
    uploading,
    files,
    businessFiles,
    intellectualPropertyFiles,
    certificateFiles,
    legalFiles,
    currentFile,
    fileVersions,
    filePermissions,
    operationLogs,
    statistics,
    errorMessage,
    pagination,
    filter,
    
    // 计算属性
    filesByCategory,
    expiringFiles,
    fileById,
    
    // 方法
    fetchFiles,
    fetchFileById,
    fetchFileVersions,
    fetchFilePermissions,
    fetchFileOperationLogs,
    fetchFileStatistics,
    uploadFile,
    updateFile,
    deleteFile,
    searchFiles,
    resetFilter,
    setFilePermission,
    deleteFilePermission,
  };
}); 