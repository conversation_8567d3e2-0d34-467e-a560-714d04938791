import { defineStore } from 'pinia';
import { ref, reactive, computed } from 'vue';

// 合同状态枚举
export enum ContractStatus {
  DRAFT = 'draft',
  REVIEWING = 'reviewing',
  REJECTED = 'rejected',
  APPROVED = 'approved',
  SIGNED = 'signed',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
  TERMINATED = 'terminated'
}

// 合同类型枚举
export enum ContractType {
  SALES = 'sales', // 销售合同
  PURCHASE = 'purchase', // 采购合同
  SERVICE = 'service', // 服务合同
  FRAMEWORK = 'framework', // 框架协议
  OTHER = 'other' // 其他合同
}

// 签署状态枚举
export enum SignatureStatus {
  UNSIGNED = 'unsigned', // 未签章
  PENDING_OUR = 'pending_our', // 待我方签章
  PENDING_OTHER = 'pending_other', // 待对方签章
  SIGNED = 'signed', // 已签订
  INVALID = 'invalid', // 已作废
  ABNORMAL = 'abnormal' // 异常
}

// 合同接口定义
export interface Contract {
  id: string;
  contractCode: string;
  contractName: string;
  contractType: ContractType;
  partyA: string; // 甲方
  partyB: string; // 乙方
  amount: number; // 金额
  taxRate: number; // 税率
  paymentMethod: string; // 付款方式
  deliveryDate: string; // 交付时间
  status: ContractStatus;
  signatureStatus: SignatureStatus;
  projectId?: string; // 关联项目ID
  templateId?: string; // 关联模板ID
  draftMethod?: 'template' | 'upload'; // 起草方式
  content?: string; // 合同内容
  attachments?: ContractAttachment[]; // 附件
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  remark?: string;
}

// 合同附件接口
export interface ContractAttachment {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadTime: string;
  uploadedBy: string;
  url: string;
  category: string; // 文件分类：双章扫描件、试算表、签收单、验收报告等
}

// 审批记录接口
export interface ApprovalRecord {
  id: string;
  contractId: string;
  approver: string;
  approverRole: string;
  opinion?: string;
  status: 'pending' | 'approved' | 'rejected';
  timestamp: string;
}

// 变更记录接口
export interface AmendmentRecord {
  id: string;
  contractId: string;
  amendmentType: 'supplement' | 'change'; // 补充或变更
  content: string;
  reason: string;
  status: ContractStatus;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 使用组合式API定义合同Store
export const useContractStore = defineStore('contract', () => {
  // 状态
  const contractList = ref<Contract[]>([]);
  const currentContract = ref<Contract | null>(null);
  const approvalRecords = ref<ApprovalRecord[]>([]);
  const amendmentRecords = ref<AmendmentRecord[]>([]);
  const loading = ref(false);
  const draftForm = reactive({
    draftMethod: 'template' as 'template' | 'upload',
    selectedTemplateId: '',
    uploadedFile: null as File | null,
    projectId: '',
  });

  // Getters
  const getContractById = computed(() => {
    return (id: string) => contractList.value.find(contract => contract.id === id) || null;
  });

  const getContractsByStatus = computed(() => {
    return (status: ContractStatus) => contractList.value.filter(contract => contract.status === status);
  });

  const getApprovalsByContractId = computed(() => {
    return (contractId: string) => approvalRecords.value.filter(record => record.contractId === contractId);
  });

  const getAmendmentsByContractId = computed(() => {
    return (contractId: string) => amendmentRecords.value.filter(record => record.contractId === contractId);
  });

  // Actions
  // 获取合同列表
  const fetchContracts = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟数据 - 实际开发中应替换为API调用
      contractList.value = generateMockContracts();
      
      loading.value = false;
      return contractList.value;
    } catch (error) {
      console.error('获取合同列表失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 获取合同详情
  const fetchContractById = async (id: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 查找现有合同或生成模拟数据
      const contract = contractList.value.find(c => c.id === id);
      if (contract) {
        currentContract.value = contract;
      } else {
        const mockContract = generateMockContracts().find(c => c.id === id);
        if (mockContract) {
          currentContract.value = mockContract;
          contractList.value.push(mockContract);
        }
      }
      
      loading.value = false;
      return currentContract.value;
    } catch (error) {
      console.error('获取合同详情失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 创建新合同
  const createContract = async (contractData: Partial<Contract>) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newId = `C${Date.now().toString().slice(-8)}`;
      const newContract: Contract = {
        id: newId,
        contractCode: `CT-${new Date().getFullYear()}-${newId}`,
        contractName: contractData.contractName || '未命名合同',
        contractType: contractData.contractType || ContractType.OTHER,
        partyA: contractData.partyA || '我方公司',
        partyB: contractData.partyB || '对方公司',
        amount: contractData.amount || 0,
        taxRate: contractData.taxRate || 0.13,
        paymentMethod: contractData.paymentMethod || '银行转账',
        deliveryDate: contractData.deliveryDate || new Date().toISOString().split('T')[0],
        status: ContractStatus.DRAFT,
        signatureStatus: SignatureStatus.UNSIGNED,
        projectId: contractData.projectId,
        templateId: contractData.templateId,
        draftMethod: draftForm.draftMethod,
        content: contractData.content || '',
        createdBy: '当前用户',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        attachments: [],
        remark: contractData.remark || '',
      };
      
      contractList.value.push(newContract);
      currentContract.value = newContract;
      
      loading.value = false;
      return newContract;
    } catch (error) {
      console.error('创建合同失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 更新合同
  const updateContract = async (id: string, updateData: Partial<Contract>) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = contractList.value.findIndex(c => c.id === id);
      if (index !== -1) {
        contractList.value[index] = {
          ...contractList.value[index],
          ...updateData,
          updatedAt: new Date().toISOString()
        };
        
        if (currentContract.value?.id === id) {
          currentContract.value = contractList.value[index];
        }
      }
      
      loading.value = false;
      return contractList.value[index];
    } catch (error) {
      console.error('更新合同失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 提交审批
  const submitForApproval = async (contractId: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const contract = contractList.value.find(c => c.id === contractId);
      if (contract) {
        // 更新合同状态
        contract.status = ContractStatus.REVIEWING;
        contract.updatedAt = new Date().toISOString();
        
        // 创建审批记录
        const approvalRecord: ApprovalRecord = {
          id: `APR-${Date.now().toString().slice(-6)}`,
          contractId,
          approver: '审批人员',
          approverRole: '经理',
          status: 'pending',
          timestamp: new Date().toISOString()
        };
        
        approvalRecords.value.push(approvalRecord);
        
        if (currentContract.value?.id === contractId) {
          currentContract.value = contract;
        }
      }
      
      loading.value = false;
      return contract;
    } catch (error) {
      console.error('提交审批失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 审批通过
  const approveContract = async (contractId: string, approverId: string, opinion?: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 400));
      
      // 更新合同状态
      const contract = contractList.value.find(c => c.id === contractId);
      if (contract) {
        contract.status = ContractStatus.APPROVED;
        contract.updatedAt = new Date().toISOString();
        
        // 更新审批记录
        const approvalIndex = approvalRecords.value.findIndex(
          record => record.contractId === contractId && record.approver === approverId
        );
        
        if (approvalIndex !== -1) {
          approvalRecords.value[approvalIndex].status = 'approved';
          approvalRecords.value[approvalIndex].opinion = opinion;
          approvalRecords.value[approvalIndex].timestamp = new Date().toISOString();
        } else {
          // 创建新的审批记录
          approvalRecords.value.push({
            id: `APR-${Date.now().toString().slice(-6)}`,
            contractId,
            approver: approverId,
            approverRole: '经理',
            status: 'approved',
            opinion,
            timestamp: new Date().toISOString()
          });
        }
        
        if (currentContract.value?.id === contractId) {
          currentContract.value = contract;
        }
      }
      
      loading.value = false;
      return contract;
    } catch (error) {
      console.error('审批失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 签署合同
  const signContract = async (contractId: string, signatureStatus: SignatureStatus) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const contract = contractList.value.find(c => c.id === contractId);
      if (contract) {
        contract.signatureStatus = signatureStatus;
        
        // 如果完成签署，更新状态
        if (signatureStatus === SignatureStatus.SIGNED) {
          contract.status = ContractStatus.SIGNED;
        }
        
        contract.updatedAt = new Date().toISOString();
        
        if (currentContract.value?.id === contractId) {
          currentContract.value = contract;
        }
      }
      
      loading.value = false;
      return contract;
    } catch (error) {
      console.error('签署合同失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 归档合同
  const archiveContract = async (contractId: string, attachments?: ContractAttachment[]) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 700));
      
      const contract = contractList.value.find(c => c.id === contractId);
      if (contract) {
        contract.status = ContractStatus.ARCHIVED;
        contract.updatedAt = new Date().toISOString();
        
        // 添加归档附件
        if (attachments && attachments.length > 0) {
          if (!contract.attachments) {
            contract.attachments = [];
          }
          contract.attachments = [...contract.attachments, ...attachments];
        }
        
        if (currentContract.value?.id === contractId) {
          currentContract.value = contract;
        }
      }
      
      loading.value = false;
      return contract;
    } catch (error) {
      console.error('归档合同失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 添加补充协议或变更
  const createAmendment = async (amendmentData: Partial<AmendmentRecord>) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newAmendment: AmendmentRecord = {
        id: `AMD-${Date.now().toString().slice(-6)}`,
        contractId: amendmentData.contractId || '',
        amendmentType: amendmentData.amendmentType || 'supplement',
        content: amendmentData.content || '',
        reason: amendmentData.reason || '',
        status: ContractStatus.DRAFT,
        createdBy: '当前用户',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      amendmentRecords.value.push(newAmendment);
      
      loading.value = false;
      return newAmendment;
    } catch (error) {
      console.error('创建补充协议/变更失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 上传合同附件
  const uploadAttachment = async (contractId: string, file: File, category: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newAttachment: ContractAttachment = {
        id: `ATT-${Date.now().toString().slice(-6)}`,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        uploadTime: new Date().toISOString(),
        uploadedBy: '当前用户',
        url: URL.createObjectURL(file), // 实际开发中应替换为实际URL
        category
      };
      
      const contract = contractList.value.find(c => c.id === contractId);
      if (contract) {
        if (!contract.attachments) {
          contract.attachments = [];
        }
        contract.attachments.push(newAttachment);
        contract.updatedAt = new Date().toISOString();
        
        if (currentContract.value?.id === contractId) {
          currentContract.value = contract;
        }
      }
      
      loading.value = false;
      return newAttachment;
    } catch (error) {
      console.error('上传附件失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 清理当前合同数据 (用于页面切换或退出)
  const clearCurrentContract = () => {
    currentContract.value = null;
    draftForm.draftMethod = 'template';
    draftForm.selectedTemplateId = '';
    draftForm.uploadedFile = null;
    draftForm.projectId = '';
  };

  // 生成模拟合同数据
  const generateMockContracts = (): Contract[] => {
    const statuses = Object.values(ContractStatus);
    const types = Object.values(ContractType);
    const signStatuses = Object.values(SignatureStatus);
    
    return Array.from({ length: 15 }).map((_, index) => {
      const id = `C${10000 + index}`;
      
      return {
        id,
        contractCode: `CT-2023-${id}`,
        contractName: `${['销售合同', '采购合同', '服务合同', '框架协议', '其他合同'][index % 5]}-${index + 1}`,
        contractType: types[index % types.length] as ContractType,
        partyA: '我方公司',
        partyB: `客户公司${index + 1}`,
        amount: Math.round(Math.random() * 1000000 * 100) / 100,
        taxRate: 0.13,
        paymentMethod: ['全额支付', '分期付款', '按进度付款'][index % 3],
        deliveryDate: new Date(Date.now() + 86400000 * (index * 10 + 30)).toISOString().split('T')[0],
        status: statuses[index % statuses.length] as ContractStatus,
        signatureStatus: signStatuses[index % signStatuses.length] as SignatureStatus,
        projectId: index % 3 === 0 ? `P${10000 + index}` : undefined,
        templateId: index % 2 === 0 ? `T${1000 + index}` : undefined,
        content: `这是合同${id}的内容示例。包含合同条款和相关约定。`,
        createdBy: '系统管理员',
        createdAt: new Date(Date.now() - 86400000 * (15 - index)).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * (10 - index)).toISOString(),
        attachments: index % 3 === 0 ? [
          {
            id: `ATT-${100 + index}`,
            fileName: '合同扫描文件.pdf',
            fileType: 'application/pdf',
            fileSize: 1024 * 1024 * 2, // 2MB
            uploadTime: new Date(Date.now() - 86400000 * (10 - index)).toISOString(),
            uploadedBy: '系统管理员',
            url: 'https://example.com/contract-scan.pdf',
            category: '双章扫描件'
          }
        ] : [],
        remark: index % 5 === 0 ? '此合同为重要战略客户合同，需特别关注' : ''
      };
    });
  };

  return {
    // 状态
    contractList,
    currentContract,
    approvalRecords,
    amendmentRecords,
    loading,
    draftForm,
    
    // Getters
    getContractById,
    getContractsByStatus,
    getApprovalsByContractId,
    getAmendmentsByContractId,
    
    // Actions
    fetchContracts,
    fetchContractById,
    createContract,
    updateContract,
    submitForApproval,
    approveContract,
    signContract,
    archiveContract,
    createAmendment,
    uploadAttachment,
    clearCurrentContract
  };
}); 