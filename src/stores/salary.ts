import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useEmployeeStore } from './employee';

// 定义薪资记录类型
export interface SalaryRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  departmentId: string;
  month: string;
  baseSalary: number;
  bonus?: number;
  overtimePay?: number;
  allowances?: number;
  tax?: number;
  insurance?: number;
  providentFund?: number;
  deductions?: number;
  actualAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  paymentDate?: string;
  createdAt: string;
  updatedAt: string;
}

// 定义薪资统计类型
interface SalaryStatistics {
  totalAmount: number;
  employeeCount: number;
  averageSalary: number;
  completionRate: number;
}

// 定义查询参数类型
interface SalaryQueryParams {
  page?: number;
  pageSize?: number;
  month?: string;
  employeeName?: string;
  departmentId?: string;
  status?: string;
}

// 定义分页结果类型
interface PaginatedResult<T> {
  items: T[];
  total: number;
}

export const useSalaryStore = defineStore('salary', () => {
  // 状态
  const salaryRecords = ref<SalaryRecord[]>([]);
  const loading = ref(false);
  const statistics = ref<SalaryStatistics>({
    totalAmount: 0,
    employeeCount: 0,
    averageSalary: 0,
    completionRate: 0,
  });

  // 模拟API延迟
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // 获取薪资列表
  const fetchSalaryList = async (params: SalaryQueryParams): Promise<PaginatedResult<SalaryRecord>> => {
    loading.value = true;
    try {
      // 模拟API调用
      await delay(800);
      
      // 这里应该是实际API调用，目前使用模拟数据
      // 从本地模拟数据筛选
      let filteredRecords = mockSalaryRecords;
      
      if (params.month) {
        filteredRecords = filteredRecords.filter(record => record.month.startsWith(params.month!));
      }
      
      if (params.employeeName) {
        filteredRecords = filteredRecords.filter(record => 
          record.employeeName.toLowerCase().includes(params.employeeName!.toLowerCase())
        );
      }
      
      if (params.departmentId) {
        filteredRecords = filteredRecords.filter(record => record.departmentId === params.departmentId);
      }
      
      if (params.status) {
        filteredRecords = filteredRecords.filter(record => record.status === params.status);
      }
      
      // 分页
      const startIndex = ((params.page || 1) - 1) * (params.pageSize || 10);
      const endIndex = startIndex + (params.pageSize || 10);
      const paginatedRecords = filteredRecords.slice(startIndex, endIndex);
      
      // 设置本地状态
      salaryRecords.value = paginatedRecords;
      
      return {
        items: paginatedRecords,
        total: filteredRecords.length
      };
    } catch (error) {
      console.error('获取薪资列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取薪资统计数据
  const getSalaryStatistics = async (month: string): Promise<SalaryStatistics> => {
    try {
      // 模拟API调用
      await delay(600);
      
      // 这里应该是实际API调用，目前使用模拟数据
      // 从本地模拟数据计算统计信息
      const monthRecords = mockSalaryRecords.filter(record => record.month.startsWith(month));
      
      const completedRecords = monthRecords.filter(record => record.status === 'completed');
      const totalAmount = completedRecords.reduce((sum, record) => sum + record.actualAmount, 0);
      const employeeCount = monthRecords.length;
      const averageSalary = employeeCount > 0 ? totalAmount / employeeCount : 0;
      const completionRate = employeeCount > 0 ? (completedRecords.length / employeeCount) * 100 : 0;
      
      const stats = {
        totalAmount,
        employeeCount,
        averageSalary,
        completionRate
      };
      
      // 设置本地状态
      statistics.value = stats;
      
      return stats;
    } catch (error) {
      console.error('获取薪资统计失败:', error);
      throw error;
    }
  };

  // 获取单个薪资记录
  const getSalaryById = async (id: string): Promise<SalaryRecord | null> => {
    try {
      // 模拟API调用
      await delay(300);
      
      // 这里应该是实际API调用，目前使用模拟数据
      const record = mockSalaryRecords.find(record => record.id === id);
      
      return record || null;
    } catch (error) {
      console.error('获取薪资记录失败:', error);
      throw error;
    }
  };

  // 处理薪资发放
  const processSalary = async (id: string): Promise<boolean> => {
    try {
      // 模拟API调用
      await delay(1000);
      
      // 这里应该是实际API调用，目前使用模拟数据模拟
      const recordIndex = mockSalaryRecords.findIndex(record => record.id === id);
      
      if (recordIndex >= 0) {
        // 模拟更新状态
        mockSalaryRecords[recordIndex].status = 'completed';
        mockSalaryRecords[recordIndex].paymentDate = new Date().toISOString().split('T')[0];
        
        // 更新本地状态
        const localIndex = salaryRecords.value.findIndex(record => record.id === id);
        if (localIndex >= 0) {
          salaryRecords.value[localIndex].status = 'completed';
          salaryRecords.value[localIndex].paymentDate = mockSalaryRecords[recordIndex].paymentDate;
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('处理薪资发放失败:', error);
      throw error;
    }
  };

  // 重试失败的薪资发放
  const retrySalary = async (id: string): Promise<boolean> => {
    try {
      // 模拟API调用
      await delay(800);
      
      // 这里应该是实际API调用，目前使用模拟数据模拟
      const recordIndex = mockSalaryRecords.findIndex(record => record.id === id);
      
      if (recordIndex >= 0 && mockSalaryRecords[recordIndex].status === 'failed') {
        // 模拟更新状态
        mockSalaryRecords[recordIndex].status = 'processing';
        
        // 更新本地状态
        const localIndex = salaryRecords.value.findIndex(record => record.id === id);
        if (localIndex >= 0) {
          salaryRecords.value[localIndex].status = 'processing';
        }
        
        // 模拟异步处理结果
        setTimeout(() => {
          mockSalaryRecords[recordIndex].status = Math.random() > 0.2 ? 'completed' : 'failed';
          if (mockSalaryRecords[recordIndex].status === 'completed') {
            mockSalaryRecords[recordIndex].paymentDate = new Date().toISOString().split('T')[0];
          }
        }, 3000);
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('重试薪资发放失败:', error);
      throw error;
    }
  };

  // 删除薪资记录
  const deleteSalary = async (id: string): Promise<boolean> => {
    try {
      // 模拟API调用
      await delay(500);
      
      // 这里应该是实际API调用，目前使用模拟数据模拟
      const recordIndex = mockSalaryRecords.findIndex(record => record.id === id);
      
      if (recordIndex >= 0) {
        // 模拟删除
        mockSalaryRecords.splice(recordIndex, 1);
        
        // 更新本地状态
        const localIndex = salaryRecords.value.findIndex(record => record.id === id);
        if (localIndex >= 0) {
          salaryRecords.value.splice(localIndex, 1);
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('删除薪资记录失败:', error);
      throw error;
    }
  };

  // 创建新的薪资记录
  const createSalary = async (salaryData: Omit<SalaryRecord, 'id' | 'status' | 'createdAt' | 'updatedAt'>): Promise<SalaryRecord> => {
    try {
      // 模拟API调用
      await delay(800);
      
      // 这里应该是实际API调用，目前使用模拟数据模拟
      const newSalary: SalaryRecord = {
        ...salaryData,
        id: `s-${Date.now()}`,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // 添加到模拟数据
      mockSalaryRecords.unshift(newSalary);
      
      return newSalary;
    } catch (error) {
      console.error('创建薪资记录失败:', error);
      throw error;
    }
  };

  // 批量创建薪资记录
  const batchCreateSalary = async (month: string, departmentId?: string): Promise<number> => {
    try {
      // 模拟API调用
      await delay(1200);
      
      // 在实际应用中，这里应该调用后端API来批量生成薪资记录
      const employeeStore = useEmployeeStore();
      await employeeStore.fetchEmployees();
      let employees = employeeStore.employees;
      
      // 如果指定了部门，则筛选部门员工
      if (departmentId) {
        employees = employees.filter(emp => emp.departmentId === departmentId);
      }
      
      // 检查是否已经存在该月份的薪资记录
      const existingEmployeeIds = mockSalaryRecords
        .filter(record => record.month === month)
        .map(record => record.employeeId);
      
      // 过滤掉已经有薪资记录的员工
      const newEmployees = employees.filter(emp => !existingEmployeeIds.includes(emp.id));
      
      // 批量创建薪资记录
      const newRecords: SalaryRecord[] = [];
      
      for (const emp of newEmployees) {
        const baseSalary = emp.baseSalary || 5000 + Math.floor(Math.random() * 5000);
        const tax = Math.round(baseSalary * 0.1);
        const insurance = Math.round(baseSalary * 0.08);
        const providentFund = Math.round(baseSalary * 0.07);
        const actualAmount = baseSalary - tax - insurance - providentFund;
        
        const newSalary: SalaryRecord = {
          id: `s-${Date.now()}-${emp.id}`,
          employeeId: emp.id,
          employeeName: emp.name,
          departmentId: emp.departmentId,
          month,
          baseSalary,
          tax,
          insurance,
          providentFund,
          actualAmount,
          status: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        newRecords.push(newSalary);
        mockSalaryRecords.unshift(newSalary);
      }
      
      message.success(`成功生成${newRecords.length}条薪资记录`);
      return newRecords.length;
    } catch (error) {
      console.error('批量创建薪资记录失败:', error);
      throw error;
    }
  };

  return {
    salaryRecords,
    loading,
    statistics,
    fetchSalaryList,
    getSalaryStatistics,
    getSalaryById,
    processSalary,
    retrySalary,
    deleteSalary,
    createSalary,
    batchCreateSalary
  };
});

// 模拟数据
const mockSalaryRecords: SalaryRecord[] = [
  {
    id: 's-001',
    employeeId: 'emp-001',
    employeeName: '张三',
    departmentId: 'dept-001',
    month: '2023-06',
    baseSalary: 8000,
    bonus: 1500,
    overtimePay: 500,
    allowances: 800,
    tax: 800,
    insurance: 640,
    providentFund: 560,
    actualAmount: 8800,
    status: 'completed',
    paymentDate: '2023-06-05',
    createdAt: '2023-06-01T08:00:00Z',
    updatedAt: '2023-06-05T14:30:00Z'
  },
  {
    id: 's-002',
    employeeId: 'emp-002',
    employeeName: '李四',
    departmentId: 'dept-002',
    month: '2023-06',
    baseSalary: 12000,
    bonus: 2000,
    tax: 1200,
    insurance: 960,
    providentFund: 840,
    actualAmount: 11000,
    status: 'completed',
    paymentDate: '2023-06-05',
    createdAt: '2023-06-01T08:10:00Z',
    updatedAt: '2023-06-05T14:35:00Z'
  },
  {
    id: 's-003',
    employeeId: 'emp-003',
    employeeName: '王五',
    departmentId: 'dept-003',
    month: '2023-06',
    baseSalary: 15000,
    tax: 1500,
    insurance: 1200,
    providentFund: 1050,
    actualAmount: 11250,
    status: 'completed',
    paymentDate: '2023-06-05',
    createdAt: '2023-06-01T08:15:00Z',
    updatedAt: '2023-06-05T14:40:00Z'
  },
  {
    id: 's-004',
    employeeId: 'emp-004',
    employeeName: '赵六',
    departmentId: 'dept-001',
    month: '2023-06',
    baseSalary: 9000,
    overtimePay: 800,
    tax: 900,
    insurance: 720,
    providentFund: 630,
    actualAmount: 7550,
    status: 'completed',
    paymentDate: '2023-06-05',
    createdAt: '2023-06-01T08:20:00Z',
    updatedAt: '2023-06-05T14:45:00Z'
  },
  {
    id: 's-005',
    employeeId: 'emp-005',
    employeeName: '钱七',
    departmentId: 'dept-002',
    month: '2023-06',
    baseSalary: 7500,
    bonus: 800,
    allowances: 500,
    tax: 750,
    insurance: 600,
    providentFund: 525,
    actualAmount: 6925,
    status: 'completed',
    paymentDate: '2023-06-05',
    createdAt: '2023-06-01T08:25:00Z',
    updatedAt: '2023-06-05T14:50:00Z'
  },
  {
    id: 's-006',
    employeeId: 'emp-001',
    employeeName: '张三',
    departmentId: 'dept-001',
    month: '2023-07',
    baseSalary: 8000,
    bonus: 2000,
    overtimePay: 600,
    allowances: 800,
    tax: 800,
    insurance: 640,
    providentFund: 560,
    actualAmount: 9400,
    status: 'completed',
    paymentDate: '2023-07-05',
    createdAt: '2023-07-01T08:00:00Z',
    updatedAt: '2023-07-05T14:30:00Z'
  },
  {
    id: 's-007',
    employeeId: 'emp-002',
    employeeName: '李四',
    departmentId: 'dept-002',
    month: '2023-07',
    baseSalary: 12000,
    bonus: 3000,
    tax: 1200,
    insurance: 960,
    providentFund: 840,
    actualAmount: 12000,
    status: 'completed',
    paymentDate: '2023-07-05',
    createdAt: '2023-07-01T08:10:00Z',
    updatedAt: '2023-07-05T14:35:00Z'
  },
  {
    id: 's-008',
    employeeId: 'emp-003',
    employeeName: '王五',
    departmentId: 'dept-003',
    month: '2023-07',
    baseSalary: 15000,
    tax: 1500,
    insurance: 1200,
    providentFund: 1050,
    deductions: 500,
    actualAmount: 10750,
    status: 'completed',
    paymentDate: '2023-07-05',
    createdAt: '2023-07-01T08:15:00Z',
    updatedAt: '2023-07-05T14:40:00Z'
  },
  {
    id: 's-009',
    employeeId: 'emp-004',
    employeeName: '赵六',
    departmentId: 'dept-001',
    month: '2023-07',
    baseSalary: 9000,
    tax: 900,
    insurance: 720,
    providentFund: 630,
    actualAmount: 6750,
    status: 'completed',
    paymentDate: '2023-07-05',
    createdAt: '2023-07-01T08:20:00Z',
    updatedAt: '2023-07-05T14:45:00Z'
  },
  {
    id: 's-010',
    employeeId: 'emp-005',
    employeeName: '钱七',
    departmentId: 'dept-002',
    month: '2023-07',
    baseSalary: 7500,
    bonus: 1000,
    allowances: 500,
    tax: 750,
    insurance: 600,
    providentFund: 525,
    actualAmount: 7125,
    status: 'completed',
    paymentDate: '2023-07-05',
    createdAt: '2023-07-01T08:25:00Z',
    updatedAt: '2023-07-05T14:50:00Z'
  },
  {
    id: 's-011',
    employeeId: 'emp-001',
    employeeName: '张三',
    departmentId: 'dept-001',
    month: '2023-08',
    baseSalary: 8500,
    bonus: 1000,
    overtimePay: 400,
    allowances: 800,
    tax: 850,
    insurance: 680,
    providentFund: 595,
    actualAmount: 8575,
    status: 'pending',
    createdAt: '2023-08-01T08:00:00Z',
    updatedAt: '2023-08-01T08:00:00Z'
  },
  {
    id: 's-012',
    employeeId: 'emp-002',
    employeeName: '李四',
    departmentId: 'dept-002',
    month: '2023-08',
    baseSalary: 12000,
    tax: 1200,
    insurance: 960,
    providentFund: 840,
    actualAmount: 9000,
    status: 'pending',
    createdAt: '2023-08-01T08:10:00Z',
    updatedAt: '2023-08-01T08:10:00Z'
  },
  {
    id: 's-013',
    employeeId: 'emp-003',
    employeeName: '王五',
    departmentId: 'dept-003',
    month: '2023-08',
    baseSalary: 16000,
    tax: 1600,
    insurance: 1280,
    providentFund: 1120,
    actualAmount: 12000,
    status: 'failed',
    createdAt: '2023-08-01T08:15:00Z',
    updatedAt: '2023-08-05T14:40:00Z'
  },
  {
    id: 's-014',
    employeeId: 'emp-004',
    employeeName: '赵六',
    departmentId: 'dept-001',
    month: '2023-08',
    baseSalary: 9000,
    overtimePay: 1000,
    tax: 900,
    insurance: 720,
    providentFund: 630,
    actualAmount: 7750,
    status: 'processing',
    createdAt: '2023-08-01T08:20:00Z',
    updatedAt: '2023-08-05T14:45:00Z'
  },
  {
    id: 's-015',
    employeeId: 'emp-005',
    employeeName: '钱七',
    departmentId: 'dept-002',
    month: '2023-08',
    baseSalary: 7500,
    bonus: 500,
    allowances: 500,
    tax: 750,
    insurance: 600,
    providentFund: 525,
    actualAmount: 6625,
    status: 'pending',
    createdAt: '2023-08-01T08:25:00Z',
    updatedAt: '2023-08-01T08:25:00Z'
  }
]; 