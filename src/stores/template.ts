import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// 模板状态枚举
export enum TemplateStatus {
  ENABLED = 'enabled', // 启用
  DISABLED = 'disabled' // 禁用
}

// 模板类型枚举
export enum TemplateNature {
  CONTRACT = 'contract', // 合同
  OTHER = 'other' // 其他文件用印
}

// 合同分类枚举
export enum ContractCategory {
  SALES = 'sales', // 销售合同
  PURCHASE = 'purchase', // 采购合同
  SERVICE = 'service', // 服务合同
  COOPERATION = 'cooperation', // 合作协议
  CONFIDENTIALITY = 'confidentiality', // 保密协议
  OTHER = 'other' // 其他
}

// 合同性质枚举
export enum ContractNature {
  FRAMEWORK = 'framework', // 框架协议
  NORMAL = 'normal' // 普通合同
}

// 编辑模式枚举
export enum EditMode {
  FULL = 'full', // 全文编辑
  FILL = 'fill' // 填空编辑
}

// 模板接口定义
export interface Template {
  id: string;
  code: string; // 模板编码
  name: string; // 模板名称
  status: TemplateStatus; // 模板状态
  nature: TemplateNature; // 模板性质
  category: ContractCategory; // 合同分类
  contractNature: ContractNature; // 合同性质
  editMode: EditMode; // 编辑模式
  source: string; // 模板来源
  version: number; // 当前有效版本
  createdBy: string; // 模板发起人
  remark?: string; // 模板备注
  content: string; // 模板内容
  createdAt: string;
  updatedAt: string;
}

// 模板版本接口
export interface TemplateVersion {
  id: string;
  templateId: string;
  version: number;
  content: string;
  createdBy: string;
  createdAt: string;
  isActive: boolean;
}

// 使用组合式API定义模板Store
export const useTemplateStore = defineStore('template', () => {
  // 状态
  const templateList = ref<Template[]>([]);
  const currentTemplate = ref<Template | null>(null);
  const templateVersions = ref<TemplateVersion[]>([]);
  const loading = ref(false);

  // Getters
  const getTemplateById = computed(() => {
    return (id: string) => templateList.value.find(template => template.id === id) || null;
  });

  const getTemplateByCode = computed(() => {
    return (code: string) => templateList.value.find(template => template.code === code) || null;
  });

  const getEnabledTemplates = computed(() => {
    return templateList.value.filter(template => template.status === TemplateStatus.ENABLED);
  });

  const getTemplateVersions = computed(() => {
    return (templateId: string) => templateVersions.value.filter(version => version.templateId === templateId);
  });

  // Actions
  // 获取模板列表
  const fetchTemplates = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟数据 - 实际开发中应替换为API调用
      templateList.value = generateMockTemplates();
      
      loading.value = false;
      return templateList.value;
    } catch (error) {
      console.error('获取模板列表失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 获取模板详情
  const fetchTemplateById = async (id: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 查找现有模板或生成模拟数据
      const template = templateList.value.find(t => t.id === id);
      if (template) {
        currentTemplate.value = template;
      } else {
        const mockTemplate = generateMockTemplates().find(t => t.id === id);
        if (mockTemplate) {
          currentTemplate.value = mockTemplate;
          templateList.value.push(mockTemplate);
        }
      }
      
      // 同时获取版本历史
      await fetchTemplateVersions(id);
      
      loading.value = false;
      return currentTemplate.value;
    } catch (error) {
      console.error('获取模板详情失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 获取模板版本历史
  const fetchTemplateVersions = async (templateId: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // 模拟数据 - 实际开发中应替换为API调用
      const versions = generateMockTemplateVersions(templateId);
      
      // 更新版本历史
      const existingVersions = templateVersions.value.filter(v => v.templateId !== templateId);
      templateVersions.value = [...existingVersions, ...versions];
      
      loading.value = false;
      return versions;
    } catch (error) {
      console.error('获取模板版本历史失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 创建新模板
  const createTemplate = async (templateData: Partial<Template>) => {
    loading.value = true;
    try {
      // 验证模板编码唯一性
      if (templateData.code && getTemplateByCode.value(templateData.code)) {
        throw new Error('模板编码已存在，请使用其他编码');
      }
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newId = `T${Date.now().toString().slice(-8)}`;
      const newTemplate: Template = {
        id: newId,
        code: templateData.code || `TPL-${new Date().getFullYear()}-${newId}`,
        name: templateData.name || '未命名模板',
        status: templateData.status || TemplateStatus.ENABLED,
        nature: templateData.nature || TemplateNature.CONTRACT,
        category: templateData.category || ContractCategory.SALES,
        contractNature: templateData.contractNature || ContractNature.NORMAL,
        editMode: templateData.editMode || EditMode.FULL,
        source: templateData.source || '内部创建',
        version: 1, // 初始版本
        createdBy: '当前用户',
        remark: templateData.remark || '',
        content: templateData.content || '<p>模板内容</p>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      templateList.value.push(newTemplate);
      currentTemplate.value = newTemplate;
      
      // 创建初始版本记录
      const initialVersion: TemplateVersion = {
        id: `TV-${Date.now().toString().slice(-6)}`,
        templateId: newTemplate.id,
        version: 1,
        content: newTemplate.content,
        createdBy: '当前用户',
        createdAt: new Date().toISOString(),
        isActive: true
      };
      
      templateVersions.value.push(initialVersion);
      
      loading.value = false;
      return newTemplate;
    } catch (error) {
      console.error('创建模板失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 更新模板
  const updateTemplate = async (id: string, updateData: Partial<Template>, createNewVersion = false) => {
    loading.value = true;
    try {
      // 检查是否更改了编码，如果是，验证唯一性
      if (updateData.code) {
        const existingTemplate = getTemplateByCode.value(updateData.code);
        if (existingTemplate && existingTemplate.id !== id) {
          throw new Error('模板编码已存在，请使用其他编码');
        }
      }
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = templateList.value.findIndex(t => t.id === id);
      if (index !== -1) {
        // 如果创建新版本，增加版本号
        let newVersion = templateList.value[index].version;
        if (createNewVersion && updateData.content) {
          newVersion += 1;
          
          // 创建新版本记录
          const newVersionRecord: TemplateVersion = {
            id: `TV-${Date.now().toString().slice(-6)}`,
            templateId: id,
            version: newVersion,
            content: updateData.content,
            createdBy: '当前用户',
            createdAt: new Date().toISOString(),
            isActive: true
          };
          
          // 将之前的活动版本设为非活动
          templateVersions.value = templateVersions.value.map(v => {
            if (v.templateId === id && v.isActive) {
              return { ...v, isActive: false };
            }
            return v;
          });
          
          // 添加新版本
          templateVersions.value.push(newVersionRecord);
        }
        
        templateList.value[index] = {
          ...templateList.value[index],
          ...updateData,
          version: newVersion,
          updatedAt: new Date().toISOString()
        };
        
        if (currentTemplate.value?.id === id) {
          currentTemplate.value = templateList.value[index];
        }
      }
      
      loading.value = false;
      return templateList.value[index];
    } catch (error) {
      console.error('更新模板失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 切换模板状态（启用/禁用）
  const toggleTemplateStatus = async (id: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = templateList.value.findIndex(t => t.id === id);
      if (index !== -1) {
        const newStatus = templateList.value[index].status === TemplateStatus.ENABLED 
          ? TemplateStatus.DISABLED 
          : TemplateStatus.ENABLED;
        
        templateList.value[index] = {
          ...templateList.value[index],
          status: newStatus,
          updatedAt: new Date().toISOString()
        };
        
        if (currentTemplate.value?.id === id) {
          currentTemplate.value = templateList.value[index];
        }
      }
      
      loading.value = false;
      return templateList.value[index];
    } catch (error) {
      console.error('切换模板状态失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 删除模板
  const deleteTemplate = async (id: string) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 400));
      
      // 从列表中移除
      templateList.value = templateList.value.filter(t => t.id !== id);
      
      // 如果当前模板是被删除的模板，清空当前模板
      if (currentTemplate.value?.id === id) {
        currentTemplate.value = null;
      }
      
      // 同时删除相关版本历史
      templateVersions.value = templateVersions.value.filter(v => v.templateId !== id);
      
      loading.value = false;
      return true;
    } catch (error) {
      console.error('删除模板失败:', error);
      loading.value = false;
      throw error;
    }
  };

  // 清理当前模板数据 (用于页面切换或退出)
  const clearCurrentTemplate = () => {
    currentTemplate.value = null;
  };

  // 查找重复编码
  const checkCodeUniqueness = async (code: string, excludeId?: string) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const template = templateList.value.find(t => t.code === code && t.id !== excludeId);
    return !template; // 返回true表示编码唯一，false表示已存在
  };

  // 生成模拟模板数据
  const generateMockTemplates = (): Template[] => {
    const templates = [
      {
        id: 'T1001',
        code: 'TPL-2023-001',
        name: '销售合同模板',
        status: TemplateStatus.ENABLED,
        nature: TemplateNature.CONTRACT,
        category: ContractCategory.SALES,
        contractNature: ContractNature.NORMAL,
        editMode: EditMode.FILL,
        source: '法务部',
        version: 3,
        createdBy: '管理员',
        remark: '适用于一般销售业务',
        content: '<h1>销售合同</h1><p>甲方: [甲方名称]</p><p>乙方: [乙方名称]</p><p>1. 销售内容及金额: [内容描述]，金额：[合同金额]元</p><p>2. 交付时间: [交付时间]</p><p>3. 付款方式: [付款方式]</p>',
        createdAt: '2023-01-15T08:30:00Z',
        updatedAt: '2023-05-20T14:45:00Z'
      },
      {
        id: 'T1002',
        code: 'TPL-2023-002',
        name: '采购合同模板',
        status: TemplateStatus.ENABLED,
        nature: TemplateNature.CONTRACT,
        category: ContractCategory.PURCHASE,
        contractNature: ContractNature.NORMAL,
        editMode: EditMode.FULL,
        source: '采购部',
        version: 2,
        createdBy: '采购经理',
        remark: '适用于标准采购业务',
        content: '<h1>采购合同</h1><p>甲方: 我方公司</p><p>乙方: 供应商</p><p>1. 采购内容及金额: 详见附件，金额：XXX元</p><p>2. 交付时间: XXX</p><p>3. 验收标准: XXX</p><p>4. 付款方式: XXX</p>',
        createdAt: '2023-02-10T10:15:00Z',
        updatedAt: '2023-06-05T09:30:00Z'
      },
      {
        id: 'T1003',
        code: 'TPL-2023-003',
        name: '服务合同模板',
        status: TemplateStatus.ENABLED,
        nature: TemplateNature.CONTRACT,
        category: ContractCategory.SERVICE,
        contractNature: ContractNature.NORMAL,
        editMode: EditMode.FILL,
        source: '法务部',
        version: 1,
        createdBy: '管理员',
        remark: '适用于常规服务业务',
        content: '<h1>服务合同</h1><p>甲方: [甲方名称]</p><p>乙方: [乙方名称]</p><p>1. 服务内容: [服务描述]</p><p>2. 服务周期: [开始日期]至[结束日期]</p><p>3. 服务标准: [标准描述]</p><p>4. 服务费用: [费用金额]元</p><p>5. 付款方式: [付款方式]</p>',
        createdAt: '2023-03-05T13:20:00Z',
        updatedAt: '2023-03-05T13:20:00Z'
      },
      {
        id: 'T1004',
        code: 'TPL-2023-004',
        name: '框架协议模板',
        status: TemplateStatus.ENABLED,
        nature: TemplateNature.CONTRACT,
        category: ContractCategory.COOPERATION,
        contractNature: ContractNature.FRAMEWORK,
        editMode: EditMode.FULL,
        source: '业务部',
        version: 2,
        createdBy: '业务总监',
        remark: '长期合作伙伴框架协议',
        content: '<h1>框架合作协议</h1><p>甲方: 我方公司</p><p>乙方: 合作方</p><p>1. 合作范围: XXX</p><p>2. 合作期限: XXX</p><p>3. 合作方式: XXX</p><p>4. 结算方式: XXX</p><p>5. 保密条款: XXX</p>',
        createdAt: '2023-01-20T09:00:00Z',
        updatedAt: '2023-07-12T11:25:00Z'
      },
      {
        id: 'T1005',
        code: 'TPL-2023-005',
        name: '保密协议模板',
        status: TemplateStatus.ENABLED,
        nature: TemplateNature.CONTRACT,
        category: ContractCategory.CONFIDENTIALITY,
        contractNature: ContractNature.NORMAL,
        editMode: EditMode.FILL,
        source: '法务部',
        version: 1,
        createdBy: '法务经理',
        remark: '标准保密协议',
        content: '<h1>保密协议</h1><p>甲方: [甲方名称]</p><p>乙方: [乙方名称]</p><p>1. 保密内容: [保密信息范围]</p><p>2. 保密期限: [保密期限]</p><p>3. 违约责任: [违约责任描述]</p>',
        createdAt: '2023-04-18T15:40:00Z',
        updatedAt: '2023-04-18T15:40:00Z'
      },
      {
        id: 'T1006',
        code: 'TPL-2023-006',
        name: '其他用印模板',
        status: TemplateStatus.DISABLED,
        nature: TemplateNature.OTHER,
        category: ContractCategory.OTHER,
        contractNature: ContractNature.NORMAL,
        editMode: EditMode.FULL,
        source: '行政部',
        version: 1,
        createdBy: '行政经理',
        remark: '非合同类文件用印模板',
        content: '<h1>证明文件</h1><p>兹证明XXX是我公司员工...</p>',
        createdAt: '2023-05-22T11:10:00Z',
        updatedAt: '2023-05-22T11:10:00Z'
      }
    ];
    
    return templates as Template[];
  };

  // 生成模拟版本历史
  const generateMockTemplateVersions = (templateId: string): TemplateVersion[] => {
    const template = templateList.value.find(t => t.id === templateId);
    if (!template) return [];
    
    const versions: TemplateVersion[] = [];
    
    // 生成历史版本记录
    for (let i = 1; i <= template.version; i++) {
      const versionDate = new Date(
        new Date(template.createdAt).getTime() + (i - 1) * 30 * 86400000
      ).toISOString();
      
      versions.push({
        id: `TV-${templateId}-${i}`,
        templateId,
        version: i,
        content: `${template.content} (版本 ${i})`,
        createdBy: i === 1 ? template.createdBy : '修订人员',
        createdAt: versionDate,
        isActive: i === template.version // 只有最新版本是激活的
      });
    }
    
    return versions;
  };

  return {
    // 状态
    templateList,
    currentTemplate,
    templateVersions,
    loading,
    
    // Getters
    getTemplateById,
    getTemplateByCode,
    getEnabledTemplates,
    getTemplateVersions,
    
    // Actions
    fetchTemplates,
    fetchTemplateById,
    fetchTemplateVersions,
    createTemplate,
    updateTemplate,
    toggleTemplateStatus,
    deleteTemplate,
    clearCurrentTemplate,
    checkCodeUniqueness
  };
}); 