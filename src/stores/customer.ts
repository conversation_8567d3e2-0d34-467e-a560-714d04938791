import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  CustomerType, 
  CustomerStatus, 
  type Customer,
  type CustomerInvoiceInfo,
  type CustomerContact,
  type CustomerStats,
  IndustryType,
  type CustomerSource
} from '@/types/customer';
import { CITY_OPTIONS } from '@/constants/customer';

// Store definition
export const useCustomerStore = defineStore('customer', () => {
  // State
  const customers = ref<Customer[]>([]);
  const customerInvoiceInfos = ref<CustomerInvoiceInfo[]>([]);
  const customerContacts = ref<CustomerContact[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const stats = ref<CustomerStats>({
    total: 0,
    growthRate: 0,
    newMonthly: 0,
    newGrowth: 0,
    formalCount: 0,
    formalRate: 0,
    avgConvertDays: 0,
    convertDaysChange: 0
  });

  // Getters
  const getCustomerById = computed(() => {
    return (id: string) => customers.value.find(c => c.id === id);
  });

  const getCustomersByType = computed(() => {
    return (type: CustomerType) => customers.value.filter(c => c.type === type);
  });

  const getCustomersByStatus = computed(() => {
    return (status: CustomerStatus) => customers.value.filter(c => c.status === status);
  });

  const getCustomersByIndustry = computed(() => {
    return (industry: IndustryType) => customers.value.filter(c => c.industry === industry);
  });

  const getCustomersByOwner = computed(() => {
    return (ownerId: string) => customers.value.filter(c => c.owner === ownerId);
  });

  const getInvoiceInfoByCustomerId = computed(() => {
    return (customerId: string) => customerInvoiceInfos.value.find(info => info.customerId === customerId);
  });

  const getContactsByCustomerId = computed(() => {
    return (customerId: string) => {
      return customerContacts.value
        .filter(contact => contact.customerId === customerId)
        .sort((a, b) => dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf());
    };
  });

  // Actions
  async function fetchCustomers(params?: any) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (customers.value.length === 0) {
        // Generate mock data if no data exists
        generateMockCustomers();
      }
      
      // Filter based on provided params
      let result = [...customers.value];
      
      if (params) {
        if (params.name) {
          result = result.filter(c => c.name.includes(params.name));
        }
        if (params.code) {
          result = result.filter(c => c.code.includes(params.code));
        }
        if (params.taxId) {
          result = result.filter(c => c.taxId.includes(params.taxId));
        }
        if (params.type) {
          result = result.filter(c => c.type === params.type);
        }
        if (params.status) {
          result = result.filter(c => c.status === params.status);
        }
        if (params.industry) {
          result = result.filter(c => c.industry === params.industry);
        }
        if (params.owner) {
          result = result.filter(c => c.owner === params.owner);
        }
        if (params.dateRange && params.dateRange.length === 2) {
          const startDate = dayjs(params.dateRange[0]);
          const endDate = dayjs(params.dateRange[1]);
          
          result = result.filter(c => {
            const createDate = dayjs(c.createTime);
            return (createDate.isAfter(startDate) || createDate.isSame(startDate)) && 
                   (createDate.isBefore(endDate) || createDate.isSame(endDate));
          });
        }
      }
      
      return {
        total: result.length,
        data: result
      };
    } catch (err: any) {
      error.value = err.message || '获取客户列表失败';
      message.error(error.value);
      return {
        total: 0,
        data: []
      };
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchCustomerById(id: string) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const customer = customers.value.find(c => c.id === id);
      
      if (!customer) {
        throw new Error('客户不存在');
      }
      
      return customer;
    } catch (err: any) {
      error.value = err.message || '获取客户详情失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  async function createCustomer(data: Partial<Customer>) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      const newCustomer: Customer = {
        id: `C${Date.now()}`,
        code: generateCustomerCode(data.type || CustomerType.ENTERPRISE),
        name: data.name || '',
        taxId: data.taxId || '',
        type: data.type || CustomerType.ENTERPRISE,
        status: data.status || CustomerStatus.NEW,
        industry: data.industry || IndustryType.OTHER_UNCATEGORIZED,
        contact: data.contact || '',
        phone: data.phone || '',
        email: data.email || '',
        address: data.address || '',
        source: data.source || 'website',
        owner: data.owner || '1',
        ownerName: data.ownerName || '张三', // Mock owner name
        createTime: now,
        updateTime: now,
        lastContactTime: now,
        remark: data.remark || '',
      };
      
      customers.value.push(newCustomer);
      updateStats();
      
      message.success('客户创建成功');
      return newCustomer;
    } catch (err: any) {
      error.value = err.message || '创建客户失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  async function updateCustomer(id: string, data: Partial<Customer>) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = customers.value.findIndex(c => c.id === id);
      
      if (index === -1) {
        throw new Error('客户不存在');
      }
      
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      
      // Update customer data
      customers.value[index] = {
        ...customers.value[index],
        ...data,
        updateTime: now
      };
      
      updateStats();
      
      message.success('客户更新成功');
      return customers.value[index];
    } catch (err: any) {
      error.value = err.message || '更新客户失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  async function deleteCustomer(id: string) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = customers.value.findIndex(c => c.id === id);
      
      if (index === -1) {
        throw new Error('客户不存在');
      }
      
      customers.value.splice(index, 1);
      
      // Also delete related data
      customerInvoiceInfos.value = customerInvoiceInfos.value.filter(info => info.customerId !== id);
      customerContacts.value = customerContacts.value.filter(contact => contact.customerId !== id);
      
      updateStats();
      
      message.success('客户删除成功');
      return true;
    } catch (err: any) {
      error.value = err.message || '删除客户失败';
      message.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchCustomerInvoiceInfo(customerId: string) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      let invoiceInfo = customerInvoiceInfos.value.find(info => info.customerId === customerId);
      
      if (!invoiceInfo) {
        // If no invoice info exists, create one based on customer info
        const customer = customers.value.find(c => c.id === customerId);
        
        if (!customer) {
          throw new Error('客户不存在');
        }
        
        invoiceInfo = {
          id: `INV${Date.now()}`,
          customerId,
          companyName: customer.name,
          taxId: customer.taxId,
          bank: '',
          bankAccount: '',
          address: customer.address,
          phone: customer.phone,
          remark: ''
        };
        
        customerInvoiceInfos.value.push(invoiceInfo);
      }
      
      return invoiceInfo;
    } catch (err: any) {
      error.value = err.message || '获取客户开票信息失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  async function updateCustomerInvoiceInfo(customerId: string, data: Partial<CustomerInvoiceInfo>) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let index = customerInvoiceInfos.value.findIndex(info => info.customerId === customerId);
      
      if (index === -1) {
        // If no invoice info exists, create one
        const newInvoiceInfo: CustomerInvoiceInfo = {
          id: `INV${Date.now()}`,
          customerId,
          companyName: data.companyName || '',
          taxId: data.taxId || '',
          bank: data.bank || '',
          bankAccount: data.bankAccount || '',
          address: data.address || '',
          phone: data.phone || '',
          remark: data.remark || ''
        };
        
        customerInvoiceInfos.value.push(newInvoiceInfo);
        message.success('客户开票信息创建成功');
        return newInvoiceInfo;
      } else {
        // Update existing invoice info
        customerInvoiceInfos.value[index] = {
          ...customerInvoiceInfos.value[index],
          ...data
        };
        
        message.success('客户开票信息更新成功');
        return customerInvoiceInfos.value[index];
      }
    } catch (err: any) {
      error.value = err.message || '更新客户开票信息失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  async function createCustomerContact(data: Partial<CustomerContact>) {
    loading.value = true;
    error.value = null;
    
    try {
      // Validate required fields
      if (!data.customerId || !data.content || !data.method) {
        throw new Error('缺少必填字段');
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      const newContact: CustomerContact = {
        id: `CONT${Date.now()}`,
        customerId: data.customerId,
        content: data.content || '',
        method: data.method || '',
        contactTime: data.contactTime || now,
        creator: data.creator || '当前用户',
        creatorId: data.creatorId || '1',
        nextContactTime: data.nextContactTime || '',
        status: data.status || '',
        createTime: now
      };
      
      customerContacts.value.push(newContact);
      
      // Update customer's last contact time and status if provided
      if (data.customerId) {
        const customer = customers.value.find(c => c.id === data.customerId);
        if (customer) {
          customer.lastContactTime = now;
          if (data.status && data.status !== '') {
            customer.status = data.status as CustomerStatus;
          }
          customer.updateTime = now;
        }
      }
      
      message.success('联系记录添加成功');
      return newContact;
    } catch (err: any) {
      error.value = err.message || '添加联系记录失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchCustomerContacts(customerId: string) {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const contacts = customerContacts.value
        .filter(contact => contact.customerId === customerId)
        .sort((a, b) => dayjs(b.createTime).valueOf() - dayjs(a.createTime).valueOf());
      
      return contacts;
    } catch (err: any) {
      error.value = err.message || '获取联系记录失败';
      message.error(error.value);
      return [];
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchCustomerStats() {
    loading.value = true;
    error.value = null;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      updateStats();
      return stats.value;
    } catch (err: any) {
      error.value = err.message || '获取客户统计数据失败';
      message.error(error.value);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  // Helper functions
  function updateStats() {
    const now = dayjs();
    const monthStart = now.startOf('month');
    const lastMonthStart = now.subtract(1, 'month').startOf('month');
    
    const total = customers.value.length;
    const formalCount = customers.value.filter(c => c.type === CustomerType.ENTERPRISE || c.type === CustomerType.GOVERNMENT).length;
    const newMonthly = customers.value.filter(c => dayjs(c.createTime).isAfter(monthStart)).length;
    const lastMonthNewCount = customers.value.filter(c => {
      const createTime = dayjs(c.createTime);
      return createTime.isAfter(lastMonthStart) && createTime.isBefore(monthStart);
    }).length;
    
    const newGrowth = lastMonthNewCount === 0 ? 100 : ((newMonthly - lastMonthNewCount) / lastMonthNewCount * 100);
    
    stats.value = {
      total,
      growthRate: Math.round((total > 0 ? (newMonthly / total * 100) : 0) * 10) / 10,
      newMonthly,
      newGrowth: Math.round(newGrowth * 10) / 10,
      formalCount,
      formalRate: Math.round((total > 0 ? (formalCount / total * 100) : 0) * 10) / 10,
      avgConvertDays: 45, // Mock data
      convertDaysChange: -3 // Mock data
    };
  }
  
  function generateMockCustomers() {
    const customerNames = [
      '北京未来科技有限公司',
      '上海数创信息技术有限公司', 
      '广州智联网络科技有限公司',
      '深圳星辰科技有限公司',
      '杭州云智信息技术有限公司',
      '南京智能科技有限公司',
      '成都创新科技有限公司',
      '重庆先锋技术有限公司',
      '武汉智慧科技有限公司',
      '天津领航信息技术有限公司'
    ];
    
    const generateRandomString = (length: number, onlyNumbers = false) => {
      const chars = onlyNumbers ? '0123456789' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };
    
    const now = dayjs();
    
    // Generate 50 mock customers
    customers.value = Array.from({ length: 50 }).map((_, index) => {
      const type = [CustomerType.ENTERPRISE, CustomerType.GOVERNMENT][Math.floor(Math.random() * 2)];
      const status = [
        CustomerStatus.NEW,
        CustomerStatus.FOLLOWING,
        CustomerStatus.NEGOTIATING,
        CustomerStatus.SIGNED,
        CustomerStatus.LOST
      ][Math.floor(Math.random() * 5)];
      const industry = [
        IndustryType.INFORMATION_TECHNOLOGY,
        IndustryType.MANUFACTURING_INDUSTRIAL,
        IndustryType.FINANCIAL_SERVICES,
        IndustryType.EDUCATION_RESEARCH,
        IndustryType.HEALTHCARE_PHARMACEUTICALS,
        IndustryType.OTHER_UNCATEGORIZED
      ][Math.floor(Math.random() * 6)];
      
      const createTime = now.subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD HH:mm:ss');
      const updateTime = now.subtract(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD HH:mm:ss');
      const lastContactTime = now.subtract(Math.floor(Math.random() * 60), 'day').format('YYYY-MM-DD');
      
      // 随机生成地区信息
      const provinces = ['beijing', 'shanghai', 'guangdong', 'zhejiang', 'jiangsu', 'sichuan', 'hubei', 'shandong'];
      const province = provinces[Math.floor(Math.random() * provinces.length)];
      const cities = CITY_OPTIONS[province] || [];
      const city = cities.length > 0 ? cities[Math.floor(Math.random() * cities.length)].value : '';
      
      return {
        id: `C${index + 1}`,
        code: generateCustomerCode(type),
        name: customerNames[index % customerNames.length] + (Math.floor(index / customerNames.length) > 0 ? String(Math.floor(index / customerNames.length) + 1) : ''),
        taxId: `91110108MA${generateRandomString(10, true)}`,
        type,
        status,
        industry,
        contact: ['张经理', '李总', '王董', '赵主任', '钱经理'][Math.floor(Math.random() * 5)],
        phone: `1381234${String(1000 + index).padStart(4, '0')}`,
        email: `customer${index + 1}@example.com`,
        address: '北京市朝阳区xxx路xxx号',
        province,
        city,
        source: ['website', 'referral', 'exhibition', 'advertisement', 'social', 'other'][Math.floor(Math.random() * 6)],
        owner: String(Math.floor(Math.random() * 3 + 1)),
        ownerName: ['张三', '李四', '王五'][Math.floor(Math.random() * 3)],
        createTime,
        updateTime,
        lastContactTime,
        remark: '这是客户备注信息'
      };
    });
    
    // Generate invoice info for some customers
    customerInvoiceInfos.value = customers.value
      .filter((_, index) => index % 3 === 0) // Only create for 1/3 of customers
      .map(customer => ({
        id: `INV${customer.id}`,
        customerId: customer.id,
        companyName: customer.name,
        taxId: customer.taxId,
        bank: ['中国银行', '工商银行', '建设银行', '农业银行'][Math.floor(Math.random() * 4)],
        bankAccount: `6222${generateRandomString(12, true)}`,
        address: customer.address,
        phone: customer.phone,
        remark: ''
      }));
    
    // Generate contact records
    const methods = ['phone', 'visit', 'email', 'meeting', 'wechat', 'other'];
    const creators = ['张三', '李四', '王五'];
    
    customerContacts.value = [];
    
    customers.value.forEach(customer => {
      // Generate 0-5 contacts for each customer
      const contactCount = Math.floor(Math.random() * 6);
      
      for (let i = 0; i < contactCount; i++) {
        const creator = creators[Math.floor(Math.random() * creators.length)];
        const contactTime = dayjs(customer.createTime)
          .add(Math.floor(Math.random() * 30), 'day')
          .format('YYYY-MM-DD HH:mm:ss');
        
        customerContacts.value.push({
          id: `CONT${customer.id}-${i}`,
          customerId: customer.id,
          content: ['电话沟通项目进展情况', '邮件确认合同细节', '现场拜访客户', '微信沟通产品需求', '召开需求讨论会议'][Math.floor(Math.random() * 5)],
          method: methods[Math.floor(Math.random() * methods.length)],
          contactTime,
          creator,
          creatorId: String(creators.indexOf(creator) + 1),
          nextContactTime: dayjs(contactTime).add(7, 'day').format('YYYY-MM-DD'),
          status: i === contactCount - 1 ? customer.status : '',
          createTime: contactTime
        });
      }
    });
    
    // Update stats
    updateStats();
  }
  
  // 生成客户编码
  const generateCustomerCode = (type: CustomerType): string => {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // 两位年份
    const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份
    const day = now.getDate().toString().padStart(2, '0'); // 日期
    
    // 获取当天的流水号（这里简化处理，实际应该从数据库获取）
    const today = `${year}${month}${day}`;
    const todayCustomers = customers.value.filter(c => 
      c.code.startsWith(type) && c.code.slice(1, 7) === today
    );
    const sequence = (todayCustomers.length + 1).toString().padStart(3, '0');
    
    return `${type}${year}${month}${day}${sequence}`;
  };
  
  return {
    // State
    customers,
    customerInvoiceInfos,
    customerContacts,
    loading,
    error,
    stats,
    
    // Getters
    getCustomerById,
    getCustomersByType,
    getCustomersByStatus,
    getCustomersByIndustry,
    getCustomersByOwner,
    getInvoiceInfoByCustomerId,
    getContactsByCustomerId,
    
    // Actions
    fetchCustomers,
    fetchCustomerById,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    fetchCustomerInvoiceInfo,
    updateCustomerInvoiceInfo,
    createCustomerContact,
    fetchCustomerContacts,
    fetchCustomerStats
  };
}); 