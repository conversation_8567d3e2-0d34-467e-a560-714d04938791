import { defineStore } from 'pinia';
import { getSupplierList, getSupplierDetail, createSupplier, updateSupplier, deleteSupplier } from '@/api/supplier';
import type { Supplier, SupplierDetail, SupplierQueryParams, SupplierFormData } from '@/types/supplier';
import { CustomerType, IndustryType, SupplierStatus, generateSupplierCode } from '@/constants/customer';
import dayjs from 'dayjs';

interface SupplierState {
  suppliers: Supplier[];
  allSuppliers: Supplier[];
  supplierDetail: SupplierDetail | null;
  total: number;
  loading: boolean;
}

// 生成随机字符串
function generateRandomString(length: number, numbersOnly = false): string {
  const chars = numbersOnly ? '0123456789' : '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export const useSupplierStore = defineStore('supplier', {
  state: (): SupplierState => ({
    suppliers: [],
    allSuppliers: [],
    supplierDetail: null,
    total: 0,
    loading: false,
  }),

  actions: {
    // 初始化时清除旧数据
    $reset() {
      this.suppliers = [];
      this.allSuppliers = [];
      this.supplierDetail = null;
      this.total = 0;
      this.loading = false;
    },

    async fetchSuppliers(params: SupplierQueryParams) {
      this.loading = true;
      try {
        // 如果没有模拟数据，生成一些
        if (this.allSuppliers.length === 0) {
          this.generateMockSuppliers();
        }

        let filteredSuppliers = [...this.allSuppliers];

        // 关键词搜索
        if (params.keyword) {
          const keyword = params.keyword.toLowerCase();
          filteredSuppliers = filteredSuppliers.filter(supplier => 
            supplier.name.toLowerCase().includes(keyword) ||
            supplier.code.toLowerCase().includes(keyword) ||
            supplier.contact.toLowerCase().includes(keyword)
          );
        }

        // 类型筛选
        if (params.type) {
          filteredSuppliers = filteredSuppliers.filter(supplier => supplier.type === params.type);
        }

        // 状态筛选
        if (params.status) {
          filteredSuppliers = filteredSuppliers.filter(supplier => supplier.status === params.status);
        }

        // 行业筛选
        if (params.industry) {
          filteredSuppliers = filteredSuppliers.filter(supplier => supplier.industry === params.industry);
        }

        // 分页处理
        const current = params.current || 1;
        const pageSize = params.pageSize || 10;
        const start = (current - 1) * pageSize;
        const end = start + pageSize;
        
        this.suppliers = filteredSuppliers.slice(start, end);
        this.total = filteredSuppliers.length;
        
        return {
          data: this.suppliers,
          total: this.total
        };
      } catch (error) {
        console.error('获取供应商列表失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchSupplierDetail(id: string) {
      this.loading = true;
      try {
        // 如果没有模拟数据，生成一些
        if (this.allSuppliers.length === 0) {
          this.generateMockSuppliers();
        }
        
        // 从本地数据中查找供应商
        const supplier = this.allSuppliers.find(s => s.id === id);
        if (supplier) {
          // 创建完整的SupplierDetail对象
          const supplierDetail = {
            ...supplier,
            statistics: {
              projectCount: Math.floor(Math.random() * 10) + 1,
              contractCount: Math.floor(Math.random() * 5) + 1,
              totalAmount: Math.floor(Math.random() * 1000000) + 100000,
              qualityScore: Math.floor(Math.random() * 30) + 70,
            },
            invoiceInfo: {
              companyName: supplier.name,
              taxId: supplier.taxId || generateRandomString(18, true),
              bank: '中国银行股份有限公司',
              bankAccount: '6222' + generateRandomString(12, true),
              address: supplier.address,
              phone: supplier.phone,
              remark: '开票信息备注',
            }
          };
          
          this.supplierDetail = supplierDetail;
          return supplierDetail;
        } else {
          throw new Error('供应商不存在');
        }
      } catch (error) {
        this.supplierDetail = null;
        console.error('获取供应商详情失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async createSupplier(formData: SupplierFormData) {
      try {
        await createSupplier(formData);
        return true;
      } catch (error) {
        console.error('创建供应商失败:', error);
        throw error;
      }
    },

    async updateSupplier(id: string, formData: SupplierFormData) {
      try {
        await updateSupplier(id, formData);
        return true;
      } catch (error) {
        console.error('更新供应商失败:', error);
        throw error;
      }
    },

    async deleteSupplier(id: string) {
      try {
        await deleteSupplier(id);
        // 从本地状态中移除
        this.suppliers = this.suppliers.filter(s => s.id !== id);
        return true;
      } catch (error) {
        console.error('删除供应商失败:', error);
        throw error;
      }
    },

    // 生成模拟供应商数据
    generateMockSuppliers() {
      const supplierNames = [
        '北京科技有限公司', '上海制造集团', '深圳创新科技', '广州智能设备',
        '杭州网络技术', '成都软件开发', '西安电子科技', '南京信息服务',
        '武汉工业制造', '大连海洋科技', '青岛精密制造', '苏州电子器件',
        '无锡物联网科技', '宁波港口物流', '温州轻工制造', '福州软件园',
        '厦门集成电路', '合肥新能源', '长沙智能装备', '重庆汽车零配件'
      ];

      // 省市映射
      const provinceAndCities = [
        { province: 'beijing', city: 'dongcheng' },
        { province: 'shanghai', city: 'huangpu' },
        { province: 'guangdong', city: 'guangzhou' },
        { province: 'guangdong', city: 'shenzhen' },
        { province: 'zhejiang', city: 'hangzhou' },
        { province: 'sichuan', city: 'chengdu' },
        { province: 'shaanxi', city: 'xian' },
        { province: 'jiangsu', city: 'nanjing' },
        { province: 'hubei', city: 'wuhan' },
        { province: 'liaoning', city: 'dalian' },
        { province: 'shandong', city: 'qingdao' },
        { province: 'jiangsu', city: 'suzhou' },
        { province: 'jiangsu', city: 'wuxi' },
        { province: 'zhejiang', city: 'ningbo' },
        { province: 'zhejiang', city: 'wenzhou' },
        { province: 'fujian', city: 'fuzhou' },
        { province: 'fujian', city: 'xiamen' },
        { province: 'anhui', city: 'hefei' },
        { province: 'hunan', city: 'changsha' },
        { province: 'chongqing', city: 'yuzhong' }
      ];

      this.allSuppliers = Array.from({ length: 50 }).map((_, index) => {
        const type = Math.random() > 0.8 ? CustomerType.GOVERNMENT : CustomerType.ENTERPRISE;
        const industries = Object.values(IndustryType);
        const industry = industries[Math.floor(Math.random() * industries.length)];
        const statuses = Object.values(SupplierStatus);
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const locationData = provinceAndCities[index % provinceAndCities.length];
        const createTime = dayjs().subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD HH:mm:ss');
        const updateTime = dayjs(createTime).add(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD HH:mm:ss');
        const lastContactTime = Math.random() > 0.3 
          ? dayjs().subtract(Math.floor(Math.random() * 60), 'day').format('YYYY-MM-DD HH:mm:ss')
          : undefined;

        return {
          id: `V${index + 1}`,
          code: generateSupplierCode(type),
          name: supplierNames[index % supplierNames.length] + (Math.floor(index / supplierNames.length) > 0 ? String(Math.floor(index / supplierNames.length) + 1) : ''),
          type,
          status,
          industry,
          taxId: generateRandomString(18, true),
          contact: ['张经理', '李总', '王董', '赵主任', '钱经理'][Math.floor(Math.random() * 5)],
          phone: `1381234${String(1000 + index).padStart(4, '0')}`,
          email: `supplier${index + 1}@example.com`,
          address: '北京市朝阳区xxx路xxx号',
          province: locationData.province,
          city: locationData.city,
          website: `https://www.supplier${index + 1}.com`,
          owner: String(Math.floor(Math.random() * 3 + 1)),
          ownerName: ['张三', '李四', '王五'][Math.floor(Math.random() * 3)],
          createTime,
          updateTime,
          lastContactTime,
          remark: '这是供应商备注信息'
        };
      });
    }
  },
}); 