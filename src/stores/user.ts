import { defineStore } from 'pinia';

interface UserState {
  token: string;
  userInfo: {
    id: string;
    name: string;
    avatar: string;
    role: string;
    permissions: string[];
  } | null;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    hasPermission: (state) => (permission: string) => {
      return state.userInfo?.permissions.includes(permission) || false;
    },
  },

  actions: {
    setToken(token: string) {
      this.token = token;
      localStorage.setItem('token', token);
    },

    setUserInfo(info: UserState['userInfo']) {
      this.userInfo = info;
    },

    async login(username: string, password: string) {
      try {
        // 这里添加实际的登录 API 调用
        const response = await fetch('/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username, password }),
        });
        
        const data = await response.json();
        if (data.code === 200) {
          this.setToken(data.data.token);
          this.setUserInfo(data.data.userInfo);
          return true;
        }
        return false;
      } catch (error) {
        console.error('登录失败:', error);
        return false;
      }
    },

    logout() {
      this.token = '';
      this.userInfo = null;
      localStorage.removeItem('token');
      // 这里可以添加其他清理逻辑
    },
  },
}); 