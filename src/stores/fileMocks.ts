import dayjs from 'dayjs';
import { 
  type File, type BusinessFile, type IntellectualPropertyFile, 
  type CertificateFile, type LegalFile, type FileVersion,
  type Permission, type FileOperationLog, type FileStatistics,
  FileType, FileCategory, FileStatus, IPStatus, BusinessFileType,
  IntellectualPropertyType, PermissionType, CodeReviewStatus
} from '@/types/file';

// 生成模拟文件数据
export function generateMockFiles(count = 50): File[] {
  const files: File[] = [];
  
  const fileTypes = Object.values(FileType);
  const fileCategories = Object.values(FileCategory);
  const fileStatuses = Object.values(FileStatus);
  
  for (let i = 0; i < count; i++) {
    const id = `F${1000 + i}`;
    const category = fileCategories[Math.floor(Math.random() * fileCategories.length)];
    const type = fileTypes[Math.floor(Math.random() * fileTypes.length)];
    
    // 基础文件
    const baseFile: File = {
      id,
      name: getRandomFileName(category, type),
      code: `FILE-${10000 + i}`,
      type,
      category,
      size: Math.floor(Math.random() * 10000),
      uploadTime: dayjs().subtract(Math.floor(Math.random() * 100), 'day').format('YYYY-MM-DD HH:mm:ss'),
      path: `/files/${id}.${getFileExtension(type)}`,
      projectId: Math.random() > 0.3 ? `P${1000 + Math.floor(Math.random() * 10)}` : undefined,
      tags: getRandomTags(),
      description: Math.random() > 0.5 ? `这是一个${getFileCategoryName(category)}文件` : undefined,
      createdBy: getRandomUser(),
      updatedBy: Math.random() > 0.7 ? getRandomUser() : undefined,
      updatedTime: Math.random() > 0.7 ? dayjs().subtract(Math.floor(Math.random() * 50), 'day').format('YYYY-MM-DD HH:mm:ss') : undefined,
      status: fileStatuses[Math.floor(Math.random() * fileStatuses.length)],
      versions: Math.random() > 0.5 ? generateMockFileVersions(id, Math.floor(Math.random() * 5) + 1) : undefined,
      permissions: Math.random() > 0.7 ? generateMockFilePermissions(id, Math.floor(Math.random() * 3) + 1) : undefined
    };
    
    // 根据分类生成特定文件类型
    let specificFile: File;
    
    switch (category) {
      case FileCategory.BUSINESS:
        specificFile = generateMockBusinessFile(baseFile);
        break;
      case FileCategory.INTELLECTUAL:
        specificFile = generateMockIntellectualPropertyFile(baseFile);
        break;
      case FileCategory.CERTIFICATE:
        specificFile = generateMockCertificateFile(baseFile);
        break;
      case FileCategory.LEGAL:
        specificFile = generateMockLegalFile(baseFile);
        break;
      default:
        specificFile = baseFile;
    }
    
    files.push(specificFile);
  }
  
  return files;
}

// 生成模拟工商档案文件
function generateMockBusinessFile(baseFile: File): BusinessFile {
  const businessTypes = Object.values(BusinessFileType);
  const businessType = businessTypes[Math.floor(Math.random() * businessTypes.length)];
  
  const businessFile: BusinessFile = {
    ...baseFile,
    businessType,
    registrationDate: dayjs().subtract(Math.floor(Math.random() * 1000), 'day').format('YYYY-MM-DD'),
    registrationAuthority: getRandomAuthority(),
    expiryDate: dayjs().add(Math.floor(Math.random() * 1000), 'day').format('YYYY-MM-DD'),
    subject: Math.random() > 0.5 ? getRandomCompany() : undefined
  };
  
  return businessFile;
}

// 生成模拟知识产权文件
function generateMockIntellectualPropertyFile(baseFile: File): IntellectualPropertyFile {
  const ipTypes = Object.values(IntellectualPropertyType);
  const ipStatuses = Object.values(IPStatus);
  const ipType = ipTypes[Math.floor(Math.random() * ipTypes.length)];
  
  const intellectualFile: IntellectualPropertyFile = {
    ...baseFile,
    ipType,
    applicationNumber: `${ipType.toUpperCase()}-${Math.floor(Math.random() * 1000000)}`,
    applicationDate: dayjs().subtract(Math.floor(Math.random() * 1000), 'day').format('YYYY-MM-DD'),
    grantDate: Math.random() > 0.3 ? dayjs().subtract(Math.floor(Math.random() * 500), 'day').format('YYYY-MM-DD') : undefined,
    expiryDate: dayjs().add(Math.floor(Math.random() * 3650), 'day').format('YYYY-MM-DD'),
    ipStatus: ipStatuses[Math.floor(Math.random() * ipStatuses.length)],
    inventors: Math.random() > 0.5 ? Array(Math.floor(Math.random() * 3) + 1).fill(0).map(() => getRandomUser()) : undefined,
    annualFeeDate: Math.random() > 0.5 ? dayjs().add(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD') : undefined
  };
  
  return intellectualFile;
}

// 生成模拟资质证书文件
function generateMockCertificateFile(baseFile: File): CertificateFile {
  const certificateTypes = [
    '高新技术企业认证', 'ISO9001认证', 'ISO27001认证', 
    '软件企业认证', '双软认证', 'CMMI认证', '质量管理体系认证'
  ];
  
  const certificateFile: CertificateFile = {
    ...baseFile,
    certificateType: certificateTypes[Math.floor(Math.random() * certificateTypes.length)],
    certificateNumber: `CERT-${Math.floor(Math.random() * 1000000)}`,
    issueDate: dayjs().subtract(Math.floor(Math.random() * 1000), 'day').format('YYYY-MM-DD'),
    expiryDate: dayjs().add(Math.floor(Math.random() * 1000), 'day').format('YYYY-MM-DD'),
    issuer: getRandomCertIssuer(),
    scope: Math.random() > 0.5 ? '质量管理、信息安全' : undefined
  };
  
  return certificateFile;
}

// 生成模拟法律文件
function generateMockLegalFile(baseFile: File): LegalFile {
  const legalTypes = [
    '合同模板', '公司章程', '劳动合同', '保密协议', 
    '授权书', '股权协议', '合作协议', '补充协议'
  ];
  
  const legalFile: LegalFile = {
    ...baseFile,
    legalType: legalTypes[Math.floor(Math.random() * legalTypes.length)],
    effectiveDate: Math.random() > 0.5 ? dayjs().subtract(Math.floor(Math.random() * 500), 'day').format('YYYY-MM-DD') : undefined,
    parties: Math.random() > 0.5 ? Array(Math.floor(Math.random() * 2) + 1).fill(0).map(() => getRandomCompany()) : undefined,
    isTemplate: Math.random() > 0.7
  };
  
  return legalFile;
}

// 生成模拟文件版本数据
export function generateMockFileVersions(fileId: string, count = 5): FileVersion[] {
  const versions: FileVersion[] = [];
  
  for (let i = 0; i < count; i++) {
    const versionNum = count - i;
    
    versions.push({
      id: `V${fileId}-${versionNum}`,
      fileId,
      versionNumber: `v${versionNum}.0`,
      changeTime: dayjs().subtract(i * 10, 'day').format('YYYY-MM-DD HH:mm:ss'),
      changeNote: getRandomVersionNote(),
      size: Math.floor(Math.random() * 10000),
      path: `/files/${fileId}-v${versionNum}.pdf`,
      createdBy: getRandomUser()
    });
  }
  
  return versions;
}

// 生成模拟文件权限数据
export function generateMockFilePermissions(fileId: string, count = 3): Permission[] {
  const permissions: Permission[] = [];
  const targetTypes = ['user', 'role', 'department', 'project'] as const;
  const permissionTypes = Object.values(PermissionType);
  
  for (let i = 0; i < count; i++) {
    const targetType = targetTypes[Math.floor(Math.random() * targetTypes.length)];
    let targetId: string;
    
    switch (targetType) {
      case 'user':
        targetId = `U${1000 + Math.floor(Math.random() * 10)}`;
        break;
      case 'role':
        targetId = ['admin', 'manager', 'editor', 'viewer'][Math.floor(Math.random() * 4)];
        break;
      case 'department':
        targetId = ['技术部', '财务部', '法务部', '人事部'][Math.floor(Math.random() * 4)];
        break;
      case 'project':
        targetId = `P${1000 + Math.floor(Math.random() * 10)}`;
        break;
    }
    
    permissions.push({
      id: `P${fileId}-${i}`,
      fileId,
      targetType,
      targetId,
      permissionType: permissionTypes[Math.floor(Math.random() * permissionTypes.length)],
      expireTime: Math.random() > 0.7 ? dayjs().add(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD HH:mm:ss') : undefined,
      createdBy: getRandomUser(),
      createdTime: dayjs().subtract(Math.floor(Math.random() * 100), 'day').format('YYYY-MM-DD HH:mm:ss')
    });
  }
  
  return permissions;
}

// 生成模拟操作日志数据
export function generateMockOperationLogs(fileId: string, count = 10): FileOperationLog[] {
  const logs: FileOperationLog[] = [];
  const operations = ['upload', 'download', 'view', 'edit', 'permission_change', 'delete'] as const;
  
  for (let i = 0; i < count; i++) {
    const operation = operations[Math.floor(Math.random() * operations.length)];
    
    logs.push({
      id: `L${fileId}-${i}`,
      fileId,
      operation,
      operationTime: dayjs().subtract(i, 'day').format('YYYY-MM-DD HH:mm:ss'),
      operatedBy: getRandomUser(),
      details: getOperationDetails(operation),
      ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`
    });
  }
  
  return logs.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime());
}

// 生成模拟统计数据
export function generateMockStatistics(): FileStatistics {
  const categories = Object.values(FileCategory);
  const types = Object.values(FileType);
  
  const categoryCount: Record<FileCategory, number> = {} as Record<FileCategory, number>;
  categories.forEach(category => {
    categoryCount[category] = Math.floor(Math.random() * 100) + 10;
  });
  
  const typeCount: Record<FileType, number> = {} as Record<FileType, number>;
  types.forEach(type => {
    typeCount[type] = Math.floor(Math.random() * 50) + 5;
  });
  
  const statistics: FileStatistics = {
    totalCount: Object.values(categoryCount).reduce((sum, count) => sum + count, 0),
    totalSize: Math.floor(Math.random() * 1000000),
    categoryCount,
    typeCount,
    expiringCount: Math.floor(Math.random() * 20),
    mostDownloaded: Array(5).fill(0).map((_, i) => ({
      id: `F${1000 + i}`,
      name: getRandomFileName(categories[Math.floor(Math.random() * categories.length)], types[Math.floor(Math.random() * types.length)]),
      count: Math.floor(Math.random() * 100) + 10
    }))
  };
  
  return statistics;
}

// 辅助函数

function getRandomFileName(category: FileCategory, type: FileType): string {
  const prefixes: Record<FileCategory, string[]> = {
    [FileCategory.BUSINESS]: ['营业执照', '税务登记证', '组织机构代码证', '开户许可证', '公司注册文件'],
    [FileCategory.INTELLECTUAL]: ['专利申请', '商标注册', '软件著作权', '版权证书', '知识产权证书'],
    [FileCategory.CERTIFICATE]: ['高新技术企业认证', 'ISO9001认证', 'ISO27001认证', '软件企业认证', 'CMMI认证'],
    [FileCategory.LEGAL]: ['合同模板', '公司章程', '劳动合同', '保密协议', '授权书']
  };
  
  const suffixes = ['文件', '证书', '申请', '档案', '记录', '协议'];
  
  const prefix = prefixes[category][Math.floor(Math.random() * prefixes[category].length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
  
  return `${prefix}${Math.floor(Math.random() * 10) + 1}_${suffix}`;
}

function getRandomCompany(): string {
  const companies = [
    '北京科技有限公司', '上海信息技术有限公司', '广州软件开发有限公司',
    '深圳电子科技有限公司', '杭州互联网科技有限公司', '南京数字科技有限公司'
  ];
  
  return companies[Math.floor(Math.random() * companies.length)];
}

function getRandomUser(): string {
  const users = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  return users[Math.floor(Math.random() * users.length)];
}

function getRandomAuthority(): string {
  const authorities = [
    '北京市工商行政管理局', '上海市市场监督管理局', '广州市市场监管局',
    '深圳市市场监督管理局', '杭州市市场监管局', '南京市工商行政管理局'
  ];
  
  return authorities[Math.floor(Math.random() * authorities.length)];
}

function getRandomCertIssuer(): string {
  const issuers = [
    '中国质量认证中心', '北京赛西认证有限责任公司', 'DNV GL商业认证服务',
    'SGS通标标准技术服务有限公司', 'TÜV莱茵技术监督服务有限公司', '国家知识产权局'
  ];
  
  return issuers[Math.floor(Math.random() * issuers.length)];
}

function getRandomTags(): string[] {
  const allTags = [
    '重要', '紧急', '核心', '基础', '参考', '模板', '过期', '有效',
    '待更新', '已确认', '内部', '外部', '公开', '保密', '私密'
  ];
  
  const count = Math.floor(Math.random() * 3) + 1;
  const tags: string[] = [];
  
  for (let i = 0; i < count; i++) {
    const tag = allTags[Math.floor(Math.random() * allTags.length)];
    if (!tags.includes(tag)) {
      tags.push(tag);
    }
  }
  
  return tags;
}

function getRandomVersionNote(): string {
  const notes = [
    '初始版本', '更新内容', '修复错误', '修改格式', '更新信息',
    '添加附件', '调整结构', '完善内容', '增加细节', '删除过期信息'
  ];
  
  return notes[Math.floor(Math.random() * notes.length)];
}

function getOperationDetails(operation: string): string {
  const details: Record<string, string[]> = {
    'upload': ['上传了新文件', '上传了文件的新版本', '批量上传了文件'],
    'download': ['下载了文件', '下载了文件的历史版本', '批量下载了文件'],
    'view': ['查看了文件内容', '预览了文件', '访问了文件详情'],
    'edit': ['编辑了文件信息', '修改了文件标签', '更新了文件描述'],
    'permission_change': ['更改了文件权限', '添加了新的权限', '移除了文件权限'],
    'delete': ['删除了文件', '彻底删除了文件', '将文件移至回收站']
  };
  
  const opDetails = details[operation] || ['操作了文件'];
  return opDetails[Math.floor(Math.random() * opDetails.length)];
}

function getFileExtension(type: FileType): string {
  const extensions: Record<FileType, string> = {
    [FileType.PDF]: 'pdf',
    [FileType.WORD]: 'docx',
    [FileType.EXCEL]: 'xlsx',
    [FileType.IMAGE]: 'jpg',
    [FileType.CODE]: 'js',
    [FileType.ZIP]: 'zip',
    [FileType.OTHER]: 'txt'
  };
  
  return extensions[type];
}

function getFileCategoryName(category: FileCategory): string {
  const names: Record<FileCategory, string> = {
    [FileCategory.BUSINESS]: '工商档案',
    [FileCategory.INTELLECTUAL]: '知识产权',
    [FileCategory.CERTIFICATE]: '资质证书',
    [FileCategory.LEGAL]: '法律文件'
  };
  
  return names[category];
} 