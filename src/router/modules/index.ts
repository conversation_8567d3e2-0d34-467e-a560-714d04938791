import { RouteRecordRaw } from 'vue-router';
import projectRoutes from './projects';
import contractRoutes from './contracts';
import customerRoutes from './customer';
import supplierRoutes from './supplier';
import fileRoutes from './files';

// 组合所有路由模块
const moduleRoutes: RouteRecordRaw[] = [
  // 客户路由可能是数组，需要处理
  ...(Array.isArray(customerRoutes) ? customerRoutes : [customerRoutes]),
  // 供应商路由可能是数组，需要处理  
  ...(Array.isArray(supplierRoutes) ? supplierRoutes : [supplierRoutes]),
  // 其他路由都是单个对象
  projectRoutes,
  contractRoutes,
  fileRoutes,
];

export default moduleRoutes;