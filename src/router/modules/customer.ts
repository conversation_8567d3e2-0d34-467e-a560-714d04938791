import { RouteRecordRaw } from 'vue-router';

// Extended meta interface for route
interface RouteMeta {
  title?: string;
  icon?: string;
  order?: number;
  permissions?: string[];
  keepAlive?: boolean;
  hidden?: boolean;
  activeMenu?: string;
}

// Route type with extended meta
type AppRouteRecordRaw = RouteRecordRaw & {
  meta?: RouteMeta;
  children?: AppRouteRecordRaw[];
};

const customerRoutes: AppRouteRecordRaw = {
  path: '/customer',
  name: 'CustomerManagement',
  component: () => import('@/views/customer/List.vue'),
  meta: {
    title: '客户管理',
    icon: 'TeamOutlined',
    keepAlive: true,
    order: 10
  }
};

const customerDetailRoutes: AppRouteRecordRaw = {
  path: '/customer',
  name: 'CustomerDetailRoutes',
  meta: {
    hidden: true
  },
  children: [
    {
      path: 'form',
      name: 'CustomerForm',
      component: () => import('@/views/customer/Form.vue'),
      meta: {
        title: '新增客户',
        hidden: true
      }
    },
    {
      path: 'form/:id',
      name: 'CustomerEdit',
      component: () => import('@/views/customer/Form.vue'),
      meta: {
        title: '编辑客户',
        hidden: true
      }
    },
    {
      path: 'detail/:id',
      name: 'CustomerDetail',
      component: () => import('@/views/customer/Detail.vue'),
      meta: {
        title: '客户详情',
        hidden: true
      }
    }
  ]
};

// 导出路由数组
export default [customerRoutes, customerDetailRoutes]; 