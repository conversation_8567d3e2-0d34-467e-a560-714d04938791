import { RouteRecordRaw } from "vue-router";

// Extended meta interface for route
interface RouteMeta {
  title?: string;
  icon?: string;
  order?: number;
  permissions?: string[];
  keepAlive?: boolean;
  hidden?: boolean;
  activeMenu?: string;
}

// Route type with extended meta
type AppRouteRecordRaw = RouteRecordRaw & {
  meta?: RouteMeta;
  children?: AppRouteRecordRaw[];
};

const projectsRoutes: AppRouteRecordRaw = {
  path: "/projects",
  name: "projects",
  component: () => import("@/layouts/RouteView.vue"),
  meta: {
    title: "项目管理",
    icon: "ProjectOutlined",
    order: 2,
    permissions: ["projects:view"],
  },
  children: [
    {
      path: "",
      name: "projectsList",
      // component: () => import("@/views/projects/List.vue"),
      component: () => import("@/views/projects/initiation/Index.vue"),
      meta: {
        title: "项目列表",
        hidden: true,
      },
    },
    {
      path: "detail/:id",
      name: "projectDetail",
      component: () => import("@/views/projects/Detail.vue"),
      meta: {
        title: "项目详情",
        hidden: true,
        activeMenu: "/projects",
      },
    },
  ],
};

export default projectsRoutes; 