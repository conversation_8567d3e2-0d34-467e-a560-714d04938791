import { RouteRecordRaw } from 'vue-router';

// Extended meta interface for route
interface RouteMeta {
  title?: string;
  icon?: string;
  order?: number;
  permissions?: string[];
  keepAlive?: boolean;
  hidden?: boolean;
  activeMenu?: string;
}

// Route type with extended meta
type AppRouteRecordRaw = RouteRecordRaw & {
  meta?: RouteMeta;
  children?: AppRouteRecordRaw[];
};

const supplierRoutes: AppRouteRecordRaw = {
  path: '/supplier',
  name: 'SupplierManagement',
  component: () => import('@/views/supplier/List.vue'),
  meta: {
    title: '供应商管理',
    icon: 'ShopOutlined',
    keepAlive: true,
    order: 11
  }
};

const supplierDetailRoutes: AppRouteRecordRaw = {
    path: '/supplier',
    name: 'SupplierDetailRoutes',
    meta: {
      hidden: true,
    },
    children: [
      {
        path: 'form',
        name: 'SupplierForm',
        component: () => import('@/views/supplier/Form.vue'),
        meta: {
          title: '新增供应商',
          hidden: true,
        },
      },
      {
        path: 'form/:id',
        name: 'SupplierEdit',
        component: () => import('@/views/supplier/Form.vue'),
        meta: {
          title: '编辑供应商',
          hidden: true,
        },
      },
      {
        path: 'detail/:id',
        name: 'SupplierDetail',
        component: () => import('@/views/supplier/Detail.vue'),
        meta: {
          title: '供应商详情',
          hidden: true,
        },
      },
    ],
  };

// 导出路由数组
export default [supplierRoutes, supplierDetailRoutes]; 