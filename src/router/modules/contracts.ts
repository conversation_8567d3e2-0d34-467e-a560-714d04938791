import { RouteRecordRaw } from 'vue-router';

// Extended meta interface for route
interface RouteMeta {
  title?: string;
  icon?: string;
  order?: number;
  permissions?: string[];
  keepAlive?: boolean;
  hidden?: boolean;
  activeMenu?: string;
}

// Route type with extended meta
type AppRouteRecordRaw = RouteRecordRaw & {
  meta?: RouteMeta;
  children?: AppRouteRecordRaw[];
};

const contractsRoutes: AppRouteRecordRaw = {
  path: "/contracts",
  name: "contracts",
  component: () => import("@/views/contracts/List.vue"),
  meta: {
    title: "合同管理",
    icon: "FileTextOutlined",
    order: 3,
    permissions: ["contracts:view"],
  },
};

export default contractsRoutes; 