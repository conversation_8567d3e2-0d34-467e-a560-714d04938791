import { RouteRecordRaw } from 'vue-router';

// Extended meta interface for route
interface RouteMeta {
  title?: string;
  icon?: string;
  order?: number;
  permissions?: string[];
  keepAlive?: boolean;
  hidden?: boolean;
  activeMenu?: string;
}

// Route type with extended meta
type AppRouteRecordRaw = RouteRecordRaw & {
  meta?: RouteMeta;
  children?: AppRouteRecordRaw[];
};

const filesRoutes: AppRouteRecordRaw = {
  path: '/files',
  name: 'FileManagement',
  meta: {
    title: '文件管理',
    icon: 'FileOutlined',
    order: 35  // 放在合同管理和人力资源之间
  },
  children: [
    {
      path: 'index',
      name: 'FilesList',
      component: () => import('@/views/files/Index.vue'),
      meta: {
        title: '文件中心',
        keepAlive: true
      }
    },
    {
      path: 'upload',
      name: 'FileUpload',
      component: () => import('@/views/files/Upload.vue'),
      meta: {
        title: '文件上传',
        hidden: true
      }
    },
    {
      path: 'permissions',
      name: 'FilePermissions',
      component: () => import('@/views/files/Permissions.vue'),
      meta: {
        title: '权限设置',
        hidden: true
      }
    },
    {
      path: 'preview/:id',
      name: 'FilePreview',
      component: () => import('@/views/files/Preview.vue'),
      meta: {
        title: '文件预览',
        hidden: true
      }
    },
    {
      path: 'versions/:id',
      name: 'FileVersions',
      component: () => import('@/views/files/Versions.vue'),
      meta: {
        title: '版本历史',
        hidden: true
      }
    }
  ]
};

export default filesRoutes; 