import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import DefaultLayout from '@/layouts/DefaultLayout.vue'

// 导入模块路由
import moduleRoutes from './modules'

// 基础路由 - 不归属于任何模块的路由
const baseRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: {
          title: '首页',
          icon: 'DashboardOutlined',
          order: 1
        }
      },
      {
        path: '/contracts/create',
        name: 'contract-create',
        component: () => import('@/views/contracts/Create.vue'),
        meta: {
          title: '新增合同',
          hidden: true
        }
      },
      {
        path: '/projects/detail/:id',
        name: 'project-detail',
        component: () => import('@/views/projects/Detail.vue'),
        meta: {
          title: '项目详情',
          hidden: true
        }
      },
      // {
      //   path: '/reports',
      //   name: 'reports',
      //   component: () => import('@/views/report/Index.vue'),
      //   meta: {
      //     title: '统计报表',
      //     icon: 'bar-chart-outlined',
      //     order: 90
      //   }
      // },
      // {
      //   path: '/settings',
      //   name: 'settings',
      //   component: () => import('@/views/settings/Index.vue'),
      //   meta: {
      //     title: '系统设置',
      //     icon: 'setting-outlined',
      //     order: 99
      //   }
      // },
      // 模块路由将在这里自动添加
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/system/NotFound.vue'),
    meta: { 
      title: '404', 
      hidden: true 
    }
  }
]

// 将模块路由添加到根路由的children中
moduleRoutes.forEach(route => {
  // @ts-ignore
  baseRoutes[0].children?.push(route)
})

// 合并所有路由
export const routes: RouteRecordRaw[] = baseRoutes

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局导航守卫
router.beforeEach((to, from, next) => {
  // 获取token
  const token = localStorage.getItem('token');
  
  // 如果是登录页面，已登录则跳转到首页
  if (to.path === '/login') {
    if (token) {
      next('/');
      return;
    }
  }
  
  // 如果不是登录页面，未登录则跳转到登录页
  if (!token && to.path !== '/login') {
    next('/login');
    return;
  }
  
  // 设置标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 业财集成管理系统`;
  } else {
    document.title = '业财集成管理系统';
  }
  
  next();
})

export default router
