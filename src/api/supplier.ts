import request from '@/utils/request';
import type { SupplierQueryParams, SupplierFormData, Supplier } from '@/types/supplier';
import { CustomerType, IndustryType, SupplierStatus, generateSupplierCode } from '@/constants/customer';

// 模拟的供应商数据
const mockSuppliers: Supplier[] = [
  { 
    id: '1', 
    code: 'V250624001', 
    name: '北京科技有限公司', 
    type: CustomerType.ENTERPRISE, 
    status: SupplierStatus.COOPERATING,
    industry: IndustryType.INFORMATION_TECHNOLOGY, 
    contact: '张三', 
    phone: '138-0001-0001', 
    email: 'zhang<PERSON>@bjtech.com', 
    address: '北京市海淀区中关村大街1号',
    province: 'beijing',
    city: 'haidian',
    website: 'https://www.bjtech.com',
    owner: 'user1',
    ownerName: '李经理',
    createTime: '2024-06-24 10:00:00',
    updateTime: '2024-06-24 10:00:00',
    remark: '优质技术供应商'
  },
  { 
    id: '2', 
    code: 'V250624002', 
    name: '上海制造集团', 
    type: CustomerType.ENTERPRISE, 
    status: SupplierStatus.QUALIFIED,
    industry: IndustryType.MANUFACTURING_INDUSTRIAL, 
    contact: '李四', 
    phone: '138-0002-0002', 
    email: '<EMAIL>', 
    address: '上海市浦东新区张江高科技园区',
    province: 'shanghai',
    city: 'pudong',
    website: 'https://www.shmanuf.com',
    owner: 'user2',
    ownerName: '王经理',
    createTime: '2024-06-24 11:00:00',
    updateTime: '2024-06-24 11:00:00',
    remark: '大型制造企业'
  },
];

let nextId = 3;

export function getSupplierList(params: SupplierQueryParams) {
  console.log('Fetching supplier list with params:', params);
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredSuppliers = [...mockSuppliers];
      
      // 关键词搜索
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase();
        filteredSuppliers = filteredSuppliers.filter(supplier => 
          supplier.name.toLowerCase().includes(keyword) ||
          supplier.code.toLowerCase().includes(keyword) ||
          supplier.contact.toLowerCase().includes(keyword)
        );
      }
      
      // 类型筛选
      if (params.type) {
        filteredSuppliers = filteredSuppliers.filter(supplier => supplier.type === params.type);
      }
      
      // 状态筛选
      if (params.status) {
        filteredSuppliers = filteredSuppliers.filter(supplier => supplier.status === params.status);
      }
      
      // 行业筛选
      if (params.industry) {
        filteredSuppliers = filteredSuppliers.filter(supplier => supplier.industry === params.industry);
      }
      
      resolve({
        data: {
          list: filteredSuppliers,
          total: filteredSuppliers.length,
        },
      });
    }, 500);
  });
  // return request({
  //   url: '/suppliers',
  //   method: 'get',
  //   params,
  // });
}

export function getSupplierDetail(id: string) {
  console.log('Fetching supplier detail for id:', id);
  return new Promise((resolve) => {
    setTimeout(() => {
      const supplier = mockSuppliers.find(s => s.id === id);
      resolve({
        data: supplier
      });
    }, 500);
  });
  // return request({
  //   url: `/suppliers/${id}`,
  //   method: 'get',
  // });
}

export function createSupplier(data: SupplierFormData) {
  console.log('Creating supplier with data:', data);
  return new Promise((resolve) => {
    setTimeout(() => {
      const newSupplier: Supplier = { 
        ...data, 
        id: String(nextId++), 
        code: generateSupplierCode(data.type),
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
      };
      mockSuppliers.push(newSupplier);
      resolve({
        data: newSupplier
      });
    }, 500);
  });
  // return request({
  //   url: '/suppliers',
  //   method: 'post',
  //   data,
  // });
}

export function updateSupplier(id: string, data: SupplierFormData) {
  console.log('Updating supplier with id and data:', id, data);
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockSuppliers.findIndex(s => s.id === id);
      if (index !== -1) {
        const updatedSupplier = { 
          ...mockSuppliers[index], 
          ...data,
          updateTime: new Date().toLocaleString(),
        };
        mockSuppliers[index] = updatedSupplier;
        resolve({
          data: updatedSupplier
        });
      } else {
        resolve({ data: null });
      }
    }, 500);
  });
  // return request({
  //   url: `/suppliers/${id}`,
  //   method: 'put',
  //   data,
  // });
}

export function deleteSupplier(id: string) {
  console.log('Deleting supplier with id:', id);
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockSuppliers.findIndex(s => s.id === id);
      if (index !== -1) {
        mockSuppliers.splice(index, 1);
      }
      resolve({
        data: {}
      });
    }, 500);
  });
  // return request({
  //   url: `/suppliers/${id}`,
  //   method: 'delete',
  // });
} 