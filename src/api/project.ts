import request from '@/utils/request';
import type {
  Project,
  ProjectMilestone,
  ProjectTask,
  ProjectMember,
  ProjectDocument,
  ProjectRisk,
  User,
} from '@/types/project';

interface ProjectListParams {
  page?: number;
  pageSize?: number;
  name?: string;
  code?: string;
  type?: string;
  status?: string;
  managerId?: string;
  customerId?: string;
  startDate?: string;
  endDate?: string;
}

interface ProjectListResponse {
  total: number;
  items: Project[];
}

// 获取项目列表
export function getProjectList(params: ProjectListParams) {
  return request.get<ProjectListResponse>('/projects', { params });
}

// 获取项目详情
export async function getProjectDetail(id: string): Promise<Project> {
  return request.get(`/api/projects/${id}`);
}

// 创建项目
export function createProject(data: Omit<Project, 'id' | 'createTime' | 'updateTime'>) {
  return request.post<Project>('/projects', data);
}

// 更新项目
export async function updateProject(id: string, data: Partial<Project>): Promise<void> {
  return request.put(`/api/projects/${id}`, data);
}

// 删除项目
export function deleteProject(id: string) {
  return request.delete(`/projects/${id}`);
}

// 获取项目里程碑
export async function getProjectMilestones(projectId: string): Promise<ProjectMilestone[]> {
  return request.get(`/api/projects/${projectId}/milestones`);
}

// 创建项目里程碑
export async function createProjectMilestone(
  projectId: string,
  data: Omit<ProjectMilestone, 'id' | 'projectId' | 'createTime' | 'updateTime'>
): Promise<ProjectMilestone> {
  return request.post(`/api/projects/${projectId}/milestones`, data);
}

// 更新项目里程碑
export async function updateProjectMilestone(
  projectId: string,
  milestoneId: string,
  data: Partial<ProjectMilestone>
): Promise<void> {
  return request.put(`/api/projects/${projectId}/milestones/${milestoneId}`, data);
}

// 获取项目任务
export async function getProjectTasks(projectId: string): Promise<ProjectTask[]> {
  return request.get(`/api/projects/${projectId}/tasks`);
}

// 获取项目任务详情
export async function getProjectTaskDetail(projectId: string, taskId: string): Promise<ProjectTask> {
  return request.get(`/api/projects/${projectId}/tasks/${taskId}`);
}

// 创建项目任务
export async function createProjectTask(
  projectId: string,
  data: Omit<ProjectTask, 'id' | 'projectId' | 'createTime' | 'updateTime'>
): Promise<ProjectTask> {
  return request.post(`/api/projects/${projectId}/tasks`, data);
}

// 更新项目任务
export async function updateProjectTask(
  projectId: string,
  taskId: string,
  data: Partial<ProjectTask>
): Promise<void> {
  return request.put(`/api/projects/${projectId}/tasks/${taskId}`, data);
}

// 获取项目成员
export async function getProjectMembers(projectId: string): Promise<ProjectMember[]> {
  return request.get(`/api/projects/${projectId}/members`);
}

// 添加项目成员
export async function addProjectMember(
  projectId: string,
  data: {
    userId: string;
    role: string;
    workload: number;
    joinDate: string;
  }
): Promise<ProjectMember> {
  return request.post(`/api/projects/${projectId}/members`, data);
}

// 更新项目成员
export async function updateProjectMember(
  projectId: string,
  memberId: string,
  data: Partial<ProjectMember>
): Promise<void> {
  return request.put(`/api/projects/${projectId}/members/${memberId}`, data);
}

// 移除项目成员
export async function removeProjectMember(
  projectId: string,
  memberId: string
): Promise<void> {
  return request.delete(`/api/projects/${projectId}/members/${memberId}`);
}

// 获取项目文档
export async function getProjectDocuments(projectId: string): Promise<ProjectDocument[]> {
  return request.get(`/api/projects/${projectId}/documents`);
}

// 上传项目文档
export async function uploadProjectDocument(
  projectId: string,
  formData: FormData
): Promise<ProjectDocument> {
  return request.post(`/api/projects/${projectId}/documents`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 删除项目文档
export async function deleteProjectDocument(
  projectId: string,
  documentId: string
): Promise<void> {
  return request.delete(`/api/projects/${projectId}/documents/${documentId}`);
}

// 获取项目风险
export async function getProjectRisks(projectId: string): Promise<ProjectRisk[]> {
  return request.get(`/api/projects/${projectId}/risks`);
}

// 创建项目风险
export async function createProjectRisk(
  projectId: string,
  data: Omit<ProjectRisk, 'id' | 'projectId' | 'createTime' | 'updateTime'>
): Promise<ProjectRisk> {
  return request.post(`/api/projects/${projectId}/risks`, data);
}

// 更新项目风险
export async function updateProjectRisk(
  projectId: string,
  riskId: string,
  data: Partial<ProjectRisk>
): Promise<void> {
  return request.put(`/api/projects/${projectId}/risks/${riskId}`, data);
}

// 获取可用用户
export async function getAvailableUsers(): Promise<User[]> {
  return request.get('/api/users');
} 