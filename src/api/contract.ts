import type { Contract, ContractAmendment, ContractAttachment, ContractParty } from '@/types/contract';
import request from '@/utils/request';

// 合同管理相关API

// 获取合同列表
export function getContractList(params?: any) {
  return request.get<{
    data: Contract[];
    total: number;
  }>('/api/contracts', { params });
}

// 获取合同详情
export function getContractDetail(id: string) {
  return request.get<Contract>(`/api/contracts/${id}`);
}

// 创建合同
export function createContract(data: Partial<Contract>) {
  return request.post<Contract>('/api/contracts', data);
}

// 更新合同
export function updateContract(id: string, data: Partial<Contract>) {
  return request.put<Contract>(`/api/contracts/${id}`, data);
}

// 删除合同
export function deleteContract(id: string) {
  return request.delete<void>(`/api/contracts/${id}`);
}

// 获取合同相关方列表
export function getContractParties(contractId: string) {
  return request({
    url: `/api/contracts/${contractId}/parties`,
    method: 'get',
  });
}

// 添加合同相关方
export function addContractParty(contractId: string, data: Partial<ContractParty>) {
  return request({
    url: `/api/contracts/${contractId}/parties`,
    method: 'post',
    data,
  });
}

// 更新合同相关方
export function updateContractParty(contractId: string, partyId: string, data: Partial<ContractParty>) {
  return request({
    url: `/api/contracts/${contractId}/parties/${partyId}`,
    method: 'put',
    data,
  });
}

// 删除合同相关方
export function deleteContractParty(contractId: string, partyId: string) {
  return request({
    url: `/api/contracts/${contractId}/parties/${partyId}`,
    method: 'delete',
  });
}

// 获取合同附件列表
export function getContractAttachments(contractId: string) {
  return request.get<ContractAttachment[]>(`/api/contracts/${contractId}/attachments`);
}

// 上传合同附件
export function uploadContractAttachment(contractId: string, file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request.post<ContractAttachment>(`/api/contracts/${contractId}/attachments`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 删除合同附件
export function deleteContractAttachment(contractId: string, attachmentId: string) {
  return request.delete<void>(`/api/contracts/${contractId}/attachments/${attachmentId}`);
}

// 合同变更相关API

// 获取合同变更记录列表
export function getContractAmendmentList(params?: any) {
  return request.get<{
    data: ContractAmendment[];
    total: number;
  }>('/api/contract-amendments', { params });
}

// 获取合同变更记录详情
export function getContractAmendmentDetail(id: string) {
  return request.get<ContractAmendment>(`/api/contract-amendments/${id}`);
}

// 创建合同变更记录
export function createContractAmendment(data: Partial<ContractAmendment>) {
  return request.post<ContractAmendment>('/api/contract-amendments', data);
}

// 更新合同变更记录
export function updateContractAmendment(id: string, data: Partial<ContractAmendment>) {
  return request.put<ContractAmendment>(`/api/contract-amendments/${id}`, data);
}

// 删除合同变更记录
export function deleteContractAmendment(id: string) {
  return request.delete<void>(`/api/contract-amendments/${id}`);
}

// 提交合同变更申请
export function submitContractAmendment(id: string) {
  return request.post<ContractAmendment>(`/api/contract-amendments/${id}/submit`);
}

// 批准合同变更
export function approveContractAmendment(id: string, remark?: string) {
  return request.post<ContractAmendment>(`/api/contract-amendments/${id}/approve`, { remark });
}

// 拒绝合同变更
export function rejectContractAmendment(id: string, reason: string) {
  return request.post<ContractAmendment>(`/api/contract-amendments/${id}/reject`, { reason });
}

// 取消合同变更申请
export function cancelContractAmendment(id: string) {
  return request.post<ContractAmendment>(`/api/contract-amendments/${id}/cancel`);
}

// 下载合同附件
export function downloadContractAttachment(attachmentId: string) {
  return request.get<Blob>(`/api/attachments/${attachmentId}/download`, {
    responseType: 'blob',
  });
}

// 获取合同变更附件列表
export function getContractAmendmentAttachments(amendmentId: string) {
  return request.get<ContractAttachment[]>(`/api/contract-amendments/${amendmentId}/attachments`);
}

// 上传合同变更附件
export function uploadContractAmendmentAttachment(amendmentId: string, file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request.post<ContractAttachment>(`/api/contract-amendments/${amendmentId}/attachments`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 删除合同变更附件
export function deleteContractAmendmentAttachment(amendmentId: string, attachmentId: string) {
  return request.delete<void>(`/api/contract-amendments/${amendmentId}/attachments/${attachmentId}`);
} 