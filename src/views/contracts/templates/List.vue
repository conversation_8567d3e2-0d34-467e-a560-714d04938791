<template>
  <div class="contract-template-list-page">
    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">合同模板管理</h2>
          <p class="text-gray-500 mt-1">管理合同模板和版本</p>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-4" :bordered="false">
      <a-form layout="horizontal" :model="filterForm" @finish="handleSearch" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="[16, 16]">
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="模板名称" name="name">
              <a-input v-model:value="filterForm.name" placeholder="请输入模板名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="模板代码" name="code">
              <a-input v-model:value="filterForm.code" placeholder="请输入模板代码" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="模板分类" name="category">
              <a-select
                v-model:value="filterForm.category"
                placeholder="请选择分类"
                allow-clear
              >
                <a-select-option v-for="(label, value) in contractCategoryOptions" :key="value" :value="value">
                  {{ label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="状态" name="status">
              <a-select
                v-model:value="filterForm.status"
                placeholder="请选择状态"
                allow-clear
              >
                <a-select-option 
                  v-for="(label, value) in statusOptions" 
                  :key="value" 
                  :value="value"
                >
                  {{ label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24" class="text-right">
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetFilter">
                <template #icon><redo-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <!-- 操作区域 -->
      <div class="flex justify-between mb-4">
        <a-space>
          <a-button type="primary" @click="handleAddTemplate">
            <template #icon><plus-outlined /></template>
            新建模板
          </a-button>
          <a-popconfirm
            v-if="selectedRowKeys.length > 0"
            title="确定批量删除选中的模板吗？"
            @confirm="handleBatchDelete"
          >
            <a-button danger>
              <template #icon><delete-outlined /></template>
              批量删除
            </a-button>
          </a-popconfirm>
        </a-space>
        <a-button @click="refreshData">
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
      </div>
      
      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data-source="templateList"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :loading="loading"
        :pagination="paginationConfig"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'category'">
            {{ contractCategoryOptions[record.category as ContractCategory] }}
          </template>
          <template v-else-if="column.dataIndex === 'contractNature'">
            {{ contractNatureOptions[record.contractNature as ContractNature] }}
          </template>
          <template v-else-if="column.dataIndex === 'editMode'">
            {{ editModeOptions[record.editMode as EditMode] }}
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === TemplateStatus.ENABLED ? 'success' : 'default'">
              {{ statusOptions[record.status as TemplateStatus] }}
            </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-space>
              <a-tooltip title="查看">
                <a-button type="link" size="small" @click="viewTemplate(record)">
                  <eye-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="编辑">
                <a-button type="link" size="small" @click="editTemplate(record)">
                  <edit-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip :title="record.status === TemplateStatus.ENABLED ? '禁用' : '启用'">
                <a-button 
                  type="link" 
                  size="small" 
                  @click="toggleStatus(record)"
                >
                  <check-circle-outlined v-if="record.status !== TemplateStatus.ENABLED" />
                  <stop-outlined v-else />
                </a-button>
              </a-tooltip>
              <a-tooltip title="删除">
                <a-popconfirm
                  title="确定删除此模板吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteTemplate(record)"
                >
                  <a-button type="link" danger size="small">
                    <delete-outlined />
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 版本查看对话框 -->
    <a-modal
      v-model:visible="versionModalVisible"
      title="模板版本历史"
      width="800px"
      :footer="null"
    >
      <a-table
        :columns="versionColumns"
        :data-source="templateVersions"
        :pagination="{ pageSize: 5, showTotal: (total: number) => `共 ${total} 条` }"
        :loading="loadingVersions"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" @click="viewVersionDetail(record)">查看</a-button>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { message, FormInstance, Modal } from 'ant-design-vue';
import {
  useTemplateStore,
  TemplateStatus,
  ContractCategory,
  ContractNature,
  EditMode,
  type Template,
  type TemplateVersion
} from '@/stores/template';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  SyncOutlined,
  EyeOutlined,
  EditOutlined,
  CheckCircleOutlined,
  StopOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';

const router = useRouter();
const templateStore = useTemplateStore();

const formRef = ref<FormInstance>();
const loading = computed(() => templateStore.loading);
const templateList = computed(() => {
  return templateStore.templateList.filter(template => {
    return (
      (!filterForm.name || template.name.includes(filterForm.name)) &&
      (!filterForm.code || template.code.includes(filterForm.code)) &&
      (!filterForm.category || template.category === filterForm.category) &&
      (!filterForm.status || template.status === filterForm.status)
    );
  });
});

const selectedRowKeys = ref<string[]>([]);
const hasSelected = computed(() => selectedRowKeys.value.length > 0);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: computed(() => templateList.value.length),
  showTotal: (total: number) => `共 ${total} 条`,
  showSizeChanger: true,
  showQuickJumper: true,
});

const filterForm = reactive({
  name: '',
  code: '',
  category: undefined as ContractCategory | undefined,
  status: undefined as TemplateStatus | undefined,
});

const formVisible = ref(false);
const formState = reactive<Partial<Template>>({});
const formRules = {};

const detailVisible = ref(false);
const currentTemplate = ref<Template | null>(null);
const templateVersions = ref<TemplateVersion[]>([]);

const versionContentVisible = ref(false);
const selectedVersion = ref<TemplateVersion | null>(null);

const contractCategoryOptions: Record<ContractCategory, string> = {
  [ContractCategory.SALES]: '销售合同',
  [ContractCategory.PURCHASE]: '采购合同',
  [ContractCategory.SERVICE]: '服务合同',
  [ContractCategory.COOPERATION]: '合作协议',
  [ContractCategory.CONFIDENTIALITY]: '保密协议',
  [ContractCategory.OTHER]: '其他合同',
};

const contractNatureOptions: Record<ContractNature, string> = {
  [ContractNature.FRAMEWORK]: '框架协议',
  [ContractNature.NORMAL]: '普通合同',
};

const editModeOptions: Record<EditMode, string> = {
  [EditMode.FULL]: '全文编辑',
  [EditMode.FILL]: '填空编辑',
};

const statusOptions: Record<TemplateStatus, string> = {
  [TemplateStatus.ENABLED]: '启用',
  [TemplateStatus.DISABLED]: '禁用',
};

const columns = [
  { title: '模板名称', dataIndex: 'name', key: 'name', sorter: (a: Template, b: Template) => a.name.localeCompare(b.name) },
  { title: '模板编码', dataIndex: 'code', key: 'code', sorter: (a: Template, b: Template) => a.code.localeCompare(b.code) },
  { title: '合同类型', dataIndex: 'category', key: 'category', filters: Object.entries(contractCategoryOptions).map(([value, text]) => ({ text, value: value as ContractCategory })), onFilter: (value: ContractCategory, record: Template) => record.category === value },
  { title: '合同性质', dataIndex: 'contractNature', key: 'contractNature' },
  { title: '编辑模式', dataIndex: 'editMode', key: 'editMode' },
  { title: '版本', dataIndex: 'version', key: 'version', width: 80, sorter: (a: Template, b: Template) => a.version - b.version },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100, filters: Object.entries(statusOptions).map(([value, text]) => ({ text, value: value as TemplateStatus })), onFilter: (value: TemplateStatus, record: Template) => record.status === value },
  { title: '创建人', dataIndex: 'createdBy', key: 'createdBy' },
  { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt', sorter: (a: Template, b: Template) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime() },
  { title: '操作', dataIndex: 'action', key: 'action', width: 180, fixed: 'right' },
];

const versionColumns = [
  { title: '版本号', dataIndex: 'version', key: 'version', width: 100 },
  { title: '创建人', dataIndex: 'createdBy', key: 'createdBy', width: 120 },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
  { title: '状态', dataIndex: 'isActive', key: 'isActive', width: 100 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 100 },
];

onMounted(async () => {
  await refreshData();
});

const refreshData = async () => {
  try {
    await templateStore.fetchTemplates();
  } catch (error) {
    message.error('获取模板列表失败');
    console.error(error);
  }
};

const handleSearch = () => {
  pagination.current = 1;
};

const resetFilter = () => {
  filterForm.name = '';
  filterForm.code = '';
  filterForm.category = undefined;
  filterForm.status = undefined;
  handleSearch();
};

const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys as string[];
};

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

const viewTemplate = async (record: Template) => {
  currentTemplate.value = record;
  try {
    const versions = await templateStore.getTemplateVersions(record.id);
    templateVersions.value = versions;
  } catch (error) {
    message.error('获取版本历史失败');
    templateVersions.value = [];
  }
  detailVisible.value = true;
};

const viewVersionContent = (record: TemplateVersion) => {
  selectedVersion.value = record;
  versionContentVisible.value = true;
};

const handleAddTemplate = () => {
  router.push({ name: 'contracts-templates-edit' });
};

const editTemplate = (record: Template) => {
  router.push({ name: 'contracts-templates-edit', params: { id: record.id } });
};

const submitForm = () => {
  console.log('Inline form submission not implemented, using navigation.');
  formVisible.value = false;
};

const toggleStatus = async (record: Template) => {
  try {
    await templateStore.toggleTemplateStatus(record.id);
    message.success(`模板已${record.status === TemplateStatus.ENABLED ? '禁用' : '启用'}`);
    await refreshData();
  } catch (error) {
    message.error('操作失败');
  }
};

const deleteTemplate = async (record: Template) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板 "${record.name}" 吗？此操作不可恢复。`,
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        await templateStore.deleteTemplate(record.id);
        message.success('模板已删除');
        await refreshData();
      } catch (error) {
        message.error('删除失败');
      }
    }
  });
};

const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的模板');
    return;
  }
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个模板吗？此操作不可恢复。`,
    okText: '删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const promises = selectedRowKeys.value.map(id => 
          templateStore.deleteTemplate(id)
        );
        await Promise.all(promises);
        message.success(`已批量删除 ${selectedRowKeys.value.length} 个模板`);
        selectedRowKeys.value = [];
        await refreshData();
      } catch (error) {
        message.error('批量删除失败');
      }
    }
  });
};

// 添加分页配置计算属性
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: pagination.showTotal,
  pageSizeOptions: ['10', '20', '50', '100']
}));

// 修改fetchTemplateList为refreshData
const fetchTemplateList = refreshData;

// 添加版本详情查看方法
const viewVersionDetail = (record: TemplateVersion) => {
  viewVersionContent(record);
};

// 添加loadingVersions状态
const loadingVersions = ref(false);
const versionModalVisible = ref(false);
</script>

<style scoped>
.contract-template-list-page {
  background-color: #f0f2f5;
}

.page-header {
  position: relative;
  margin-bottom: 16px;
}

.page-header h2 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

.page-header .desc {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  margin-bottom: 0;
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

.template-content-preview {
  border: 1px solid #f0f0f0;
  padding: 16px;
  background-color: #fafafa;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.form-help-text {
  color: #999;
  font-size: 12px;
}
</style> 