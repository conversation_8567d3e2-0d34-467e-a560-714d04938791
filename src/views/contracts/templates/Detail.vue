<template>
  <div class="template-detail-page">
    <a-card class="card-container">
      <template #title>
        <div class="flex justify-between items-center">
          <h2>模板详情</h2>
          <a-space>
            <a-button type="primary" @click="editTemplate">
              <template #icon><edit-outlined /></template>
              编辑
            </a-button>
            <a-button @click="goBack">
              <template #icon><arrow-left-outlined /></template>
              返回
            </a-button>
          </a-space>
        </div>
      </template>

      <a-spin :spinning="loading">
        <!-- 基本信息 -->
        <div v-if="template">
          <a-descriptions title="基本信息" bordered :column="2">
            <a-descriptions-item label="模板名称" :span="2">{{ template.name }}</a-descriptions-item>
            <a-descriptions-item label="模板编码">{{ template.code }}</a-descriptions-item>
            <a-descriptions-item label="模板状态">
              <a-tag :color="template.status === 'enabled' ? 'success' : 'default'">
                {{ template.status === 'enabled' ? '启用' : '禁用' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="合同类型">{{ getCategoryName(template.category) }}</a-descriptions-item>
            <a-descriptions-item label="合同性质">{{ getNatureName(template.contractNature) }}</a-descriptions-item>
            <a-descriptions-item label="编辑模式">{{ getEditModeName(template.editMode) }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ formatDate(template.createdAt) }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ formatDate(template.updatedAt) }}</a-descriptions-item>
            <a-descriptions-item label="备注" :span="2">{{ template.remark || '暂无备注' }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 模板内容 -->
        <div class="mt-6">
          <a-divider orientation="left">模板内容</a-divider>
          <div class="template-content" v-html="template?.content || '加载中...'"></div>
        </div>

        <!-- 版本历史 -->
        <div class="mt-6">
          <a-divider orientation="left">版本历史</a-divider>
          <a-table
            :columns="versionColumns"
            :data-source="templateVersions"
            :loading="versionsLoading"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'isActive'">
                <a-tag v-if="record.isActive" color="success">当前版本</a-tag>
                <a-tag v-else color="default">历史版本</a-tag>
              </template>
              <template v-else-if="column.dataIndex === 'action'">
                <a-space>
                  <a @click="viewVersion(record)">查看</a>
                  <a-divider type="vertical" />
                  <a @click="useVersion(record)" v-if="!record.isActive">使用此版本</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </a-spin>
    </a-card>

    <!-- 版本内容弹窗 -->
    <a-modal
      v-model:visible="versionVisible"
      title="版本内容"
      width="800px"
      :footer="null"
    >
      <div class="version-content" v-html="selectedVersion?.content || ''"></div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons-vue';
import { 
  useTemplateStore, 
  Template, 
  TemplateVersion,
  ContractCategory,
  ContractNature,
  EditMode
} from '@/stores/template';

const route = useRoute();
const router = useRouter();
const templateStore = useTemplateStore();

const templateId = ref(route.params.id as string);
const template = ref<Template | null>(null);
const templateVersions = ref<TemplateVersion[]>([]);
const selectedVersion = ref<TemplateVersion | null>(null);
const loading = ref(false);
const versionsLoading = ref(false);
const versionVisible = ref(false);

// 版本历史表格列定义
const versionColumns = [
  { title: '版本号', dataIndex: 'version', key: 'version' },
  { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', render: (text: string) => formatDate(text) },
  { title: '创建人', dataIndex: 'createdBy', key: 'createdBy' },
  { title: '是否当前版本', dataIndex: 'isActive', key: 'isActive' },
  { title: '操作', dataIndex: 'action', key: 'action', width: 150 }
];

// 获取合同类型名称
const getCategoryName = (category: string) => {
  const categories: Record<string, string> = {
    'sales': '销售合同',
    'purchase': '采购合同',
    'service': '服务合同',
    'framework': '框架协议',
    'other': '其他合同'
  };
  return categories[category] || '未知类型';
};

// 获取合同性质名称
const getNatureName = (nature: string) => {
  const natures: Record<string, string> = {
    'standard': '标准合同',
    'custom': '定制合同',
    'supplementary': '补充协议'
  };
  return natures[nature] || '未知性质';
};

// 获取编辑模式名称
const getEditModeName = (mode: string) => {
  const modes: Record<string, string> = {
    'rich_text': '富文本编辑',
    'plain_text': '纯文本编辑',
    'template': '模板变量'
  };
  return modes[mode] || '未知模式';
};

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知时间';
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 返回列表页
const goBack = () => {
  router.push({ name: 'contracts-templates' });
};

// 编辑模板
const editTemplate = () => {
  router.push({ 
    name: 'contracts-templates-edit', 
    params: { id: templateId.value } 
  });
};

// 查看版本内容
const viewVersion = (version: TemplateVersion) => {
  selectedVersion.value = version;
  versionVisible.value = true;
};

// 使用此版本
const useVersion = async (version: TemplateVersion) => {
  try {
    // Since there's no activateTemplateVersion method in the store,
    // we'll use updateTemplate to set the current template content to this version's content
    await templateStore.updateTemplate(
      templateId.value, 
      { 
        content: version.content,
        version: version.version
      },
      false
    );
    message.success('已切换到所选版本');
    await loadTemplateDetails();
  } catch (error) {
    console.error('切换版本失败:', error);
    message.error('切换版本失败，请重试');
  }
};

// 加载模板详情
const loadTemplateDetails = async () => {
  loading.value = true;
  try {
    template.value = await templateStore.fetchTemplateById(templateId.value);
  } catch (error) {
    console.error('获取模板详情失败:', error);
    message.error('获取模板详情失败');
  } finally {
    loading.value = false;
  }
};

// 加载版本历史
const loadVersionHistory = async () => {
  versionsLoading.value = true;
  try {
    templateVersions.value = await templateStore.fetchTemplateVersions(templateId.value);
  } catch (error) {
    console.error('获取版本历史失败:', error);
    message.error('获取版本历史失败');
  } finally {
    versionsLoading.value = false;
  }
};

onMounted(() => {
  loadTemplateDetails();
  loadVersionHistory();
});
</script>

<style scoped>
.template-detail-page {
  padding: 20px;
}

.card-container {
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.template-content {
  border: 1px solid #f0f0f0;
  padding: 20px;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  background-color: #fafafa;
}

.version-content {
  border: 1px solid #f0f0f0;
  padding: 20px;
  min-height: 200px;
  max-height: 500px;
  overflow-y: auto;
  background-color: #fafafa;
}
</style> 