<template>
  <div class="template-edit-page">
    <a-card class="card-container">
      <template #title>
        <div class="flex justify-between items-center">
          <h2>{{ isEdit ? '编辑模板' : '新增模板' }}</h2>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
        </div>
      </template>

      <a-spin :spinning="loading">
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 16 }"
        >
          <!-- 基本信息 -->
          <a-divider orientation="left">基本信息</a-divider>
          
          <a-form-item label="模板名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入模板名称" />
          </a-form-item>
          
          <a-form-item label="模板编码" name="code">
            <a-input v-model:value="formState.code" placeholder="请输入模板编码" />
          </a-form-item>
          
          <a-form-item label="合同类型" name="category">
            <a-select v-model:value="formState.category" placeholder="请选择合同类型">
              <a-select-option v-for="(label, value) in contractCategoryOptions" :key="value" :value="value">
                {{ label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="合同性质" name="contractNature">
            <a-select v-model:value="formState.contractNature" placeholder="请选择合同性质">
              <a-select-option v-for="(label, value) in contractNatureOptions" :key="value" :value="value">
                {{ label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="编辑模式" name="editMode">
            <a-select v-model:value="formState.editMode" placeholder="请选择编辑模式">
              <a-select-option v-for="(label, value) in editModeOptions" :key="value" :value="value">
                {{ label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="状态" name="status">
            <a-radio-group v-model:value="formState.status">
              <a-radio value="enabled">启用</a-radio>
              <a-radio value="disabled">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" :rows="3" placeholder="请输入备注信息" />
          </a-form-item>
          
          <!-- 模板文件上传 -->
          <a-divider orientation="left">模板文件</a-divider>

          <a-form-item label="上传文件" name="files">
            <a-upload
              v-model:file-list="formState.files"
              name="file"
              :multiple="true"
              action="/api/upload" 
              :before-upload="beforeUpload"
              @change="handleUploadChange"
              accept=".doc,.docx,.pdf"
            >
              <a-button>
                <upload-outlined />
                点击上传
              </a-button>
            </a-upload>
            <div class="ant-upload-text" style="margin-top: 8px;">支持格式：.doc, .docx, .pdf，可上传多个文件</div>
          </a-form-item>
          
          <!-- 模板内容 -->
          <a-divider orientation="left">模板内容</a-divider>
          
          <a-form-item label="内容" name="content" :wrapper-col="{ span: 20 }">
            <a-tabs v-model:activeKey="activeTab">
              <a-tab-pane key="editor" tab="编辑器">
                <div class="editor-container">
                  <!-- 富文本编辑器区域 -->
                  <div class="rich-editor" ref="editorContainer"></div>
                </div>
              </a-tab-pane>
              <a-tab-pane key="preview" tab="预览">
                <div class="preview-container" v-html="formState.content"></div>
              </a-tab-pane>
            </a-tabs>
          </a-form-item>
          
          <!-- 变量设置 -->
          <a-divider orientation="left">变量设置</a-divider>
          
          <a-form-item label="可用变量" :wrapper-col="{ span: 20 }">
            <a-table
              :columns="variableColumns"
              :data-source="templateVariables"
              :pagination="false"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'action'">
                  <a-button type="link" @click="insertVariable(record)">插入</a-button>
                </template>
              </template>
            </a-table>
          </a-form-item>
          
          <!-- 提交按钮 -->
          <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
            <a-space>
              <a-button type="primary" @click="submitForm">保存</a-button>
              <a-button @click="goBack">取消</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, FormInstance } from 'ant-design-vue';
import { 
  ArrowLeftOutlined, 
  UploadOutlined
} from '@ant-design/icons-vue';
import { 
  useTemplateStore, 
  TemplateStatus, 
  ContractCategory, 
  ContractNature, 
  EditMode,
  Template
} from '@/stores/template';

// 引入编辑器 (mock，实际项目需要引入真实的编辑器)
const mockEditor = {
  create: () => ({ setContents: () => {}, getContents: () => '' }),
  destroy: () => {}
};
// 模拟编辑器，实际项目请替换为真实编辑器如 TinyMCE, CKEditor 等
const Editor = mockEditor;

const route = useRoute();
const router = useRouter();
const templateStore = useTemplateStore();
const formRef = ref<FormInstance>();
const editorContainer = ref<HTMLElement | null>(null);
const editor = ref<any>(null);

const templateId = route.params.id as string;
const isEdit = computed(() => !!templateId);
const loading = ref(false);
const activeTab = ref<string>('editor');

// 表单状态
const formState = reactive<Partial<Template> & { files?: any[] }>({
  name: '',
  code: '',
  category: undefined,
  contractNature: undefined,
  editMode: EditMode.FULL,
  status: TemplateStatus.ENABLED,
  content: '',
  remark: '',
  files: [],
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入模板编码', trigger: 'blur' }],
  category: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  contractNature: [{ required: true, message: '请选择合同性质', trigger: 'change' }],
  editMode: [{ required: true, message: '请选择编辑模式', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
};

// 选项配置
const contractCategoryOptions = {
  [ContractCategory.SALES]: '销售合同',
  [ContractCategory.PURCHASE]: '采购合同',
  [ContractCategory.SERVICE]: '服务合同',
  [ContractCategory.COOPERATION]: '合作协议',
  [ContractCategory.CONFIDENTIALITY]: '保密协议',
  [ContractCategory.OTHER]: '其他合同',
};

const contractNatureOptions = {
  [ContractNature.FRAMEWORK]: '框架协议',
  [ContractNature.NORMAL]: '普通合同',
};

const editModeOptions = {
  [EditMode.FULL]: '全文编辑',
  [EditMode.FILL]: '填空编辑',
};

const statusOptions = {
  [TemplateStatus.ENABLED]: '启用',
  [TemplateStatus.DISABLED]: '禁用',
};

// 模板变量定义
const templateVariables = [
  { id: 1, name: '甲方名称', code: '{party_a}', description: '合同甲方公司名称' },
  { id: 2, name: '乙方名称', code: '{party_b}', description: '合同乙方公司名称' },
  { id: 3, name: '合同金额', code: '{amount}', description: '合同总金额' },
  { id: 4, name: '签约日期', code: '{sign_date}', description: '合同签约日期' },
  { id: 5, name: '开始日期', code: '{start_date}', description: '合同生效日期' },
  { id: 6, name: '结束日期', code: '{end_date}', description: '合同终止日期' },
];

const variableColumns = [
  { title: '变量名称', dataIndex: 'name', key: 'name' },
  { title: '变量代码', dataIndex: 'code', key: 'code' },
  { title: '说明', dataIndex: 'description', key: 'description' },
  { title: '操作', dataIndex: 'action', key: 'action', width: 80 },
];

// 返回列表页
const goBack = () => {
  router.push({ name: 'contracts-templates' });
};

// 插入变量到编辑器
const insertVariable = (variable: any) => {
  if (editor.value) {
    const content = formState.content || '';
    formState.content = content + ' ' + variable.code;
    
    // 如果使用真实编辑器，应该使用编辑器的API插入内容
    // editor.value.insertContent(variable.code);
  }
};

// 处理文件上传
const handleUploadChange = (info: any) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 文件上传成功`);
    // Optionally update formState.files with response data if needed
    // formState.files = info.fileList.map(file => file.response ? file.response.url : file.url);
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 文件上传失败.`);
  }
};

const beforeUpload = (file: any) => {
  const isDoc = file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
  const isPdf = file.type === 'application/pdf';
  if (!isDoc && !isPdf) {
    message.error('只能上传 DOC, DOCX 或 PDF 文件!');
  }
  const isLt10M = file.size / 1024 / 1024 < 10; // Limit to 10MB
  if (!isLt10M) {
    message.error('文件必须小于 10MB!');
  }
  return (isDoc || isPdf) && isLt10M;
};

// 提交表单
const submitForm = () => {
  // 获取编辑器内容并更新到表单
  if (editor.value) {
    // 这里应该从真实编辑器获取内容
    // formState.content = editor.value.getContents();
  }
  
  formRef.value?.validate().then(async () => {
    try {
      loading.value = true;
      
      if (isEdit.value) {
        // 更新模板
        await templateStore.updateTemplate(templateId, formState as Partial<Template>, true);
        message.success('模板更新成功');
      } else {
        // 创建模板
        await templateStore.createTemplate(formState as Template);
        message.success('模板创建成功');
      }
      
      // 返回列表页
      router.push({ name: 'contracts-templates' });
    } catch (error) {
      console.error('保存模板失败:', error);
      message.error('保存失败，请重试');
    } finally {
      loading.value = false;
    }
  });
};

// 加载模板详情
const loadTemplateDetails = async () => {
  if (isEdit.value) {
    loading.value = true;
    try {
      const template = await templateStore.fetchTemplateById(templateId as string);
      if (template) {
        // 更新表单数据
        Object.assign(formState, template);
        
        // 更新编辑器内容
        if (editor.value) {
          // 这里应该使用真实编辑器的API设置内容
          // editor.value.setContents(template.content || '');
        }
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
      message.error('获取模板详情失败');
    } finally {
      loading.value = false;
    }
  }
};

// 初始化编辑器
const initEditor = () => {
  if (editorContainer.value) {
    // 实际开发中，这里应该初始化真实的编辑器
    editor.value = Editor.create();
    
    // 如果是编辑模式，设置编辑器内容
    if (isEdit.value && formState.content) {
      // editor.value.setContents(formState.content);
    }
  }
};

onMounted(() => {
  loadTemplateDetails();
  initEditor();
});

onBeforeUnmount(() => {
  // 销毁编辑器实例
  if (editor.value) {
    // editor.value.destroy();
  }
});
</script>

<style scoped>
.template-edit-page {
  padding: 20px;
}

.card-container {
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 400px;
}

.rich-editor {
  height: 400px;
}

.preview-container {
  border: 1px solid #f0f0f0;
  padding: 20px;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  background-color: #fafafa;
}
</style> 