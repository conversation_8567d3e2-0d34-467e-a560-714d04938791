<template>
  <div class="contract-list-container">
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">合同管理</h2>
          <p class="text-gray-500 mt-1">管理所有合同信息和执行状态</p>
        </div>
        <a-button type="primary" @click="handleAddContract">
          <template #icon><plus-outlined /></template>
          新增合同
        </a-button>
      </div>
    </div>
    
    <!-- 搜索和筛选区域 - 参考项目管理模块的简洁设计 -->
    <a-card class="mb-6" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="合同名称/编号/客户">
          <a-input v-model:value="searchForm.keyword" placeholder="请输入合同名称、编号或客户名称" allowClear style="width: 240px" />
            </a-form-item>
        
            <a-form-item label="合同类别">
          <a-select v-model:value="searchForm.category" style="width: 120px" placeholder="请选择" allowClear>
                <a-select-option value="sales">销售合同</a-select-option>
                <a-select-option value="procurement">采购合同</a-select-option>
              </a-select>
            </a-form-item>
        
            <a-form-item label="合同状态">
          <a-select v-model:value="searchForm.status" style="width: 120px" placeholder="请选择" allowClear>
                <a-select-option value="draft">草稿</a-select-option>
                <a-select-option value="review">审核中</a-select-option>
                <a-select-option value="signed">已签署</a-select-option>
                <a-select-option value="executing">执行中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="terminated">已终止</a-select-option>
              </a-select>
            </a-form-item>
        
        <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <div class="flex justify-end mb-4">
        <a-space>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            导出
          </a-button>
          <a-button @click="showColumnConfig">
            <template #icon><setting-outlined /></template>
            字段配置
          </a-button>
          <a-button @click="fetchContractList">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
      <a-table
        :columns="visibleColumns"
        :data-source="contractList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 自定义列内容 -->
        <template #bodyCell="{ column, record }">
          <!-- 合同名称列 - 点击跳转详情 -->
          <template v-if="column.dataIndex === 'name'">
            <a @click="() => viewContract(record)" class="font-medium text-blue-600 hover:text-blue-800">{{ record.name }}</a>
          </template>
          
          <!-- 合同编号列 -->
          <template v-if="column.dataIndex === 'code'">
            <span class="font-mono text-gray-600">{{ record.code }}</span>
          </template>
          
          <!-- 客户名称列 -->
          <template v-if="column.dataIndex === 'customerName'">
            <a @click="() => viewCustomer(record)" class="text-blue-600 hover:text-blue-800">{{ record.customerName }}</a>
          </template>
          
          <!-- 合同类别列 -->
          <template v-if="column.dataIndex === 'category'">
            <a-tag :color="getCategoryColor(record.category)">
              {{ getCategoryName(record.category) }}
            </a-tag>
          </template>
          
          <!-- 项目列 -->
          <template v-if="column.dataIndex === 'projectName'">
            <span v-if="record.projectName">{{ record.projectName }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
          
          <!-- 合同类型列 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          
          <!-- 合同状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          
          <!-- 合同金额列 -->
          <template v-if="column.dataIndex === 'amount'">
            ¥{{ formatNumber(record.amount) }}
          </template>
          
          <!-- 操作列 - 去掉查看按钮，更多下拉只保留终止合同 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="() => editContract(record)">编辑</a-button>
              <a-button type="link" size="small" @click="() => manageAttachments(record)">附件</a-button>
              <a-dropdown>
                <a-button type="link" size="small" @click.prevent>
                  更多 <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="4" @click="() => terminateContract(record)">
                      <stop-outlined /> 终止合同
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 附件管理弹框 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      :title="`附件管理 - ${currentContract?.name || ''}`"
      width="1000px"
      :footer="null"
    >
      <div class="mb-4">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-select
              v-model:value="selectedAttachmentCategory"
              placeholder="请选择附件类别"
              style="width: 100%"
              @change="handleCategoryChange"
            >
              <a-select-option v-for="category in attachmentCategories" :key="category.value" :value="category.value">
                {{ category.label }}
              </a-select-option>
              <a-select-option value="__add_new__" class="add-category-option">
                <plus-outlined /> 新增类别
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="16">
            <a-upload
              :file-list="[]"
              :before-upload="beforeUploadAttachment"
              @change="handleAttachmentChange"
              :multiple="true"
              :disabled="!selectedAttachmentCategory || selectedAttachmentCategory === '__add_new__'"
            >
              <a-button type="primary" :disabled="!selectedAttachmentCategory || selectedAttachmentCategory === '__add_new__'">
                <template #icon><upload-outlined /></template>
                上传附件
              </a-button>
            </a-upload>
          </a-col>
        </a-row>
      </div>
      
      <a-table
        :columns="attachmentColumns"
        :data-source="attachmentList"
        :pagination="false"
        row-key="uid"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <!-- 文件名称 -->
          <template v-if="column.dataIndex === 'name'">
            <a @click="() => viewAttachment(record)">{{ record.name }}</a>
          </template>
          
          <!-- 附件类别 -->
          <template v-if="column.dataIndex === 'category'">
            <a-tag :color="getAttachmentCategoryColor(record.category)">
              {{ getAttachmentCategoryName(record.category) }}
            </a-tag>
          </template>
          
          <!-- 附件类型 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag :color="getAttachmentTypeColor(record.type)">
              {{ getAttachmentTypeName(record.type) }}
            </a-tag>
          </template>
          
          <!-- 文件大小 -->
          <template v-if="column.dataIndex === 'size'">
            {{ formatFileSize(record.size) }}
          </template>
          
          <!-- 上传时间 -->
          <template v-if="column.dataIndex === 'uploadTime'">
            {{ formatDate(record.uploadTime) }}
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="() => viewAttachment(record)">查看</a-button>
              <a-button type="link" size="small" danger @click="() => deleteAttachment(record)">删除</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 新增附件类别弹框 -->
    <a-modal
      v-model:visible="addCategoryModalVisible"
      title="新增附件类别"
      @ok="handleAddCategory"
      @cancel="handleCancelAddCategory"
      width="400px"
    >
      <a-form :model="newCategoryForm" ref="categoryFormRef">
        <a-form-item label="类别名称" name="name" :rules="[{ required: true, message: '请输入类别名称' }]">
          <a-input v-model:value="newCategoryForm.name" placeholder="请输入附件类别名称" />
        </a-form-item>
        <a-form-item label="类别描述" name="description">
          <a-textarea v-model:value="newCategoryForm.description" placeholder="请输入类别描述（可选）" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 字段配置弹框 -->
    <a-modal
      v-model:visible="columnConfigVisible"
      title="字段配置"
      @ok="handleColumnConfigSave"
      @cancel="handleColumnConfigCancel"
      width="600px"
    >
      <div class="column-config-content">
        <div class="mb-4">
          <a-alert
            message="拖拽调整字段顺序，勾选控制字段显示"
            type="info"
            show-icon
            banner
          />
        </div>
        
        <div class="column-list">
          <draggable
            v-model="configColumns"
            item-key="key"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <template #item="{ element }">
              <div class="column-item">
                <div class="column-item-content">
                  <div class="drag-handle">
                    <drag-outlined />
                  </div>
                  <a-checkbox 
                    v-model:checked="element.visible"
                    :disabled="element.key === 'action'"
                  >
                    {{ element.title }}
                  </a-checkbox>
                  <div class="column-width">
                    <span class="width-label">宽度:</span>
                    <a-input-number
                      v-model:value="element.width"
                      :min="80"
                      :max="500"
                      size="small"
                      style="width: 80px"
                    />
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
        
        <div class="mt-4">
          <a-space>
            <a-button @click="resetColumnConfig">重置默认</a-button>
            <a-button @click="saveColumnTemplate">保存模板</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 新增/编辑合同抽屉 -->
    <a-drawer
      v-model:visible="contractModalVisible"
      :title="isEdit ? '编辑合同' : '新增合同'"
      placement="right"
      width="1000px"
      @close="handleModalCancel"
    >
      <div class="contract-form-container">
        <a-form
          :model="contractForm"
          :rules="contractFormRules"
          ref="contractFormRef"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
        >
          <div class="contract-form-content">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="合同名称" name="name">
                  <a-input v-model:value="contractForm.name" placeholder="请输入合同名称" />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="合同类别" name="category">
                  <a-select v-model:value="contractForm.category" placeholder="请选择合同类别">
                    <a-select-option value="sales">销售合同</a-select-option>
                    <a-select-option value="procurement">采购合同</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="关联项目" name="projectId">
                  <a-input-group compact>
                    <a-select 
                      v-model:value="contractForm.projectId" 
                      placeholder="请选择项目" 
                      allowClear 
                      show-search
                      style="width: calc(100% - 100px)"
                    >
                      <a-select-option v-for="project in projectOptions" :key="project.id" :value="project.id">
                        {{ project.name }}
                      </a-select-option>
                    </a-select>
                    <a-button @click="openProjectModal" style="width: 100px">新增项目</a-button>
                  </a-input-group>
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <!-- 根据合同类别显示不同的选择字段 -->
                <a-form-item 
                  v-if="contractForm.category === 'sales'" 
                  label="客户名称" 
                  name="customerId"
                >
                  <a-input-group compact>
                    <a-select 
                      v-model:value="contractForm.customerId" 
                      placeholder="请选择客户" 
                      allowClear 
                      show-search
                      style="width: calc(100% - 100px)"
                      :filter-option="filterCustomerOption"
                    >
                      <a-select-option 
                        v-for="customer in customerOptions" 
                        :key="customer.id" 
                        :value="customer.id"
                      >
                        {{ customer.name }} ({{ customer.code || customer.id }})
                      </a-select-option>
                    </a-select>
                    <a-button @click="openCustomerModal" style="width: 100px">新增客户</a-button>
                  </a-input-group>
                </a-form-item>
                <a-form-item 
                  v-if="contractForm.category === 'procurement'" 
                  label="供应商" 
                  name="supplierId"
                >
                  <a-select 
                    v-model:value="contractForm.supplierId" 
                    placeholder="请选择供应商" 
                    allowClear 
                    show-search
                    :filter-option="filterSupplierOption"
                    :loading="loadingSuppliers"
                  >
                    <a-select-option 
                      v-for="supplier in supplierOptions" 
                      :key="supplier.id" 
                      :value="supplier.id"
                    >
                      {{ supplier.name }} ({{ supplier.code }})
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item 
                  v-if="!contractForm.category || (contractForm.category !== 'sales' && contractForm.category !== 'procurement')" 
                  label="合作方名称" 
                  name="partnerName"
                >
                  <a-input v-model:value="contractForm.partnerName" placeholder="请输入合作方名称" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="合同金额" name="amount">
                  <a-input-number
                    v-model:value="contractForm.amount"
                    placeholder="请输入合同金额"
                    :min="0"
                    :step="0.01"
                    :precision="2"
                    style="width: 100%"
                    addon-before="¥"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="签约日期" name="signDate">
                  <a-date-picker
                    v-model:value="contractForm.signDate"
                    placeholder="请选择签约日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="开始日期" name="startDate">
                  <a-date-picker
                    v-model:value="contractForm.startDate"
                    placeholder="请选择开始日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              
              <a-col :span="12">
                <a-form-item label="结束日期" name="endDate">
                  <a-date-picker
                    v-model:value="contractForm.endDate"
                    placeholder="请选择结束日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row>
              <a-col :span="24">
                <a-form-item label="合同备注" name="description" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                  <a-textarea
                    v-model:value="contractForm.description"
                    placeholder="请输入合同备注"
                    :rows="4"
                    show-count
                    :maxlength="500"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
      
      <template #footer>
        <div class="drawer-footer">
          <a-space>
            <a-button @click="handleModalCancel">取消</a-button>
            <a-button type="primary" @click="handleModalOk" :loading="submitting">
              {{ isEdit ? '更新' : '保存' }}
            </a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>

    <!-- 新增客户抽屉 -->
    <a-drawer
      v-model:visible="customerModalVisible"
      title="新增客户"
      placement="right"
      width="700px"
      @close="handleCustomerModalCancel"
    >
      <div class="customer-form-container">
        <a-form
          :model="customerForm"
          :rules="customerFormRules"
          ref="customerFormRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <!-- 客户信息 -->
          <div class="form-section">
            <h4 class="section-title">客户信息</h4>
            
            <a-form-item label="客户名称" name="name">
              <a-input v-model:value="customerForm.name" placeholder="请输入客户名称" />
            </a-form-item>
            
            <a-form-item label="客户类型" name="type">
              <a-select v-model:value="customerForm.type" placeholder="请选择客户类型">
                <a-select-option value="C01">企业</a-select-option>
                <a-select-option value="C02">政府</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="所属行业" name="industry">
              <a-select v-model:value="customerForm.industry" placeholder="请选择所属行业">
                <a-select-option value="I01">政府机构</a-select-option>
                <a-select-option value="I02">金融服务</a-select-option>
                <a-select-option value="I03">信息技术/互联网</a-select-option>
                <a-select-option value="I04">制造与工业</a-select-option>
                <a-select-option value="I05">零售与消费品</a-select-option>
                <a-select-option value="I06">能源与公用事业</a-select-option>
                <a-select-option value="I07">交通与物流</a-select-option>
                <a-select-option value="I08">医疗与健康</a-select-option>
                <a-select-option value="I09">教育与科研</a-select-option>
                <a-select-option value="I10">房地产与建筑</a-select-option>
                <a-select-option value="I11">专业服务</a-select-option>
                <a-select-option value="I12">农林牧渔</a-select-option>
                <a-select-option value="I13">其他/未分类</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="客户规模" name="scale">
              <a-select v-model:value="customerForm.scale" placeholder="请选择客户规模">
                <a-select-option value="large">大型</a-select-option>
                <a-select-option value="medium">中型</a-select-option>
                <a-select-option value="small">小型</a-select-option>
                <a-select-option value="startup">初创</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="客户地址" name="address">
              <a-input v-model:value="customerForm.address" placeholder="请输入客户地址" />
            </a-form-item>
            
            <a-form-item label="客户描述" name="description">
              <a-textarea v-model:value="customerForm.description" placeholder="请输入客户描述" :rows="3" />
            </a-form-item>
          </div>
          
          <!-- 联系信息 -->
          <div class="form-section">
            <h4 class="section-title">联系信息</h4>
            
            <a-form-item label="联系人" name="contactName">
              <a-input v-model:value="customerForm.contactName" placeholder="请输入联系人姓名" />
            </a-form-item>
            
            <a-form-item label="联系电话" name="contactPhone">
              <a-input v-model:value="customerForm.contactPhone" placeholder="请输入联系电话" />
            </a-form-item>
            
            <a-form-item label="联系邮箱" name="contactEmail">
              <a-input v-model:value="customerForm.contactEmail" placeholder="请输入联系邮箱" />
            </a-form-item>
            
            <a-form-item label="职位" name="contactPosition">
              <a-input v-model:value="customerForm.contactPosition" placeholder="请输入联系人职位" />
            </a-form-item>
          </div>
          
          <!-- 开票信息 -->
          <div class="form-section">
            <h4 class="section-title">开票信息</h4>
            
            <a-form-item label="开票公司名称" name="invoiceCompanyName">
              <a-input v-model:value="customerForm.invoiceCompanyName" placeholder="请输入开票公司名称" />
            </a-form-item>
            
            <a-form-item label="纳税人识别号" name="invoiceTaxNumber">
              <a-input v-model:value="customerForm.invoiceTaxNumber" placeholder="请输入纳税人识别号" />
            </a-form-item>
            
            <a-form-item label="开户银行" name="invoiceBank">
              <a-input v-model:value="customerForm.invoiceBank" placeholder="请输入开户银行" />
            </a-form-item>
            
            <a-form-item label="银行账号" name="invoiceBankAccount">
              <a-input v-model:value="customerForm.invoiceBankAccount" placeholder="请输入银行账号" />
            </a-form-item>
            
            <a-form-item label="开票地址" name="invoiceAddress">
              <a-input v-model:value="customerForm.invoiceAddress" placeholder="请输入开票地址" />
            </a-form-item>
            
            <a-form-item label="开票电话" name="invoicePhone">
              <a-input v-model:value="customerForm.invoicePhone" placeholder="请输入开票电话" />
            </a-form-item>
            
            <a-form-item label="开票备注" name="invoiceRemark">
              <a-textarea v-model:value="customerForm.invoiceRemark" placeholder="请输入开票备注" :rows="3" />
            </a-form-item>
          </div>
        </a-form>
      </div>
      
      <template #footer>
        <div class="drawer-footer">
          <a-space>
            <a-button @click="handleCustomerModalCancel">取消</a-button>
            <a-button type="primary" @click="handleCustomerSubmit" :loading="customerSubmitting">保存</a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>

    <!-- 新增项目抽屉 -->
    <a-drawer
      v-model:visible="projectModalVisible"
      title="新增项目"
      placement="right"
      width="1000px"
      @close="handleProjectModalCancel"
    >
      <div class="project-form-container">
        <a-form
          :model="projectForm"
          :rules="projectFormRules"
          ref="projectFormRef"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-row :gutter="24">
            <!-- 左侧：立项信息 -->
            <a-col :span="12">
              <h3 class="form-section-title">立项信息</h3>
              
              <a-form-item label="项目名称" name="name">
                <a-input v-model:value="projectForm.name" placeholder="请输入项目名称" />
              </a-form-item>
              
              <a-form-item label="项目起止时间" name="timeRange">
                <a-range-picker 
                  v-model:value="projectForm.timeRange" 
                  style="width: 100%" 
                  placeholder="['开始日期', '结束日期']"
                />
              </a-form-item>
              
              <a-form-item label="客户信息" name="customerId">
                <a-input-group compact>
                  <a-select 
                    v-model:value="projectForm.customerId" 
                    placeholder="请选择客户" 
                    allowClear 
                    show-search
                    style="width: calc(100% - 100px)"
                  >
                    <a-select-option v-for="customer in customerOptions" :key="customer.id" :value="customer.id">
                      {{ customer.name }}
                    </a-select-option>
                  </a-select>
                  <a-button @click="openCustomerModalFromProject" style="width: 100px">新增客户</a-button>
                </a-input-group>
              </a-form-item>
              
              <a-form-item label="项目类型" name="type">
                <a-select v-model:value="projectForm.type" placeholder="请选择项目类型">
                  <a-select-option value="software">软件开发</a-select-option>
                  <a-select-option value="integration">系统集成</a-select-option>
                  <a-select-option value="implementation">实施交付</a-select-option>
                  <a-select-option value="service">技术服务</a-select-option>
                  <a-select-option value="research">研发项目</a-select-option>
                  <a-select-option value="maintenance">运维服务</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="项目经理" name="managerId">
                <a-select v-model:value="projectForm.managerId" placeholder="请选择项目经理">
                  <a-select-option value="1">张三</a-select-option>
                  <a-select-option value="2">李四</a-select-option>
                  <a-select-option value="3">王五</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="立项原因" name="reason">
                <a-textarea v-model:value="projectForm.reason" placeholder="请输入立项原因" :rows="3" />
              </a-form-item>
              
              <a-form-item label="预计毛利率" name="expectedProfitRate">
                <a-input-number
                  v-model:value="projectForm.expectedProfitRate"
                  placeholder="请输入预计毛利率"
                  :min="0"
                  :max="100"
                  :step="0.1"
                  :precision="1"
                  style="width: 100%"
                  addon-after="%"
                />
              </a-form-item>
              
              <a-form-item label="项目描述" name="description">
                <a-textarea v-model:value="projectForm.description" placeholder="请输入项目描述" :rows="4" />
              </a-form-item>
            </a-col>
            
            <!-- 右侧：最终用户信息 -->
            <a-col :span="12">
              <h3 class="form-section-title">最终用户信息</h3>
              
              <a-form-item>
                <a-checkbox v-model:checked="isEndUserSameAsCustomer" @change="handleEndUserCheckChange">
                  是否最终用户
                </a-checkbox>
              </a-form-item>
              
              <a-form-item label="最终用户名称" name="endUserName">
                <a-input v-model:value="projectForm.endUserName" placeholder="请输入最终用户名称" />
              </a-form-item>
              
              <a-form-item label="最终用户联系人" name="endUserContact">
                <a-input v-model:value="projectForm.endUserContact" placeholder="请输入最终用户联系人" />
              </a-form-item>
              
              <a-form-item label="最终用户电话" name="endUserPhone">
                <a-input v-model:value="projectForm.endUserPhone" placeholder="请输入最终用户电话" />
              </a-form-item>
              
              <a-form-item label="最终用户地址" name="endUserAddress">
                <a-input v-model:value="projectForm.endUserAddress" placeholder="请输入最终用户地址" />
              </a-form-item>
              
              <a-form-item label="最终用户备注" name="endUserRemark">
                <a-textarea v-model:value="projectForm.endUserRemark" placeholder="请输入最终用户备注" :rows="4" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      
      <template #footer>
        <div class="drawer-footer">
          <a-space>
            <a-button @click="handleProjectModalCancel">取消</a-button>
            <a-button type="primary" @click="handleProjectSubmit" :loading="projectSubmitting">保存</a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import draggable from 'vuedraggable';
import dayjs from 'dayjs';
import { useSupplierStore } from '@/stores/supplier';
import { useCustomerStore } from '@/stores/customer';
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  DollarOutlined,
  DownloadOutlined,
  StopOutlined,
  UploadOutlined,
  SettingOutlined,
  DragOutlined
} from '@ant-design/icons-vue';

const router = useRouter();
const supplierStore = useSupplierStore();
const customerStore = useCustomerStore();

// 搜索表单
const searchForm = reactive({
    keyword: '', // 合并搜索：合同名称/编号/客户名称
  category: undefined,
    status: undefined,
});

// 供应商相关状态
const supplierOptions = ref<any[]>([]);
const loadingSuppliers = ref(false);

// 统计数据
const stats = reactive({
  total: 96,
  growthRate: 15.7,
  newMonthly: 12,
  newGrowth: 8.3,
  totalAmount: '12,580,000',
  amountRate: 78.6,
  paymentRate: 85.2,
  paymentChange: 3.5,
});

// 表格列定义
const columns = [
  {
    title: '合同编号',
    dataIndex: 'code',
    key: 'code',
    width: 200,
  },
  {
    title: '合同名称',
    dataIndex: 'name',
    key: 'name',
    width: 220,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 180,
  },
  {
    title: '合同类别',
    dataIndex: 'category',
    key: 'category',
    width: 100,
  },
  {
    title: '项目',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 150,
  },
  {
    title: '合同类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '合同金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    sorter: (a: any, b: any) => a.amount - b.amount,
  },
  {
    title: '签约日期',
    dataIndex: 'signDate',
    key: 'signDate',
    width: 120,
    sorter: (a: any, b: any) => dayjs(a.signDate).unix() - dayjs(b.signDate).unix(),
  },
  {
    title: '生效日期',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 120,
  },
  {
    title: '到期日期',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 120,
  },
  {
    title: '合同状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
];

// 合同列表数据 - 添加合适的类型定义
interface ContractItem {
  id: number;
  code: string;
  name: string;
  customerName: string;
  type: string;
  category: string; // 合同类别
  projectId?: string; // 关联项目ID
  projectName?: string; // 关联项目名称
  amount: number;
  signDate: string;
  startDate: string;
  endDate: string;
  status: string;
  paymentRate: number;
  attachments: number;
  createTime: string;
  updateTime: string;
}

// 更改类型声明
const contractList = ref<ContractItem[]>([]);
const loading = ref(false);
const projectOptions = ref<any[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 字段配置相关状态
const columnConfigVisible = ref(false);
const configColumns = ref([]);

// 初始化配置列
const initConfigColumns = () => {
  configColumns.value = columns.map(col => ({
    ...col,
    visible: true
  }));
};

// 可见列计算属性
const visibleColumns = computed(() => {
  return configColumns.value.filter(col => col.visible);
});

// 获取合同列表
const fetchContractList = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 根据编码规范生成合同编号的函数
    const generateContractCode = (category: string, projectCode: string, contractIndex: number) => {
      const prefix = category === 'sales' ? 'SAL' : category === 'procurement' ? 'PUR' : 'OTH';
      const contractNumber = String(contractIndex).padStart(3, '0');
      return `${prefix}${contractNumber}-${projectCode}`;
    };

    // 模拟数据
    const mockData = Array.from({ length: 50 }, (_, index) => {
      const category = ['sales', 'procurement'][Math.floor(Math.random() * 2)];
      const projectCode = `P${String(index % 10 + 1).padStart(3, '0')}-C250624${String(index % 5 + 1).padStart(3, '0')}`;
      const contractCode = generateContractCode(category, projectCode, index % 3 + 1);
      
      return {
      id: index + 1,
        code: contractCode,
      name: ['软件开发服务合同', '产品销售合同', '技术咨询服务合同', '系统集成合同', '运维服务合同'][Math.floor(Math.random() * 5)] + (index + 1),
      customerName: ['北京科技有限公司', '上海网络科技有限公司', '广州数据科技有限公司', '深圳智能科技有限公司', '杭州软件有限公司'][Math.floor(Math.random() * 5)],
      type: ['product', 'service', 'project', 'framework'][Math.floor(Math.random() * 4)],
        category: category,
        projectId: Math.random() > 0.3 ? projectCode : undefined,
      projectName: Math.random() > 0.3 ? ['新零售系统项目', '医疗平台项目', '云计算平台项目', '智能家居项目', 'AI助手项目'][Math.floor(Math.random() * 5)] : undefined,
      amount: Math.floor(Math.random() * 1000000) + 50000,
      signDate: dayjs().subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD'),
      startDate: dayjs().subtract(Math.floor(Math.random() * 300), 'day').format('YYYY-MM-DD'),
      endDate: dayjs().add(Math.floor(Math.random() * 365) + 30, 'day').format('YYYY-MM-DD'),
      status: ['draft', 'review', 'signed', 'executing', 'completed', 'terminated'][Math.floor(Math.random() * 6)],
      paymentRate: Math.floor(Math.random() * 100),
      attachments: Math.floor(Math.random() * 5),
      createTime: dayjs().subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs().subtract(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD HH:mm:ss'),
      };
    });
    
    // 根据搜索条件过滤
    let filteredData = [...mockData];
    
    if (searchForm.keyword) {
      filteredData = filteredData.filter(item => 
        item.name.includes(searchForm.keyword) || 
        item.code.includes(searchForm.keyword) || 
        item.customerName.includes(searchForm.keyword)
      );
    }
    
    if (searchForm.category) {
      filteredData = filteredData.filter(item => item.category === searchForm.category);
    }
    
    if (searchForm.status) {
      filteredData = filteredData.filter(item => item.status === searchForm.status);
    }
    
    // 应用分页
    const start = (pagination.current - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    
    // 更新列表数据和分页信息
    contractList.value = filteredData.slice(start, end);
    pagination.total = filteredData.length;
    
    loading.value = false;
  }, 500);
};

// 字段配置相关方法
const showColumnConfig = () => {
  columnConfigVisible.value = true;
};

const handleColumnConfigSave = () => {
  // 保存配置到本地存储
  localStorage.setItem('contract-columns-config', JSON.stringify(configColumns.value));
  message.success('字段配置已保存');
  columnConfigVisible.value = false;
};

const handleColumnConfigCancel = () => {
  // 重新加载配置
  loadColumnConfig();
  columnConfigVisible.value = false;
};

const resetColumnConfig = () => {
  initConfigColumns();
  message.success('已重置为默认配置');
};

const saveColumnTemplate = () => {
  // 保存为模板
  localStorage.setItem('contract-columns-template', JSON.stringify(configColumns.value));
  message.success('已保存为模板');
};

const loadColumnConfig = () => {
  const saved = localStorage.getItem('contract-columns-config');
  if (saved) {
    try {
      const savedConfig = JSON.parse(saved);
      // 确保新增的列也能显示
      const currentKeys = columns.map(col => col.key);
      const savedKeys = savedConfig.map(col => col.key);
      
      configColumns.value = columns.map(col => {
        const savedCol = savedConfig.find(saved => saved.key === col.key);
        return {
          ...col,
          visible: savedCol ? savedCol.visible : true,
          width: savedCol ? savedCol.width : col.width
        };
      });
    } catch (error) {
      console.error('加载字段配置失败:', error);
      initConfigColumns();
    }
  } else {
    initConfigColumns();
  }
};

const onDragStart = () => {
  // 拖拽开始
};

const onDragEnd = () => {
  // 拖拽结束，可以在这里保存顺序
};

// 初始化
onMounted(() => {
  // 初始化项目选项
  projectOptions.value = Array.from({ length: 10 }, (_, index) => ({
    id: `PI${String(10000 + index).padStart(5, '0')}`,
    name: `${['新零售系统', '医疗平台', '云计算平台', '智能家居', 'AI助手'][index % 5]}项目${index + 1}`,
  }));
  
  // 初始化字段配置
  loadColumnConfig();
  
  // 预加载供应商数据
  loadSupplierOptions();
  
  // 预加载客户数据
  loadCustomerOptions();
  
  // 检查路由参数，如果有项目ID则自动筛选
  const route = router.currentRoute.value;
  if (route.query.projectId) {
    (searchForm as any).projectId = route.query.projectId as string;
  }
  
  fetchContractList();
});

// 格式化数字
const formatNumber = (num: number) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 获取合同类型名称
const getTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    product: '产品销售',
    service: '服务合同',
    project: '项目合同',
    framework: '框架协议',
  };
  return typeMap[type] || type;
};

// 获取合同类别名称
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    sales: '销售合同',
    procurement: '采购合同',
  };
  return categoryMap[category] || category;
};

// 获取合同类别颜色
const getCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    sales: 'green',
    procurement: 'orange',
  };
  return colorMap[category] || 'default';
};

// 获取合同类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    product: 'blue',
    service: 'green',
    project: 'purple',
    framework: 'orange',
  };
  return colorMap[type] || 'default';
};

// 获取合同状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    review: '审核中',
    signed: '已签署',
    executing: '执行中',
    completed: '已完成',
    terminated: '已终止',
  };
  return statusMap[status] || status;
};

// 获取合同状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'default',
    review: 'processing',
    signed: 'success',
    executing: 'blue',
    completed: 'green',
    terminated: 'error',
  };
  return colorMap[status] || 'default';
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchContractList();
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchContractList();
};

// 重置搜索
const resetSearch = () => {
    searchForm.keyword = '';
  searchForm.category = undefined;
    searchForm.status = undefined;
  
  pagination.current = 1;
  fetchContractList();
};

// 处理添加合同
const handleAddContract = () => {
  contractModalVisible.value = true;
  isEdit.value = false;
  resetForm();
};

// 处理导出
const handleExport = () => {
  message.success('合同数据导出成功');
};

// 查看合同详情
const viewContract = (record: any) => {
  message.info('合同详情查看功能开发中');
};

// 编辑合同
const editContract = (record: any) => {
  isEdit.value = true;
  
  // 填充表单数据
  contractForm.value = {
    id: record.id,
    name: record.name,
    code: record.code,
    category: record.category,
    type: record.type,
    customerId: record.customerId,
    projectId: record.projectId,
    amount: record.amount,
    signDate: record.signDate ? dayjs(record.signDate) : null,
    startDate: record.startDate ? dayjs(record.startDate) : null,
    endDate: record.endDate ? dayjs(record.endDate) : null,
    description: record.description || '',
  };
  
  contractModalVisible.value = true;
};

// 查看客户详情
const viewCustomer = (record: any) => {
  router.push(`/customer/detail/${record.customerId || 1}`);
};

// 终止合同
const terminateContract = (record: any) => {
  Modal.confirm({
    title: '确定要终止该合同吗？',
    content: '终止后合同状态将变更为"已终止"，请确认。',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      message.success(`合同 ${record.name} 已终止`);
      // 更新合同状态
      record.status = 'terminated';
    },
  });
};

// 管理附件
const manageAttachments = (record: any) => {
  attachmentModalVisible.value = true;
  currentContract.value = record;
  selectedAttachmentCategory.value = '';
  // 模拟附件数据
  attachmentList.value = [
    {
      uid: '1',
      name: '合同文件.pdf',
      category: 'contract',
      type: 'pdf',
      size: 1024000,
      uploadTime: '2024-01-15 10:30:00',
      url: '/files/contract.pdf'
    },
    {
      uid: '2', 
      name: '发票.jpg',
      category: 'invoice',
      type: 'jpg',
      size: 512000,
      uploadTime: '2024-01-20 14:20:00',
      url: '/files/invoice.jpg'
    },
    {
      uid: '3',
      name: '验收报告.docx',
      category: 'acceptance',
      type: 'docx',
      size: 2048000,
      uploadTime: '2024-01-25 16:45:00',
      url: '/files/acceptance.docx'
    }
  ];
};

// 附件管理弹框
const attachmentModalVisible = ref(false);
const currentContract = ref<ContractItem | null>(null);
const attachmentList = ref<any[]>([]);
const selectedAttachmentCategory = ref('');
const addCategoryModalVisible = ref(false);
const categoryFormRef = ref(null);

// 附件类别管理
const attachmentCategories = ref([
  { value: 'contract', label: '合同文件' },
  { value: 'invoice', label: '发票文件' },
  { value: 'acceptance', label: '验收文件' },
  { value: 'other', label: '其他附件' },
]);

// 新增类别表单
const newCategoryForm = ref({
  name: '',
  description: '',
});

const attachmentColumns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '附件类别',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '附件类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    key: 'size',
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    key: 'uploadTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
];

// 获取附件类型名称
const getAttachmentTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    pdf: 'PDF',
    doc: 'Word',
    xls: 'Excel',
    ppt: 'PPT',
    txt: '文本',
    jpg: '图片',
    png: '图片',
    gif: '图片',
  };
  return typeMap[type] || type;
};

// 获取附件类型颜色
const getAttachmentTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    pdf: 'blue',
    doc: 'green',
    xls: 'orange',
    ppt: 'purple',
    txt: 'default',
    jpg: 'red',
    png: 'red',
    gif: 'red',
  };
  return colorMap[type] || 'default';
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 上传附件
const beforeUploadAttachment = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword', 
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/jpg'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持上传 PDF、DOC、DOCX、XLS、XLSX、JPG、PNG 格式的文件！');
    return false;
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB！');
    return false;
  }
  
  return true;
};

// 处理附件变化
const handleAttachmentChange = (info: any) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 附件上传成功`);
    // 添加到附件列表
    attachmentList.value.push({
      uid: Date.now().toString(),
      name: info.file.name,
      category: selectedAttachmentCategory.value,
      type: info.file.name.split('.').pop()?.toLowerCase() || 'unknown',
      size: info.file.size,
      uploadTime: new Date().toISOString(),
      url: info.file.response?.url || '#'
    });
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 附件上传失败`);
  }
};

// 查看附件
const viewAttachment = (record: any) => {
  window.open(record.url, '_blank');
};

// 删除附件
const deleteAttachment = (record: any) => {
  Modal.confirm({
    title: '确定要删除该附件吗？',
    content: '删除后无法恢复，请确认。',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      attachmentList.value = attachmentList.value.filter(item => item.uid !== record.uid);
      message.success('附件删除成功');
    },
  });
};

// 处理附件类别变化
const handleCategoryChange = (value: string) => {
  if (value === '__add_new__') {
    addCategoryModalVisible.value = true;
    selectedAttachmentCategory.value = '';
  }
};

const getAttachmentCategoryName = (category: string) => {
  const categoryItem = attachmentCategories.value.find(item => item.value === category);
  return categoryItem ? categoryItem.label : category;
};

const getAttachmentCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    contract: 'blue',
    invoice: 'green',
    acceptance: 'purple',
    other: 'orange',
  };
  return colorMap[category] || 'default';
};

const handleAddCategory = () => {
  // 表单验证
  if (categoryFormRef.value) {
    (categoryFormRef.value as any).validate().then(() => {
      // 添加新类别到列表
      const newCategoryValue = newCategoryForm.value.name.toLowerCase().replace(/\s+/g, '_');
      attachmentCategories.value.push({
        value: newCategoryValue,
        label: newCategoryForm.value.name
      });
      
      message.success('附件类别添加成功');
      
      // 重置表单并关闭弹框
      newCategoryForm.value = { name: '', description: '' };
      addCategoryModalVisible.value = false;
    }).catch((error: any) => {
      console.error('表单验证失败:', error);
    });
  }
};

const handleCancelAddCategory = () => {
  newCategoryForm.value = { name: '', description: '' };
  addCategoryModalVisible.value = false;
};

// 合同管理相关变量
const contractModalVisible = ref(false);
const customerModalVisible = ref(false);
const projectModalVisible = ref(false);
const isEdit = ref(false);
const submitting = ref(false);
const customerSubmitting = ref(false);
const projectSubmitting = ref(false);
const isEndUserSameAsCustomer = ref(false);

const contractFormRef = ref(null);
const customerFormRef = ref(null);
const projectFormRef = ref(null);

// 客户选项
const customerOptions = ref<any[]>([]);
const loadingCustomers = ref(false);

// 合同表单
const contractForm = ref({
  name: '',
  code: '',
  category: undefined,
  customerId: undefined,
  customerName: '',
  supplierId: undefined,
  partnerName: '',
  projectId: undefined,
  type: undefined,
  amount: undefined,
  signDate: undefined,
  startDate: undefined,
  endDate: undefined,
  description: '',
});

// 客户表单
const customerForm = ref({
  name: '',
  type: undefined,
  industry: undefined,
  scale: undefined,
  address: '',
  description: '',
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  contactPosition: '',
  invoiceCompanyName: '',
  invoiceTaxNumber: '',
  invoiceBank: '',
  invoiceBankAccount: '',
  invoiceAddress: '',
  invoicePhone: '',
  invoiceRemark: '',
});

// 项目表单
const projectForm = ref({
  name: '',
  timeRange: [],
  customerId: undefined,
  type: undefined,
  managerId: undefined,
  reason: '',
  expectedProfitRate: undefined,
  description: '',
  endUserName: '',
  endUserContact: '',
  endUserPhone: '',
  endUserAddress: '',
  endUserRemark: '',
});

// 表单验证规则
const contractFormRules = {
  name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
  category: [{ required: true, message: '请选择合同类别', trigger: 'change' }],
  customerId: [{ required: true, message: '请选择关联客户', trigger: 'change' }],
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  type: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  amount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
  signDate: [{ required: true, message: '请选择签约日期', trigger: 'change' }],
};

const customerFormRules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
};

const projectFormRules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  timeRange: [{ required: true, message: '请选择项目起止时间', trigger: 'change' }],
  customerId: [{ required: true, message: '请选择客户信息', trigger: 'change' }],
  type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  managerId: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
};

// 重置表单
const resetForm = () => {
  contractForm.value = {
    name: '',
    code: '',
    category: undefined,
    customerId: undefined,
    customerName: '',
    supplierId: undefined,
    partnerName: '',
    projectId: undefined,
    type: undefined,
    amount: undefined,
    signDate: undefined,
    startDate: undefined,
    endDate: undefined,
    description: '',
  };
};

const resetCustomerForm = () => {
  customerForm.value = {
    name: '',
    type: undefined,
    industry: undefined,
    scale: undefined,
    address: '',
    description: '',
    contactName: '',
    contactPhone: '',
    contactEmail: '',
    contactPosition: '',
    invoiceCompanyName: '',
    invoiceTaxNumber: '',
    invoiceBank: '',
    invoiceBankAccount: '',
    invoiceAddress: '',
    invoicePhone: '',
    invoiceRemark: '',
  };
};

const resetProjectForm = () => {
  projectForm.value = {
    name: '',
    timeRange: [],
    customerId: undefined,
    type: undefined,
    managerId: undefined,
    reason: '',
    expectedProfitRate: undefined,
    description: '',
    endUserName: '',
    endUserContact: '',
    endUserPhone: '',
    endUserAddress: '',
    endUserRemark: '',
  };
  isEndUserSameAsCustomer.value = false;
};

// 生成合同编号 - 按照编码规范
const generateContractCode = () => {
  // 根据编码规范生成合同编号：SAL/PUR/OTH + 流水号 + - + 项目编码
  const category = contractForm.value.category || 'sales';
  const prefix = category === 'sales' ? 'SAL' : category === 'procurement' ? 'PUR' : 'OTH';
  const contractNumber = '001'; // 默认第一个合同
  const projectCode = contractForm.value.projectId || 'P001-C250624001'; // 默认项目编码
  contractForm.value.code = `${prefix}${contractNumber}-${projectCode}`;
};

// 弹窗确认
const handleModalOk = () => {
  if (contractFormRef.value) {
  contractFormRef.value.validate().then(() => {
    submitting.value = true;
    
    setTimeout(() => {
      if (isEdit.value) {
        message.success('合同更新成功');
      } else {
        message.success('合同创建成功');
          
          // 自动生成合同编号
          const category = contractForm.value.category || 'sales';
          const prefix = category === 'sales' ? 'SAL' : category === 'procurement' ? 'PUR' : 'OTH';
          const contractNumber = '001';
          const projectCode = contractForm.value.projectId || 'P001-C250624001';
          const generatedCode = `${prefix}${contractNumber}-${projectCode}`;
        
        // 模拟添加到列表
        const newContract = {
            id: Date.now(),
            name: contractForm.value.name || '',
            code: generatedCode,
            category: contractForm.value.category || 'sales',
          projectId: contractForm.value.projectId,
            projectName: projectOptions.value.find(p => p.id === contractForm.value.projectId)?.name || '',
            type: 'service', // 固定为服务合同
            amount: contractForm.value.amount || 0,
            customerName: '默认客户', // 由于去掉了客户选择，使用默认值
            signDate: contractForm.value.signDate ? dayjs(contractForm.value.signDate).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
            startDate: contractForm.value.startDate ? dayjs(contractForm.value.startDate).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
            endDate: contractForm.value.endDate ? dayjs(contractForm.value.endDate).format('YYYY-MM-DD') : dayjs().add(1, 'year').format('YYYY-MM-DD'),
          status: 'draft',
            paymentRate: 0,
            attachments: 0,
            createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        };
        contractList.value.unshift(newContract);
      }
      
      contractModalVisible.value = false;
      submitting.value = false;
    }, 1000);
  }).catch((error: any) => {
    console.log('验证失败', error);
  });
  }
};

// 弹窗取消
const handleModalCancel = () => {
  contractModalVisible.value = false;
};

// 打开客户弹窗
const openCustomerModal = () => {
  customerModalVisible.value = true;
  resetCustomerForm();
};

// 客户弹窗提交
const handleCustomerSubmit = () => {
  if (customerFormRef.value) {
  customerFormRef.value.validate().then(() => {
    customerSubmitting.value = true;
    
    setTimeout(() => {
      const newCustomer = {
        id: `CU${Date.now()}`,
          name: customerForm.value.name || '',
          type: customerForm.value.type || 'enterprise',
          contactName: customerForm.value.contactName || '',
          contactPhone: customerForm.value.contactPhone || '',
          address: customerForm.value.address || '',
      };
      
      customerOptions.value.push(newCustomer);
      contractForm.value.customerId = newCustomer.id;
      
      message.success('客户创建成功');
      customerModalVisible.value = false;
      customerSubmitting.value = false;
    }, 1000);
  }).catch((error: any) => {
    console.log('验证失败', error);
  });
  }
};

// 客户弹窗取消
const handleCustomerModalCancel = () => {
  customerModalVisible.value = false;
};

// 打开项目弹窗
const openProjectModal = () => {
  projectModalVisible.value = true;
  resetProjectForm();
};

// 从项目中打开客户弹窗
const openCustomerModalFromProject = () => {
  // 先关闭项目弹窗，打开客户弹窗
  projectModalVisible.value = false;
  customerModalVisible.value = true;
  resetCustomerForm();
};

// 项目弹窗提交
const handleProjectSubmit = () => {
  if (projectFormRef.value) {
  projectFormRef.value.validate().then(() => {
    projectSubmitting.value = true;
    
    setTimeout(() => {
      const newProject = {
        id: `P${Date.now()}`,
          name: projectForm.value.name || '',
          customerId: projectForm.value.customerId || '',
          type: projectForm.value.type || 'system_integration',
      };
      
      projectOptions.value.push(newProject);
      contractForm.value.projectId = newProject.id;
      
      message.success('项目创建成功');
      projectModalVisible.value = false;
      projectSubmitting.value = false;
    }, 1000);
  }).catch((error: any) => {
    console.log('验证失败', error);
  });
  }
};

// 项目弹窗取消
const handleProjectModalCancel = () => {
  projectModalVisible.value = false;
};

// 处理最终用户复选框变化
const handleEndUserCheckChange = (e: any) => {
  if (e.target.checked && projectForm.value.customerId) {
    const selectedCustomer = customerOptions.value.find(c => c.id === projectForm.value.customerId);
    if (selectedCustomer) {
      projectForm.value.endUserName = selectedCustomer.name;
      projectForm.value.endUserContact = selectedCustomer.contactName;
      projectForm.value.endUserPhone = selectedCustomer.contactPhone;
      projectForm.value.endUserAddress = selectedCustomer.address || '';
    }
  } else if (!e.target.checked) {
    projectForm.value.endUserName = '';
    projectForm.value.endUserContact = '';
    projectForm.value.endUserPhone = '';
    projectForm.value.endUserAddress = '';
    projectForm.value.endUserRemark = '';
  }
};

// 加载供应商选项
const loadSupplierOptions = async () => {
  try {
    loadingSuppliers.value = true;
    const result = await supplierStore.fetchSuppliers({
      current: 1,
      pageSize: 1000 // 获取所有供应商
    });
    supplierOptions.value = result.data;
  } catch (error) {
    console.error('加载供应商列表失败:', error);
    message.error('加载供应商列表失败');
  } finally {
    loadingSuppliers.value = false;
  }
};

// 供应商选项过滤
const filterSupplierOption = (input: string, option: any) => {
  const supplier = supplierOptions.value.find(s => s.id === option.value);
  if (!supplier) return false;
  
  const searchText = input.toLowerCase();
  return supplier.name.toLowerCase().includes(searchText) || 
         supplier.code.toLowerCase().includes(searchText);
};

// 加载客户选项
const loadCustomerOptions = async () => {
  try {
    loadingCustomers.value = true;
    const result = await customerStore.fetchCustomers({
      current: 1,
      pageSize: 1000 // 获取所有客户
    });
    customerOptions.value = result.data;
  } catch (error) {
    console.error('加载客户列表失败:', error);
    message.error('加载客户列表失败');
  } finally {
    loadingCustomers.value = false;
  }
};

// 客户选项过滤
const filterCustomerOption = (input: string, option: any) => {
  const customer = customerOptions.value.find(c => c.id === option.value);
  if (!customer) return false;
  
  const searchText = input.toLowerCase();
  return customer.name.toLowerCase().includes(searchText) || 
         (customer.code && customer.code.toLowerCase().includes(searchText));
};
</script>

<style scoped>
.contract-list-container {
  background-color: #f0f2f5;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #262626;
}

.form-section {
  margin-bottom: 24px;
}

.drawer-footer {
  text-align: right;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.contract-form-content {
  padding: 0 16px;
}

/* 字段配置样式 */
.column-config-content {
  .column-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 8px;
  }
  
  .column-item {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .column-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 4px;
    cursor: move;
    
    &:hover {
      background: #f0f0f0;
    }
  }
  
  .drag-handle {
    margin-right: 8px;
    color: #999;
    cursor: move;
  }
  
  .column-width {
    margin-left: auto;
    display: flex;
    align-items: center;
    
    .width-label {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }
}
</style> 