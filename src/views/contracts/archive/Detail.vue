<template>
  <div class="contract-archive-detail-page">
    <a-card class="card-container">
      <template #title>
        <div class="flex justify-between items-center">
          <h2>归档详情</h2>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
        </div>
      </template>

      <a-spin :spinning="loading">
        <!-- 合同基本信息 -->
        <div v-if="contract" class="mb-6">
          <a-descriptions title="合同基本信息" bordered :column="2">
            <a-descriptions-item label="合同编号">{{ contract.contractCode }}</a-descriptions-item>
            <a-descriptions-item label="合同名称">{{ contract.contractName }}</a-descriptions-item>
            <a-descriptions-item label="合同类型">{{ getContractTypeName(contract.contractType) }}</a-descriptions-item>
            <a-descriptions-item label="合同状态">
              <a-tag :color="getStatusColor(contract.status)">
                {{ getStatusName(contract.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="甲方">{{ contract.partyA }}</a-descriptions-item>
            <a-descriptions-item label="乙方">{{ contract.partyB }}</a-descriptions-item>
            <a-descriptions-item label="合同金额">¥{{ formatNumber(contract.amount) }}</a-descriptions-item>
            <a-descriptions-item label="归档时间">{{ formatDate(contract.archivedAt) }}</a-descriptions-item>
            <a-descriptions-item label="归档人">{{ contract.archivedBy }}</a-descriptions-item>
            <a-descriptions-item label="归档编号">{{ contract.archiveNumber || '-' }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 归档信息 -->
        <div class="mb-6">
          <a-divider orientation="left">归档信息</a-divider>
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="存储位置">{{ contract?.archiveLocation || '-' }}</a-descriptions-item>
            <a-descriptions-item label="归档类型">{{ getArchiveTypeName(contract?.archiveType) }}</a-descriptions-item>
            <a-descriptions-item label="备注" :span="2">{{ contract?.archiveRemark || '无备注' }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 合同文件列表 -->
        <div class="mb-6">
          <a-divider orientation="left">合同文件</a-divider>
          <a-table
            :columns="fileColumns"
            :data-source="contractFiles"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a @click="handlePreview(record)">预览</a>
                  <a-divider type="vertical" />
                  <a @click="handleDownload(record)">下载</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 合同跟踪记录 -->
        <div class="mb-6">
          <a-divider orientation="left">操作记录</a-divider>
          <a-timeline>
            <a-timeline-item v-for="(record, index) in contractHistory" :key="index">
              <template #dot>
                <a-tag :color="getHistoryTypeColor(record.type)" class="timeline-dot">
                  {{ getHistoryTypeIcon(record.type) }}
                </a-tag>
              </template>
              <div class="history-item">
                <div class="history-title">{{ getHistoryTypeName(record.type) }}</div>
                <div class="history-time">{{ formatDate(record.timestamp) }}</div>
                <div class="history-content">{{ record.description }}</div>
                <div class="history-operator">操作人: {{ record.operator }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { useContractStore, ContractType, ContractStatus } from '@/stores/contract';

// 定义档案类型枚举（示例）
enum ArchiveType {
  ELECTRONIC = 'electronic',
  PHYSICAL = 'physical',
  BOTH = 'both'
}

// 定义历史记录类型枚举（示例）
enum HistoryType {
  CREATION = 'creation',
  APPROVAL = 'approval',
  SIGNATURE = 'signature',
  MODIFICATION = 'modification',
  ARCHIVE = 'archive'
}

interface ContractFile {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  uploadedBy: string;
  url: string;
}

interface HistoryRecord {
  type: HistoryType;
  timestamp: string;
  description: string;
  operator: string;
}

const route = useRoute();
const router = useRouter();
const contractStore = useContractStore();

const contractId = ref(route.params.id as string);
const contract = ref<any>(null);
const contractFiles = ref<ContractFile[]>([]);
const contractHistory = ref<HistoryRecord[]>([]);
const loading = ref(false);

// 文件列表列定义
const fileColumns = [
  { title: '文件名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type', width: 120 },
  { title: '大小', dataIndex: 'size', key: 'size', width: 120, 
    customRender: ({ text }: { text: number }) => formatFileSize(text) },
  { title: '上传时间', dataIndex: 'uploadedAt', key: 'uploadedAt', width: 180,
    customRender: ({ text }: { text: string }) => formatDate(text) },
  { title: '上传人', dataIndex: 'uploadedBy', key: 'uploadedBy', width: 120 },
  { title: '操作', dataIndex: 'action', key: 'action', width: 150 }
];

// 获取合同类型名称
const getContractTypeName = (type: ContractType) => {
  const typeMap: Record<string, string> = {
    [ContractType.SALES]: '销售合同',
    [ContractType.PURCHASE]: '采购合同',
    [ContractType.SERVICE]: '服务合同',
    [ContractType.FRAMEWORK]: '框架协议',
    [ContractType.OTHER]: '其他合同',
  };
  return typeMap[type] || '未知类型';
};

// 获取合同状态名称
const getStatusName = (status: ContractStatus) => {
  const statusMap: Record<string, string> = {
    [ContractStatus.DRAFT]: '草稿',
    [ContractStatus.REVIEWING]: '审核中',
    [ContractStatus.REJECTED]: '已驳回',
    [ContractStatus.APPROVED]: '已审批',
    [ContractStatus.SIGNED]: '已签署',
    [ContractStatus.COMPLETED]: '已完成',
    [ContractStatus.ARCHIVED]: '已归档',
    [ContractStatus.TERMINATED]: '已终止',
  };
  return statusMap[status] || '未知状态';
};

// 获取档案类型名称
const getArchiveTypeName = (type?: string) => {
  if (!type) return '未指定';
  const typeMap: Record<string, string> = {
    [ArchiveType.ELECTRONIC]: '电子归档',
    [ArchiveType.PHYSICAL]: '实物归档',
    [ArchiveType.BOTH]: '电子+实物',
  };
  return typeMap[type] || '未知类型';
};

// 获取历史记录类型名称
const getHistoryTypeName = (type: HistoryType) => {
  const typeMap: Record<string, string> = {
    [HistoryType.CREATION]: '创建合同',
    [HistoryType.APPROVAL]: '合同审批',
    [HistoryType.SIGNATURE]: '合同签署',
    [HistoryType.MODIFICATION]: '合同修改',
    [HistoryType.ARCHIVE]: '合同归档',
  };
  return typeMap[type] || '未知操作';
};

// 获取历史记录类型颜色
const getHistoryTypeColor = (type: HistoryType) => {
  const colorMap: Record<string, string> = {
    [HistoryType.CREATION]: 'blue',
    [HistoryType.APPROVAL]: 'green',
    [HistoryType.SIGNATURE]: 'purple',
    [HistoryType.MODIFICATION]: 'orange',
    [HistoryType.ARCHIVE]: 'cyan',
  };
  return colorMap[type] || 'default';
};

// 获取历史记录类型图标
const getHistoryTypeIcon = (type: HistoryType) => {
  const iconMap: Record<string, string> = {
    [HistoryType.CREATION]: '创',
    [HistoryType.APPROVAL]: '审',
    [HistoryType.SIGNATURE]: '签',
    [HistoryType.MODIFICATION]: '改',
    [HistoryType.ARCHIVE]: '档',
  };
  return iconMap[type] || '?';
};

// 获取状态颜色
const getStatusColor = (status: ContractStatus) => {
  const colorMap: Record<string, string> = {
    [ContractStatus.DRAFT]: 'default',
    [ContractStatus.REVIEWING]: 'processing',
    [ContractStatus.REJECTED]: 'error',
    [ContractStatus.APPROVED]: 'success',
    [ContractStatus.SIGNED]: 'success',
    [ContractStatus.COMPLETED]: 'success',
    [ContractStatus.ARCHIVED]: 'cyan',
    [ContractStatus.TERMINATED]: 'error',
  };
  return colorMap[status] || 'default';
};

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '-';
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
};

// 返回上一页
const goBack = () => {
  router.push({ name: 'contracts-archive' });
};

// 处理文件预览
const handlePreview = (file: ContractFile) => {
  window.open(file.url, '_blank');
};

// 处理文件下载
const handleDownload = (file: ContractFile) => {
  const a = document.createElement('a');
  a.href = file.url;
  a.download = file.name;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

// 加载合同详情
const loadContractDetails = async () => {
  loading.value = true;
  try {
    contract.value = await contractStore.fetchContractById(contractId.value);
    
    // 模拟加载合同文件列表
    contractFiles.value = [
      {
        id: '1',
        name: `${contract.value.contractName}_已归档.pdf`,
        type: 'PDF',
        size: 2458621,
        uploadedAt: new Date().toISOString(),
        uploadedBy: '档案管理员',
        url: '#'
      },
      {
        id: '2',
        name: `${contract.value.contractName}_附件.zip`,
        type: 'ZIP',
        size: 5236984,
        uploadedAt: new Date().toISOString(),
        uploadedBy: '档案管理员',
        url: '#'
      }
    ];
    
    // 模拟加载合同历史记录
    contractHistory.value = [
      {
        type: HistoryType.CREATION,
        timestamp: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString(),
        description: '创建合同草稿',
        operator: '销售经理'
      },
      {
        type: HistoryType.APPROVAL,
        timestamp: new Date(new Date().setDate(new Date().getDate() - 25)).toISOString(),
        description: '合同审批通过',
        operator: '部门主管'
      },
      {
        type: HistoryType.SIGNATURE,
        timestamp: new Date(new Date().setDate(new Date().getDate() - 20)).toISOString(),
        description: '双方完成签署',
        operator: '法务专员'
      },
      {
        type: HistoryType.ARCHIVE,
        timestamp: new Date(new Date().setDate(new Date().getDate() - 10)).toISOString(),
        description: '合同归档完成',
        operator: '档案管理员'
      }
    ];
  } catch (error) {
    console.error('获取合同详情失败:', error);
    message.error('获取合同详情失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadContractDetails();
});
</script>

<style scoped>
.contract-archive-detail-page {
  padding: 20px;
}

.card-container {
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.timeline-dot {
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
}

.history-item {
  margin-bottom: 16px;
}

.history-title {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 4px;
}

.history-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.history-content {
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #8c8c8c;
}
</style> 