<template>
  <div class="contract-archive">
    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">合同归档管理</h2>
          <p class="text-gray-500 mt-1">管理已完成的合同归档与存储</p>
        </div>
        <div class="flex space-x-3">
          <a-button type="primary" @click="handleArchive">
            <template #icon><folder-add-outlined /></template>
            归档合同
          </a-button>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-4" :bordered="false">
      <a-form layout="horizontal" :model="searchForm" ref="searchFormRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="[16, 16]">
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="合同名称" name="name">
              <a-input v-model:value="searchForm.name" placeholder="请输入合同名称" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="合同编号" name="code">
              <a-input v-model:value="searchForm.code" placeholder="请输入合同编号" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="归档状态" name="status">
              <a-select v-model:value="searchForm.status" placeholder="请选择归档状态" allowClear>
                <a-select-option v-for="item in archiveStatus" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="归档日期" name="archiveDate">
              <a-range-picker v-model:value="searchForm.archiveDate" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="客户名称" name="customerName">
              <a-input v-model:value="searchForm.customerName" placeholder="请输入客户名称" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="负责人" name="managerName">
              <a-input v-model:value="searchForm.managerName" placeholder="请输入负责人姓名" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="8" :sm="12" :xs="24">
            <a-form-item label="合同类型" name="type">
              <a-select v-model:value="searchForm.type" placeholder="请选择合同类型" allowClear>
                <a-select-option v-for="item in contractTypes" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24" class="text-right">
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 数据统计卡片 -->
    <div class="statistics-cards mb-4">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="statistic-card" :bordered="false">
            <statistic title="已归档合同" :value="statistics.archived" :precision="0" />
            <template #extra>
              <folder-outlined style="color: #1890ff; font-size: 32px" />
            </template>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="statistic-card" :bordered="false">
            <statistic title="本月归档" :value="statistics.monthlyArchived" :precision="0" />
            <template #extra>
              <calendar-outlined style="color: #52c41a; font-size: 32px" />
            </template>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="statistic-card" :bordered="false">
            <statistic title="纸质归档" :value="statistics.paperArchived" :precision="0" />
            <template #extra>
              <file-outlined style="color: #faad14; font-size: 32px" />
            </template>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="statistic-card" :bordered="false">
            <statistic title="电子归档" :value="statistics.digitalArchived" :precision="0" />
            <template #extra>
              <cloud-outlined style="color: #eb2f96; font-size: 32px" />
            </template>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 表格视图 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="contractList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
        :scroll="{ x: 1300 }"
      >
        <!-- 自定义列内容 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'name'">
            <a @click="viewDetail(record)">{{ record.name }}</a>
          </template>
          
          <template v-if="column.dataIndex === 'archiveStatus'">
            <a-tag :color="getStatusColor(record.archiveStatus)">
              {{ getStatusText(record.archiveStatus) }}
            </a-tag>
          </template>
          
          <template v-if="column.dataIndex === 'archiveDate'">
            {{ formatDate(record.archiveDate) }}
          </template>
          
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">查看</a-button>
              <a-button type="link" size="small" @click="downloadArchive(record)">下载</a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="updateArchiveInfo(record)">更新归档信息</a-menu-item>
                    <a-menu-item @click="moveArchive(record)">调整归档位置</a-menu-item>
                    <a-menu-item @click="handlePrint(record)">打印归档表</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 归档表单弹窗 -->
    <a-modal
      v-model:visible="archiveModalVisible"
      title="合同归档"
      width="800px"
      @ok="submitArchive"
    >
      <a-form :model="archiveForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="合同选择" name="contractId" :rules="[{ required: true, message: '请选择需要归档的合同' }]">
          <a-select
            v-model:value="archiveForm.contractId"
            placeholder="请选择需要归档的合同"
            show-search
            :options="archivableContracts"
            :filter-option="filterOption"
          />
        </a-form-item>
        <a-form-item label="归档类型" name="archiveType" :rules="[{ required: true, message: '请选择归档类型' }]">
          <a-radio-group v-model:value="archiveForm.archiveType">
            <a-radio value="digital">电子归档</a-radio>
            <a-radio value="paper">纸质归档</a-radio>
            <a-radio value="both">电子和纸质归档</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="archiveForm.archiveType === 'paper' || archiveForm.archiveType === 'both'" label="存放位置" name="location">
          <a-input v-model:value="archiveForm.location" placeholder="请输入纸质文档存放位置" />
        </a-form-item>
        <a-form-item label="归档备注" name="remarks">
          <a-textarea v-model:value="archiveForm.remarks" placeholder="请输入归档备注信息" :rows="4" />
        </a-form-item>
        <a-form-item v-if="archiveForm.archiveType === 'digital' || archiveForm.archiveType === 'both'" label="上传扫描件" name="files">
          <a-upload-dragger
            v-model:fileList="fileList"
            name="file"
            :multiple="true"
            action="/api/upload"
            @change="handleFileChange"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持单个或批量上传。支持PDF、JPG、PNG等格式
            </p>
          </a-upload-dragger>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import {
  FolderAddOutlined,
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  FolderOutlined,
  CalendarOutlined,
  FileOutlined,
  CloudOutlined,
  InboxOutlined
} from '@ant-design/icons-vue';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface Contract {
  id: string;
  name: string;
  code: string;
  type: string;
  archiveStatus: string;
  archiveDate: Date;
  archiveType: string;
  location: string;
  fileUrl: string;
  customerName: string;
  managerName: string;
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const archiveModalVisible = ref(false);
const fileList = ref([]);

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  status: undefined as string | undefined,
  archiveDate: [] as any[],
  customerName: '',
  managerName: '',
  type: undefined as string | undefined,
});

// 归档表单
const archiveForm = reactive({
  contractId: undefined as string | undefined,
  archiveType: 'digital',
  location: '',
  remarks: '',
  files: [] as any[]
});

// 统计数据
const statistics = reactive({
  archived: 145,
  monthlyArchived: 18,
  paperArchived: 76,
  digitalArchived: 69
});

// 合同类型选项
const contractTypes = [
  { label: '销售合同', value: 'sales' },
  { label: '采购合同', value: 'purchase' },
  { label: '服务合同', value: 'service' },
  { label: '合作协议', value: 'cooperation' },
  { label: '其他', value: 'other' }
];

// 归档状态选项
const archiveStatus = [
  { label: '已归档', value: 'archived' },
  { label: '归档中', value: 'inProgress' },
  { label: '待归档', value: 'pending' },
  { label: '异常', value: 'exception' }
];

// 可归档的合同列表
const archivableContracts = ref([
  { value: 'C2023001', label: '销售合同-A公司-产品销售' },
  { value: 'C2023002', label: '采购合同-B公司-原材料采购' },
  { value: 'C2023003', label: '服务合同-C公司-系统维护' },
]);

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'name',
    key: 'name',
    width: 220,
    ellipsis: true
  },
  {
    title: '合同编号',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '合同类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      const found = contractTypes.find(item => item.value === text);
      return found ? found.label : text;
    }
  },
  {
    title: '归档状态',
    dataIndex: 'archiveStatus',
    key: 'archiveStatus',
    width: 120
  },
  {
    title: '归档日期',
    dataIndex: 'archiveDate',
    key: 'archiveDate',
    width: 150,
    sorter: true
  },
  {
    title: '归档类型',
    dataIndex: 'archiveType',
    key: 'archiveType',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      const typeMap: Record<string, string> = {
        digital: '电子归档',
        paper: '纸质归档',
        both: '电子和纸质归档'
      };
      return typeMap[text] || text;
    }
  },
  {
    title: '存放位置',
    dataIndex: 'location',
    key: 'location',
    width: 180,
    ellipsis: true
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 180,
    ellipsis: true
  },
  {
    title: '负责人',
    dataIndex: 'managerName',
    key: 'managerName',
    width: 120
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 合同列表数据
const contractList = ref<Contract[]>([]);

// 获取状态对应的文字
const getStatusText = (status: string): string => {
  const found = archiveStatus.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取状态对应的颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    archived: 'green',
    inProgress: 'blue',
    pending: 'orange',
    exception: 'red'
  };
  return colorMap[status] || 'default';
};

// 格式化日期
const formatDate = (date: Date | string | null): string => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

// 获取归档合同列表
const fetchArchiveList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    contractList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取归档列表失败');
    console.error('获取归档列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const types = contractTypes.map(t => t.value);
  const statuses = archiveStatus.map(s => s.value);
  const archiveTypes = ['digital', 'paper', 'both'];
  const total = 145;
  
  for (let i = 1; i <= 20; i++) {
    const now = new Date();
    const randomDate = new Date(now.getFullYear(), now.getMonth() - Math.floor(Math.random() * 6), Math.floor(Math.random() * 28) + 1);
    
    items.push({
      id: `A2023${String(i).padStart(4, '0')}`,
      name: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      code: `HT-${new Date().getFullYear()}-${String(i).padStart(4, '0')}`,
      type: types[Math.floor(Math.random() * types.length)],
      archiveStatus: statuses[Math.floor(Math.random() * statuses.length)],
      archiveDate: randomDate,
      archiveType: archiveTypes[Math.floor(Math.random() * archiveTypes.length)],
      location: ['档案室A区-12架-3层', '档案室B区-08架-2层', '电子档案服务器', '法务部档案柜-B层'][Math.floor(Math.random() * 4)],
      fileUrl: i % 3 === 0 ? 'https://example.com/files/contract-123.pdf' : '',
      customerName: `客户${String.fromCharCode(65 + Math.floor(Math.random() * 26))}公司`,
      managerName: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`
    });
  }

  return {
    items,
    total
  };
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchArchiveList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchArchiveList();
};

// 表格变更
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchArchiveList();
};

// 查看详情
const viewDetail = (record: Contract) => {
  router.push(`/contracts/detail/${record.id}`);
};

// 打开归档表单
const handleArchive = () => {
  archiveModalVisible.value = true;
  archiveForm.contractId = undefined;
  archiveForm.archiveType = 'digital';
  archiveForm.location = '';
  archiveForm.remarks = '';
  fileList.value = [];
};

// 提交归档
const submitArchive = () => {
  if (!archiveForm.contractId) {
    message.error('请选择需要归档的合同');
    return;
  }

  // 模拟提交
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    archiveModalVisible.value = false;
    message.success('合同归档提交成功');
    fetchArchiveList();
  }, 1000);
};

// 处理文件上传变化
const handleFileChange = (info: any) => {
  const { status } = info.file;
  if (status === 'done') {
    message.success(`${info.file.name} 文件上传成功`);
  } else if (status === 'error') {
    message.error(`${info.file.name} 文件上传失败`);
  }
};

// 下载归档文件
const downloadArchive = (record: Contract) => {
  if (record.fileUrl) {
    // 实际项目中应该是window.open(record.fileUrl)或使用下载API
    message.success('开始下载归档文件');
  } else {
    message.warning('该合同没有电子归档文件');
  }
};

// 更新归档信息
const updateArchiveInfo = (record: Contract) => {
  message.info(`更新 ${record.name} 的归档信息`);
  // 这里应该打开一个编辑弹窗
};

// 移动归档位置
const moveArchive = (record: Contract) => {
  message.info(`调整 ${record.name} 的归档位置`);
  // 这里应该打开一个位置选择弹窗
};

// 打印归档表
const handlePrint = (record: Contract) => {
  message.success(`正在生成 ${record.name} 的归档表，请稍候...`);
  // 实际应该调用打印API或打开打印预览页面
};

// 导出数据
const handleExport = () => {
  message.success('归档数据导出成功');
};

// 下拉选择过滤
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

onMounted(() => {
  fetchArchiveList();
});
</script>

<style scoped>
.contract-archive {
  background-color: #f0f2f5;
}

.page-header {
  position: relative;
  margin-bottom: 16px;
}

.page-header h2 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

.statistic-card {
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}
</style> 