<template>
  <div class="contract-detail">
    <!-- 页面头部 -->
    <div class="mb-4 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <a-button @click="router.back()">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
        <h2 class="text-xl font-bold m-0">{{ contractInfo.name }}</h2>
        <a-tag :color="getStatusColor(contractInfo.status)">
          {{ getStatusName(contractInfo.status) }}
        </a-tag>
      </div>
      <div class="flex gap-2">
        <a-button type="primary" @click="handleEdit">
          <template #icon><edit-outlined /></template>
          编辑合同
        </a-button>
        <a-button @click="handleDownload">
          <template #icon><download-outlined /></template>
          下载合同
        </a-button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 基本信息 -->
      <a-card title="基本信息" class="lg:col-span-2">
        <a-descriptions :column="2">
          <a-descriptions-item label="合同编号">
            {{ contractInfo.code }}
          </a-descriptions-item>
          <a-descriptions-item label="合同名称">
            {{ contractInfo.name }}
          </a-descriptions-item>
          <a-descriptions-item label="客户名称">
            {{ contractInfo.customerName }}
          </a-descriptions-item>
          <a-descriptions-item label="合同金额">
            ¥{{ formatMoney(contractInfo.amount) }}
          </a-descriptions-item>
          <a-descriptions-item label="签订日期">
            {{ formatDate(contractInfo.signDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="生效日期">
            {{ formatDate(contractInfo.startDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="到期日期">
            {{ formatDate(contractInfo.endDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="合同状态">
            <a-tag :color="getStatusColor(contractInfo.status)">
              {{ getStatusName(contractInfo.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(contractInfo.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDate(contractInfo.updateTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ contractInfo.remark || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 付款信息 -->
      <a-card title="付款信息">
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <div class="text-gray-500">已付金额</div>
            <div class="text-2xl font-bold mt-2 text-green-500">
              ¥{{ formatMoney(paymentInfo.paidAmount) }}
            </div>
          </div>
          <div class="text-center">
            <div class="text-gray-500">未付金额</div>
            <div class="text-2xl font-bold mt-2 text-red-500">
              ¥{{ formatMoney(paymentInfo.unpaidAmount) }}
            </div>
          </div>
        </div>
        <a-progress
          :percent="paymentInfo.paymentProgress"
          :stroke-color="{ from: '#108ee9', to: '#87d068' }"
          class="mt-4"
        />
        <div class="text-center text-gray-500 mt-2">
          付款进度：{{ paymentInfo.paymentProgress }}%
        </div>
      </a-card>

      <!-- 付款记录 -->
      <a-card title="付款记录" class="lg:col-span-2">
        <template #extra>
          <a-button type="link" @click="handleAddPayment">
            添加付款记录
          </a-button>
        </template>
        <a-table
          :columns="paymentColumns"
          :data-source="paymentRecords"
          :pagination="{ pageSize: 5 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'amount'">
              ¥{{ formatMoney(record.amount) }}
            </template>
            <template v-if="column.key === 'paymentTime'">
              {{ formatDate(record.paymentTime) }}
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 操作记录 -->
      <a-card title="操作记录">
        <a-timeline>
          <a-timeline-item v-for="item in operationRecords" :key="item.id">
            <template #dot>
              <a-avatar size="small">{{ item.operator[0] }}</a-avatar>
            </template>
            <div class="mb-2">{{ item.content }}</div>
            <div class="text-gray-400 text-sm">
              {{ item.operator }} - {{ formatDate(item.operateTime) }}
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>

    <!-- 添加付款记录弹窗 -->
    <a-modal
      v-model:visible="paymentModalVisible"
      title="添加付款记录"
      @ok="handlePaymentModalOk"
      @cancel="handlePaymentModalCancel"
      :confirm-loading="paymentModalLoading"
    >
      <a-form
        ref="paymentFormRef"
        :model="paymentForm"
        :rules="paymentFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="付款金额" name="amount">
          <a-input-number
            v-model:value="paymentForm.amount"
            placeholder="请输入付款金额"
            :min="0"
            :max="paymentInfo.unpaidAmount"
            :precision="2"
            :style="{ width: '100%' }"
          />
        </a-form-item>
        <a-form-item label="付款时间" name="paymentTime">
          <a-date-picker
            v-model:value="paymentForm.paymentTime"
            placeholder="请选择付款时间"
            :style="{ width: '100%' }"
          />
        </a-form-item>
        <a-form-item label="付款方式" name="paymentMethod">
          <a-select
            v-model:value="paymentForm.paymentMethod"
            placeholder="请选择付款方式"
            :style="{ width: '100%' }"
          >
            <a-select-option value="bank">银行转账</a-select-option>
            <a-select-option value="cash">现金支付</a-select-option>
            <a-select-option value="other">其他方式</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="paymentForm.remark"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  LeftOutlined,
  EditOutlined,
  DownloadOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();

// 合同基本信息
const contractInfo = ref({
  id: '',
  code: 'HT202401001',
  name: '示例合同',
  customerName: '示例客户',
  amount: 100000,
  signDate: '2024-01-01',
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  status: 'active',
  createTime: '2024-01-01 12:00:00',
  updateTime: '2024-01-02 15:30:00',
  remark: '这是一个示例合同',
});

// 付款信息
const paymentInfo = ref({
  paidAmount: 60000,
  unpaidAmount: 40000,
  paymentProgress: 60,
});

// 付款记录列
const paymentColumns = [
  { title: '付款金额', dataIndex: 'amount', key: 'amount', width: 150 },
  { title: '付款时间', dataIndex: 'paymentTime', key: 'paymentTime', width: 150 },
  { title: '付款方式', dataIndex: 'paymentMethod', key: 'paymentMethod', width: 120 },
  { title: '经办人', dataIndex: 'operator', key: 'operator', width: 120 },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
];

// 付款记录
const paymentRecords = ref([
  {
    id: '1',
    amount: 30000,
    paymentTime: '2024-01-15',
    paymentMethod: '银行转账',
    operator: '张三',
    remark: '首付款',
  },
  {
    id: '2',
    amount: 30000,
    paymentTime: '2024-02-15',
    paymentMethod: '银行转账',
    operator: '李四',
    remark: '第二期付款',
  },
]);

// 操作记录
const operationRecords = ref([
  {
    id: '1',
    content: '创建合同',
    operator: '张三',
    operateTime: '2024-01-01 12:00:00',
  },
  {
    id: '2',
    content: '添加付款记录',
    operator: '李四',
    operateTime: '2024-01-15 14:30:00',
  },
  {
    id: '3',
    content: '添加付款记录',
    operator: '李四',
    operateTime: '2024-02-15 16:20:00',
  },
]);

// 付款记录弹窗
const paymentModalVisible = ref(false);
const paymentModalLoading = ref(false);
const paymentFormRef = ref();
const paymentForm = reactive({
  amount: undefined,
  paymentTime: undefined,
  paymentMethod: undefined,
  remark: '',
});

// 付款记录表单校验规则
const paymentFormRules = {
  amount: [{ required: true, message: '请输入付款金额', trigger: 'change' }],
  paymentTime: [{ required: true, message: '请选择付款时间', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
};

// 获取状态名称
function getStatusName(status: string) {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    reviewing: '审核中',
    active: '已生效',
    expired: '已到期',
    terminated: '已终止',
  };
  return statusMap[status] || '-';
}

// 获取状态颜色
function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    draft: 'default',
    reviewing: 'processing',
    active: 'success',
    expired: 'warning',
    terminated: 'error',
  };
  return colorMap[status] || 'default';
}

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 格式化金额
function formatMoney(amount: number) {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 编辑合同
function handleEdit() {
  // TODO: 实现编辑功能
}

// 下载合同
function handleDownload() {
  // TODO: 实现下载功能
  message.info('正在下载合同文件...');
}

// 添加付款记录
function handleAddPayment() {
  paymentForm.amount = undefined;
  paymentForm.paymentTime = undefined;
  paymentForm.paymentMethod = undefined;
  paymentForm.remark = '';
  paymentModalVisible.value = true;
}

// 付款记录弹窗确认
async function handlePaymentModalOk() {
  try {
    await paymentFormRef.value.validate();
    paymentModalLoading.value = true;
    // TODO: 调用添加付款记录接口
    message.success('添加付款记录成功');
    paymentModalVisible.value = false;
    // 重新获取付款记录列表
  } catch (error) {
    // 表单校验失败
  } finally {
    paymentModalLoading.value = false;
  }
}

// 付款记录弹窗取消
function handlePaymentModalCancel() {
  paymentFormRef.value?.resetFields();
  paymentModalVisible.value = false;
}

// 获取合同详情
async function fetchContractDetail() {
  const id = route.params.id;
  // TODO: 调用接口获取合同详情
}

onMounted(() => {
  fetchContractDetail();
});
</script> 