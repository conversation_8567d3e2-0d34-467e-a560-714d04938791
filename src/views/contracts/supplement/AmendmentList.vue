<template>
  <div class="amendment-list-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">变更记录查询</h2>
          <div class="action-buttons">
            <a-button type="primary" @click="createAmendment">
              <template #icon><plus-outlined /></template>
              新增变更记录
            </a-button>
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="变更类型" name="type">
                <a-select v-model:value="searchForm.type" placeholder="请选择变更类型" allowClear>
                  <a-select-option v-for="item in amendmentTypes" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="变更日期" name="amendmentDate">
                <a-range-picker v-model:value="searchForm.amendmentDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="变更申请人" name="applicant">
                <a-input v-model:value="searchForm.applicant" placeholder="请输入申请人" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="审批状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择审批状态" allowClear>
                  <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 数据统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="变更总数" :value="statistics.total" :precision="0" />
              <template #extra>
                <file-sync-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="补充协议" :value="statistics.supplement" :precision="0" />
              <template #extra>
                <file-add-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="合同变更" :value="statistics.change" :precision="0" />
              <template #extra>
                <swap-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="本月变更" :value="statistics.thisMonth" :precision="0" />
              <template #extra>
                <calendar-outlined style="color: #eb2f96; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <div class="table-view">
        <a-table
          :columns="columns"
          :data-source="amendmentList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
        >
          <!-- 合同名称列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractName'">
              <a @click="viewContract(record)">{{ record.contractName }}</a>
            </template>
            
            <!-- 变更类型列 -->
            <template v-if="column.dataIndex === 'type'">
              <a-tag :color="record.type === 'supplement' ? 'green' : 'blue'">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            
            <!-- 审批状态列 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <!-- 操作列 -->
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewAmendment(record)">查看</a-button>
                <a-button v-if="record.status === 'approved'" type="link" size="small" @click="downloadAmendment(record)">下载</a-button>
                <a-dropdown v-if="['draft', 'rejected'].includes(record.status)">
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="editAmendment(record)">编辑</a-menu-item>
                      <a-menu-item @click="deleteAmendment(record)">删除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import {
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  FileSyncOutlined,
  FileAddOutlined,
  SwapOutlined,
  CalendarOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface Amendment {
  id: string;
  contractId: string;
  contractName: string;
  contractCode: string;
  type: string;
  title: string;
  description: string;
  applicant: string;
  amendmentDate: string;
  status: string;
  reason: string;
  fileUrl?: string;
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  type: undefined,
  amendmentDate: [],
  applicant: '',
  status: undefined,
});

// 统计数据
const statistics = reactive({
  total: 127,
  supplement: 78,
  change: 49,
  thisMonth: 14
});

// 变更类型选项
const amendmentTypes = [
  { label: '补充协议', value: 'supplement' },
  { label: '合同变更', value: 'change' },
];

// 审批状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '审批中', value: 'approving' },
  { label: '已批准', value: 'approved' },
  { label: '已驳回', value: 'rejected' },
];

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '变更标题',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true,
  },
  {
    title: '变更类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    key: 'applicant',
    width: 120,
  },
  {
    title: '变更日期',
    dataIndex: 'amendmentDate',
    key: 'amendmentDate',
    width: 150,
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 列表数据
const amendmentList = ref<Amendment[]>([]);

// 获取变更类型文本
const getTypeText = (type: string): string => {
  const found = amendmentTypes.find(item => item.value === type);
  return found ? found.label : type;
};

// 获取状态文本
const getStatusText = (status: string): string => {
  const found = statusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    draft: 'blue',
    approving: 'orange',
    approved: 'green',
    rejected: 'red',
  };
  return colorMap[status] || 'default';
};

// 获取变更记录列表
const fetchAmendmentList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    amendmentList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取变更记录列表失败');
    console.error('获取变更记录列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const types = amendmentTypes.map(t => t.value);
  const statuses = statusOptions.map(s => s.value);
  const total = 127;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * 180); // 半年内随机日期
    const amendmentDate = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    const type = types[Math.floor(Math.random() * types.length)];
    
    items.push({
      id: `A2023${String(i).padStart(4, '0')}`,
      contractId: `C2023${String(i).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(i).padStart(4, '0')}`,
      type,
      title: type === 'supplement' 
        ? `关于${['延长交付期限', '调整付款条件', '增加服务内容', '变更技术参数'][Math.floor(Math.random() * 4)]}的补充协议` 
        : `关于${['合同金额', '交付日期', '服务范围', '合同主体'][Math.floor(Math.random() * 4)]}的变更申请`,
      description: `因${['市场变化', '政策调整', '客户要求', '技术原因'][Math.floor(Math.random() * 4)]}，需要${['调整合同条款', '补充合同内容', '变更合同金额', '延长合同期限'][Math.floor(Math.random() * 4)]}`,
      applicant: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`,
      amendmentDate: formatDate(amendmentDate),
      status: statuses[Math.floor(Math.random() * statuses.length)],
      reason: `由于${['客户需求变更', '市场环境变化', '生产能力调整', '技术方案更新'][Math.floor(Math.random() * 4)]}，需要${['重新协商', '调整交付时间', '变更合同金额', '增加服务内容'][Math.floor(Math.random() * 4)]}`,
      fileUrl: Math.random() > 0.3 ? 'https://example.com/amendments/file.pdf' : undefined,
    });
  }

  return {
    items,
    total
  };
};

// 格式化日期
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchAmendmentList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchAmendmentList();
};

// 表格变更处理
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchAmendmentList();
};

// 查看合同
const viewContract = (record: Amendment) => {
  router.push(`/contracts/detail/${record.contractId}`);
};

// 查看变更记录
const viewAmendment = (record: Amendment) => {
  router.push(`/contracts/supplement/amendment-form?id=${record.id}&view=true`);
};

// 编辑变更记录
const editAmendment = (record: Amendment) => {
  router.push(`/contracts/supplement/amendment-form?id=${record.id}`);
};

// 新增变更记录
const createAmendment = () => {
  router.push('/contracts/supplement/amendment-form');
};

// 下载变更文件
const downloadAmendment = (record: Amendment) => {
  if (record.fileUrl) {
    // 实际项目中应该是window.open(record.fileUrl)或使用下载API
    message.success('开始下载变更文件');
  } else {
    message.warning('该变更记录没有附件');
  }
};

// 删除变更记录
const deleteAmendment = (record: Amendment) => {
  Modal.confirm({
    title: '确认删除',
    content: `确认要删除"${record.title}"吗？`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      // 模拟删除操作
      loading.value = true;
      setTimeout(() => {
        const index = amendmentList.value.findIndex(item => item.id === record.id);
        if (index !== -1) {
          amendmentList.value.splice(index, 1);
          pagination.total -= 1;
        }
        loading.value = false;
        message.success('删除成功');
      }, 500);
    }
  });
};

// 导出数据
const handleExport = () => {
  message.success('变更记录数据导出成功');
};

onMounted(() => {
  fetchAmendmentList();
});
</script>

<style scoped>
.amendment-list-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.table-view {
  margin-bottom: 16px;
}
</style> 