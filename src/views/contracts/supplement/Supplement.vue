<template>
  <div class="supplement-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">合同补充协议</h2>
          <div class="action-buttons">
            <a-button type="primary" @click="handleCreateSupplement">
              <template #icon><plus-outlined /></template>
              新增补充协议
            </a-button>
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="补充协议标题" name="supplementTitle">
                <a-input v-model:value="searchForm.supplementTitle" placeholder="请输入补充协议标题" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="协议状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择协议状态" allowClear>
                  <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="签订日期" name="signDate">
                <a-range-picker v-model:value="searchForm.signDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="客户名称" name="customerName">
                <a-input v-model:value="searchForm.customerName" placeholder="请输入客户名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 数据统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="补充协议总数" :value="statistics.total" :precision="0" />
              <template #extra>
                <file-text-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="本月新增" :value="statistics.monthlyNew" :precision="0" />
              <template #extra>
                <rise-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已生效" :value="statistics.active" :precision="0" />
              <template #extra>
                <check-circle-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="审批中" :value="statistics.reviewing" :precision="0" />
              <template #extra>
                <clock-circle-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <a-table
        :columns="columns"
        :data-source="supplementList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <!-- 自定义列 -->
        <template #bodyCell="{ column, record }">
          <!-- 补充协议标题列 -->
          <template v-if="column.dataIndex === 'title'">
            <a @click="viewSupplement(record)">{{ record.title }}</a>
          </template>
          
          <!-- 协议状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <!-- 签订日期列 -->
          <template v-if="column.dataIndex === 'signDate'">
            {{ formatDate(record.signDate) }}
          </template>
          
          <!-- 生效日期列 -->
          <template v-if="column.dataIndex === 'effectiveDate'">
            {{ formatDate(record.effectiveDate) }}
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewSupplement(record)">查看</a-button>
              <a-button v-if="record.status === 'draft'" type="link" size="small" @click="editSupplement(record)">编辑</a-button>
              <a-button v-if="record.status === 'draft'" type="link" size="small" @click="submitForApproval(record)">提交审批</a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="downloadSupplement(record)">下载文件</a-menu-item>
                    <a-menu-item v-if="record.status === 'draft'" @click="deleteSupplement(record)">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 删除确认对话框 -->
    <a-modal
      v-model:visible="deleteModalVisible"
      title="删除确认"
      okText="确认删除"
      cancelText="取消"
      @ok="confirmDelete"
    >
      <p>确定要删除此补充协议吗？删除后无法恢复。</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
  DownOutlined,
  FileTextOutlined,
  RiseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue';

// 接口定义
interface FormInstance {
  resetFields: () => void;
}

interface SupplementContract {
  id: string;
  contractId: string; // 关联的合同编号
  contractName: string; // 合同名称
  contractCode: string; // 合同编号
  title: string; // 补充协议标题
  content: string; // 补充协议内容
  status: string; // 协议状态 
  signDate: string; // 签订日期
  effectiveDate: string; // 生效日期
  customerName: string; // 客户名称
  createdBy: string; // 创建人
  createdTime: string; // 创建时间
  description: string; // 描述
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const deleteModalVisible = ref(false);
const selectedContract = ref<SupplementContract | null>(null);

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  supplementTitle: '',
  status: undefined,
  signDate: [],
  customerName: '',
});

// 统计数据
const statistics = reactive({
  total: 47,
  monthlyNew: 12,
  active: 35,
  reviewing: 8
});

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '审批中', value: 'reviewing' },
  { label: '已生效', value: 'active' },
  { label: '已终止', value: 'terminated' },
];

// 表格列定义
const columns = [
  {
    title: '补充协议标题',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    ellipsis: true,
  },
  {
    title: '协议状态',
    dataIndex: 'status',
    key: 'status',
    width: 120,
  },
  {
    title: '签订日期',
    dataIndex: 'signDate',
    key: 'signDate',
    width: 120,
    sorter: true
  },
  {
    title: '生效日期',
    dataIndex: 'effectiveDate',
    key: 'effectiveDate',
    width: 120,
    sorter: true
  },
  {
    title: '创建人',
    dataIndex: 'createdBy',
    key: 'createdBy',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 补充协议列表数据
const supplementList = ref<SupplementContract[]>([]);

// 获取状态文本
const getStatusText = (status: string): string => {
  const found = statusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    draft: 'blue',
    reviewing: 'orange',
    active: 'green',
    terminated: 'red'
  };
  return colorMap[status] || 'default';
};

// 格式化日期
const formatDate = (date: string | null): string => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

// 获取补充协议列表数据
const fetchSupplementList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    supplementList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取补充协议列表失败');
    console.error('获取补充协议列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const statuses = statusOptions.map(s => s.value);
  const total = 47;
  
  for (let i = 1; i <= 20; i++) {
    const now = new Date();
    const signDate = new Date(now.getFullYear(), now.getMonth() - Math.floor(Math.random() * 3), Math.floor(Math.random() * 28) + 1);
    const effectiveDate = new Date(signDate);
    effectiveDate.setDate(signDate.getDate() + Math.floor(Math.random() * 10) + 1);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    items.push({
      id: `S2023${String(i).padStart(4, '0')}`,
      contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      title: `关于${['价格调整', '交付时间变更', '服务范围扩展', '付款方式变更'][Math.floor(Math.random() * 4)]}的补充协议`,
      content: '补充协议内容...',
      status: status,
      signDate: formatDate(signDate.toISOString()),
      effectiveDate: formatDate(effectiveDate.toISOString()),
      customerName: `${['上海', '北京', '广州', '深圳'][Math.floor(Math.random() * 4)]}${['科技', '贸易', '实业', '集团'][Math.floor(Math.random() * 4)]}有限公司`,
      createdBy: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`,
      createdTime: new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
      description: '补充协议描述信息...'
    });
  }

  return {
    items,
    total
  };
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchSupplementList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchSupplementList();
};

// 表格变更
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchSupplementList();
};

// 查看补充协议
const viewSupplement = (record: SupplementContract) => {
  // 实际项目中应该跳转到补充协议详情页面
  message.info(`查看补充协议：${record.title}`);
};

// 编辑补充协议
const editSupplement = (record: SupplementContract) => {
  // 实际项目中应该跳转到补充协议编辑页面
  message.info(`编辑补充协议：${record.title}`);
};

// 新增补充协议
const handleCreateSupplement = () => {
  // 实际项目中应该跳转到新增补充协议页面
  message.info('新增补充协议');
};

// 提交审批
const submitForApproval = (record: SupplementContract) => {
  Modal.confirm({
    title: '提交确认',
    content: `确定提交补充协议"${record.title}"进行审批吗？`,
    onOk: () => {
      message.success('提交审批成功');
      // 更新状态
      const index = supplementList.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        supplementList.value[index].status = 'reviewing';
      }
    }
  });
};

// 导出数据
const handleExport = () => {
  message.success('补充协议数据导出成功');
};

// 下载补充协议
const downloadSupplement = (record: SupplementContract) => {
  message.success(`补充协议"${record.title}"下载成功`);
};

// 删除补充协议
const deleteSupplement = (record: SupplementContract) => {
  selectedContract.value = record;
  deleteModalVisible.value = true;
};

// 确认删除
const confirmDelete = () => {
  if (selectedContract.value) {
    // 模拟删除操作
    supplementList.value = supplementList.value.filter(
      item => item.id !== selectedContract.value?.id
    );
    message.success('删除成功');
    deleteModalVisible.value = false;
  }
};

onMounted(() => {
  fetchSupplementList();
});
</script>

<style scoped>
.supplement-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}
</style> 