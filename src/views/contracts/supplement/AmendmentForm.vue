<template>
  <div class="amendment-form-page">
    <a-card :bordered="false">
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">
            {{ isViewMode ? '变更记录详情' : (editMode ? '编辑变更记录' : '新增变更记录') }}
          </h2>
          <div class="action-buttons" v-if="isViewMode">
            <a-button @click="handleEdit">
              <template #icon><edit-outlined /></template>
              编辑
            </a-button>
            <a-button @click="goBack">
              <template #icon><rollback-outlined /></template>
              返回
            </a-button>
          </div>
        </div>
      </div>

      <a-form
        ref="formRef"
        :model="formState"
        :rules="isViewMode ? {} : rules"
        layout="vertical"
        :disabled="isViewMode"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="合同选择" name="contractId" required>
              <a-select
                v-model:value="formState.contractId"
                placeholder="请选择关联合同"
                :options="contractOptions"
                show-search
                :filter-option="filterOption"
                @change="handleContractChange"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="变更类型" name="type" required>
              <a-select
                v-model:value="formState.type"
                placeholder="请选择变更类型"
                :options="amendmentTypeOptions"
              ></a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="变更标题" name="title" required>
          <a-input 
            v-model:value="formState.title" 
            placeholder="请输入变更标题，例如：延长项目交付期限"
            :maxLength="100"
            show-count
          />
        </a-form-item>

        <a-form-item label="变更说明" name="description" required>
          <a-textarea 
            v-model:value="formState.description" 
            placeholder="请输入变更详细说明，包括变更原因、影响等"
            :rows="4"
            :maxLength="500"
            show-count
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="原值" name="oldValue">
              <a-textarea 
                v-model:value="formState.oldValue" 
                placeholder="请输入变更前的原始值"
                :rows="3"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="新值" name="newValue" required>
              <a-textarea 
                v-model:value="formState.newValue" 
                placeholder="请输入变更后的新值"
                :rows="3"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="申请人" name="applicant" required>
              <a-input 
                v-model:value="formState.applicant"
                placeholder="请输入申请人姓名"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="变更日期" name="amendmentDate" required>
              <a-date-picker 
                v-model:value="formState.amendmentDate"
                style="width: 100%"
                :disabledDate="disabledDate"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="上传附件" name="attachments">
          <a-upload
            v-model:file-list="fileList"
            :action="uploadUrl"
            :headers="headers"
            :multiple="true"
            :before-upload="beforeUpload"
            v-if="!isViewMode"
          >
            <a-button v-if="!isViewMode">
              <template #icon><upload-outlined /></template>
              上传文件
            </a-button>
          </a-upload>
          <div v-else>
            <a-tag v-for="file in fileList" :key="file.uid" style="margin-bottom: 8px">
              <paper-clip-outlined /> {{ file.name }}
              <a @click.stop="downloadFile(file)" style="margin-left: 8px">
                <download-outlined />
              </a>
            </a-tag>
            <div v-if="fileList.length === 0">无附件</div>
          </div>
        </a-form-item>

        <div class="form-actions" v-if="!isViewMode">
          <a-button type="primary" @click="handleSubmit" :loading="submitting">
            提交
          </a-button>
          <a-button style="margin-left: 8px" @click="handleSaveDraft" :loading="saving">
            保存草稿
          </a-button>
          <a-button style="margin-left: 8px" @click="resetForm">
            重置
          </a-button>
          <a-button style="margin-left: 8px" @click="goBack">
            取消
          </a-button>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, FormInstance } from 'ant-design-vue';
import { 
  EditOutlined, 
  RollbackOutlined, 
  UploadOutlined, 
  PaperClipOutlined, 
  DownloadOutlined 
} from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

// 表单实例
const formRef = ref<FormInstance>();
const router = useRouter();
const route = useRoute();

// 判断是否是查看模式
const isViewMode = computed(() => {
  return route.query.view === 'true';
});

// 判断是否是编辑模式
const editMode = computed(() => {
  return route.query.id && !isViewMode.value;
});

// 表单状态
const formState = reactive({
  id: '',
  contractId: undefined,
  contractName: '',
  contractCode: '',
  type: undefined,
  title: '',
  description: '',
  oldValue: '',
  newValue: '',
  applicant: '',
  amendmentDate: dayjs(),
  status: 'draft', // 草稿、已提交、已审批、已拒绝
  attachments: [],
});

// 上传相关状态
const fileList = ref<any[]>([]);
const uploadUrl = '/api/upload';
const headers = {
  authorization: 'authorization-text',
};

// 表单验证规则
const rules = {
  contractId: [{ required: true, message: '请选择关联合同', trigger: 'change' }],
  type: [{ required: true, message: '请选择变更类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入变更标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入变更说明', trigger: 'blur' }],
  newValue: [{ required: true, message: '请输入变更后的新值', trigger: 'blur' }],
  applicant: [{ required: true, message: '请输入申请人姓名', trigger: 'blur' }],
  amendmentDate: [{ required: true, message: '请选择变更日期', trigger: 'change' }],
};

// 合同选项
const contractOptions = ref([
  { value: '*********', label: '销售合同-001号' },
  { value: 'C20230002', label: '采购合同-002号' },
  { value: 'C20230003', label: '服务合同-003号' },
  { value: 'C20230004', label: '合作合同-004号' },
  { value: 'C20230005', label: '租赁合同-005号' },
]);

// 变更类型选项
const amendmentTypeOptions = [
  { value: 'clause', label: '条款变更' },
  { value: 'price', label: '价格变更' },
  { value: 'quantity', label: '数量变更' },
  { value: 'period', label: '周期变更' },
  { value: 'party', label: '主体变更' },
  { value: 'other', label: '其他变更' },
];

// 加载状态
const submitting = ref(false);
const saving = ref(false);
const loading = ref(false);

// 处理合同选择变化
const handleContractChange = (value: string) => {
  const selected = contractOptions.value.find(item => item.value === value);
  if (selected) {
    formState.contractName = selected.label;
    formState.contractCode = selected.value;
  }
};

// 过滤选项
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 禁用日期
const disabledDate = (current: Dayjs) => {
  // 不能选择未来日期
  return current && current > dayjs().endOf('day');
};

// 上传前检查
const beforeUpload = (file: File) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
  }
  return isLt10M || false;
};

// 下载文件
const downloadFile = (file: any) => {
  message.success(`正在下载文件: ${file.name}`);
  // 实际项目中应该调用下载API
};

// 提交表单
const handleSubmit = async () => {
  try {
    if (formRef.value) {
      await formRef.value.validate();
      submitting.value = true;
      
      const formData = {
        ...formState,
        amendmentDate: formState.amendmentDate.format('YYYY-MM-DD'),
        status: 'submitted',
        attachments: fileList.value.map(file => file.name || file),
      };
      
      // 这里应该调用API保存数据
      console.log('表单提交数据:', formData);
      
      // 模拟API请求
      setTimeout(() => {
        message.success('提交成功');
        submitting.value = false;
        router.push('/contracts/supplement/amendment-list');
      }, 1000);
    }
  } catch (e) {
    console.error('验证失败:', e);
  }
};

// 保存草稿
const handleSaveDraft = async () => {
  saving.value = true;
  
  const formData = {
    ...formState,
    amendmentDate: formState.amendmentDate.format('YYYY-MM-DD'),
    status: 'draft',
    attachments: fileList.value.map(file => file.name || file),
  };
  
  // 这里应该调用API保存数据
  console.log('保存草稿数据:', formData);
  
  // 模拟API请求
  setTimeout(() => {
    message.success('草稿保存成功');
    saving.value = false;
  }, 1000);
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  fileList.value = [];
};

// 返回列表
const goBack = () => {
  router.push('/contracts/supplement/amendment-list');
};

// 编辑
const handleEdit = () => {
  const currentId = route.query.id;
  router.push(`/contracts/supplement/amendment-form?id=${currentId}`);
};

// 获取详情数据
const fetchDetail = async (id: string) => {
  loading.value = true;
  try {
    // 这里应该调用API获取详情
    console.log(`获取ID为${id}的变更记录详情`);
    
    // 模拟API请求
    setTimeout(() => {
      // 模拟详情数据
      const detail = {
        id: id,
        contractId: '*********',
        contractName: '销售合同-001号',
        contractCode: '*********',
        type: 'period',
        title: '延长项目交付期限',
        description: '由于甲方提供的资料延迟，影响了项目进度，经双方协商，将项目交付期限延长30天。',
        oldValue: '交付日期：2023年10月15日',
        newValue: '交付日期：2023年11月15日',
        applicant: '张三',
        amendmentDate: dayjs('2023-09-20'),
        status: 'submitted',
        attachments: ['变更申请书.docx', '双方确认邮件.pdf'],
      };
      
      // 更新表单数据
      Object.assign(formState, detail);
      
      // 更新附件列表
      fileList.value = detail.attachments.map((name, index) => ({
        uid: `-${index + 1}`,
        name: name,
        status: 'done',
        url: `#/file/${name}`,
      }));
      
      loading.value = false;
    }, 1000);
  } catch (error) {
    message.error('获取详情失败');
    console.error('获取详情失败:', error);
    loading.value = false;
  }
};

onMounted(() => {
  const id = route.query.id as string;
  if (id) {
    fetchDetail(id);
  }
});
</script>

<style scoped>
.amendment-form-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 24px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.form-actions {
  margin-top: 24px;
  text-align: center;
}
</style> 