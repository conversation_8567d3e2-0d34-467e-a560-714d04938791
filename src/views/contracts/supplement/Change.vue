<template>
  <div class="contract-change-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">合同变更</h2>
          <div class="action-buttons">
            <a-button type="primary" @click="handleCreateChange">
              <template #icon><plus-outlined /></template>
              申请变更
            </a-button>
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="变更类型" name="changeType">
                <a-select v-model:value="searchForm.changeType" placeholder="请选择变更类型" allowClear>
                  <a-select-option v-for="item in changeTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="变更状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择变更状态" allowClear>
                  <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="申请日期" name="applyDate">
                <a-range-picker v-model:value="searchForm.applyDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="申请人" name="applicant">
                <a-input v-model:value="searchForm.applicant" placeholder="请输入申请人" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 数据统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="变更申请总数" :value="statistics.total" :precision="0" />
              <template #extra>
                <file-sync-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="待审批" :value="statistics.pending" :precision="0" />
              <template #extra>
                <hourglass-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已批准" :value="statistics.approved" :precision="0" />
              <template #extra>
                <check-circle-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已驳回" :value="statistics.rejected" :precision="0" />
              <template #extra>
                <close-circle-outlined style="color: #f5222d; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <a-table
        :columns="columns"
        :data-source="changeList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <!-- 自定义列 -->
        <template #bodyCell="{ column, record }">
          <!-- 合同名称列 -->
          <template v-if="column.dataIndex === 'contractName'">
            <a @click="viewContract(record)">{{ record.contractName }}</a>
          </template>
          
          <!-- 变更类型列 -->
          <template v-if="column.dataIndex === 'changeType'">
            <a-tag>{{ getChangeTypeText(record.changeType) }}</a-tag>
          </template>
          
          <!-- 变更状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <!-- 申请日期列 -->
          <template v-if="column.dataIndex === 'applyDate'">
            {{ formatDate(record.applyDate) }}
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewChangeDetail(record)">详情</a-button>
              <a-button v-if="record.status === 'draft'" type="link" size="small" @click="editChange(record)">编辑</a-button>
              <a-button v-if="record.status === 'draft'" type="link" size="small" @click="submitChange(record)">提交审批</a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="downloadAttachments(record)">附件下载</a-menu-item>
                    <a-menu-item v-if="record.status === 'draft'" @click="deleteChange(record)">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 删除确认对话框 -->
    <a-modal
      v-model:visible="deleteModalVisible"
      title="删除确认"
      okText="确认删除"
      cancelText="取消"
      @ok="confirmDelete"
    >
      <p>确定要删除此变更申请吗？删除后无法恢复。</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
  DownOutlined,
  FileSyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  HourglassOutlined
} from '@ant-design/icons-vue';

// 接口定义
interface FormInstance {
  resetFields: () => void;
}

interface ContractChange {
  id: string;
  contractId: string; // 关联的合同编号
  contractName: string; // 合同名称
  contractCode: string; // 合同编号
  changeType: string; // 变更类型
  title: string; // 变更标题
  description: string; // 变更描述
  reason: string; // 变更原因
  status: string; // 变更状态
  applyDate: string; // 申请日期
  applicant: string; // 申请人
  approver: string | null; // 审批人
  approveDate: string | null; // 审批日期
  approveComment: string | null; // 审批意见
  attachments: { id: string; name: string; url: string }[]; // 附件
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const deleteModalVisible = ref(false);
const selectedChange = ref<ContractChange | null>(null);

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  changeType: undefined,
  status: undefined,
  applyDate: [],
  applicant: '',
});

// 统计数据
const statistics = reactive({
  total: 86,
  pending: 12,
  approved: 56,
  rejected: 18
});

// 变更类型选项
const changeTypeOptions = [
  { label: '合同主体变更', value: 'subject_change' },
  { label: '合同金额变更', value: 'amount_change' },
  { label: '合同期限变更', value: 'period_change' },
  { label: '合同条款变更', value: 'clause_change' },
  { label: '履约方式变更', value: 'performance_change' },
  { label: '其他变更', value: 'other_change' },
];

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '审批中', value: 'pending' },
  { label: '已批准', value: 'approved' },
  { label: '已驳回', value: 'rejected' },
];

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '变更类型',
    dataIndex: 'changeType',
    key: 'changeType',
    width: 120,
  },
  {
    title: '变更标题',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true,
  },
  {
    title: '变更状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '申请日期',
    dataIndex: 'applyDate',
    key: 'applyDate',
    width: 120,
    sorter: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    key: 'applicant',
    width: 100,
  },
  {
    title: '审批人',
    dataIndex: 'approver',
    key: 'approver',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 220,
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 变更列表数据
const changeList = ref<ContractChange[]>([]);

// 获取变更类型文本
const getChangeTypeText = (type: string): string => {
  const found = changeTypeOptions.find(item => item.value === type);
  return found ? found.label : type;
};

// 获取状态文本
const getStatusText = (status: string): string => {
  const found = statusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    draft: 'blue',
    pending: 'orange',
    approved: 'green',
    rejected: 'red'
  };
  return colorMap[status] || 'default';
};

// 格式化日期
const formatDate = (date: string | null): string => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

// 获取变更列表数据
const fetchChangeList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    changeList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取变更列表失败');
    console.error('获取变更列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const statuses = statusOptions.map(s => s.value);
  const changeTypes = changeTypeOptions.map(t => t.value);
  const total = 86;
  
  for (let i = 1; i <= 20; i++) {
    const now = new Date();
    const applyDate = new Date(now.getFullYear(), now.getMonth() - Math.floor(Math.random() * 6), Math.floor(Math.random() * 28) + 1);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const changeType = changeTypes[Math.floor(Math.random() * changeTypes.length)];
    
    // 审批日期和审批人只有在已批准或已驳回状态时才有
    let approveDate = null;
    let approver = null;
    let approveComment = null;
    
    if (status === 'approved' || status === 'rejected') {
      const approveDateObj = new Date(applyDate);
      approveDateObj.setDate(applyDate.getDate() + Math.floor(Math.random() * 5) + 1);
      approveDate = formatDate(approveDateObj.toISOString());
      approver = `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`;
      approveComment = status === 'approved' ? '同意变更申请' : '变更内容不符合公司规定，请修改后重新提交';
    }
    
    items.push({
      id: `CH2023${String(i).padStart(4, '0')}`,
      contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      changeType: changeType,
      title: `关于${getChangeTypeText(changeType)}的申请`,
      description: `合同${getChangeTypeText(changeType)}变更详细说明...`,
      reason: `由于项目需求变化，需要对合同进行${getChangeTypeText(changeType)}`,
      status: status,
      applyDate: formatDate(applyDate.toISOString()),
      applicant: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['小', '大', '老', '二', '三'][Math.floor(Math.random() * 5)]}`,
      approver: approver,
      approveDate: approveDate,
      approveComment: approveComment,
      attachments: [
        { id: `A${i}001`, name: '变更说明文档.docx', url: '#' },
        { id: `A${i}002`, name: '变更对照表.xlsx', url: '#' }
      ]
    });
  }

  return {
    items,
    total
  };
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchChangeList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchChangeList();
};

// 表格变更
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchChangeList();
};

// 查看合同详情
const viewContract = (record: ContractChange) => {
  router.push(`/contracts/detail/${record.contractId}`);
};

// 查看变更详情
const viewChangeDetail = (record: ContractChange) => {
  // 实际项目中应该跳转到变更详情页面
  message.info(`查看变更详情：${record.title}`);
};

// 编辑变更申请
const editChange = (record: ContractChange) => {
  // 实际项目中应该跳转到变更编辑页面
  message.info(`编辑变更申请：${record.title}`);
};

// 新增变更申请
const handleCreateChange = () => {
  // 实际项目中应该跳转到新增变更页面
  message.info('新增变更申请');
};

// 提交变更申请
const submitChange = (record: ContractChange) => {
  Modal.confirm({
    title: '提交确认',
    content: `确定提交变更申请"${record.title}"进行审批吗？`,
    onOk: () => {
      message.success('提交审批成功');
      // 更新状态
      const index = changeList.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        changeList.value[index].status = 'pending';
      }
    }
  });
};

// 导出数据
const handleExport = () => {
  message.success('变更申请数据导出成功');
};

// 下载附件
const downloadAttachments = (record: ContractChange) => {
  message.success(`变更申请"${record.title}"的附件下载成功`);
};

// 删除变更申请
const deleteChange = (record: ContractChange) => {
  selectedChange.value = record;
  deleteModalVisible.value = true;
};

// 确认删除
const confirmDelete = () => {
  if (selectedChange.value) {
    // 模拟删除操作
    changeList.value = changeList.value.filter(
      item => item.id !== selectedChange.value?.id
    );
    message.success('删除成功');
    deleteModalVisible.value = false;
  }
};

onMounted(() => {
  fetchChangeList();
});
</script>

<style scoped>
.contract-change-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}
</style> 