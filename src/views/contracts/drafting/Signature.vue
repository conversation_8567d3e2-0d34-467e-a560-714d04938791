<template>
  <div class="signature-page">
    <a-page-header
      title="合同签署"
      :breadcrumb="{ routes }"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="saveSignature">保存</a-button>
          <a-button type="primary" @click="completeSignature">完成签署</a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-container">
      <a-row :gutter="16">
        <!-- 左侧合同预览 -->
        <a-col :span="16">
          <a-card title="合同预览与签署" style="margin-bottom: 16px">
            <div class="contract-preview">
              <div v-if="!signatureMode" class="preview-container">
                <iframe :src="previewUrl" width="100%" height="700" frameborder="0"></iframe>
              </div>
              <div v-else class="signature-container">
                <div class="contract-pages">
                  <div v-for="(page, index) in contractPages" :key="index" class="contract-page">
                    <img :src="page.imageUrl" width="100%" />
                    <div v-for="(area, areaIndex) in page.signatureAreas" :key="areaIndex"
                      class="signature-area"
                      :class="{ active: selectedArea && selectedArea.id === area.id }"
                      :style="{
                        left: area.position.x + '%',
                        top: area.position.y + '%',
                        width: area.size.width + '%',
                        height: area.size.height + '%'
                      }"
                      @click="selectSignatureArea(area)">
                      <div v-if="area.signed" class="signed-content">
                        <img :src="area.signatureUrl" width="100%" />
                      </div>
                      <div v-else class="unsigned-content">
                        <div class="placeholder">{{ area.type === 'signature' ? '点击签名' : '点击盖章' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="signature-actions">
              <a-space>
                <a-switch v-model:checked="signatureMode" checked-children="签署模式" un-checked-children="预览模式" />
                <a-button v-if="signatureMode" type="primary" @click="addSignatureArea">添加签名区域</a-button>
                <a-button v-if="signatureMode" @click="addSealArea">添加盖章区域</a-button>
              </a-space>
            </div>
          </a-card>
        </a-col>
        
        <!-- 右侧签署信息 -->
        <a-col :span="8">
          <a-card title="签署信息" style="margin-bottom: 16px">
            <a-descriptions bordered :column="1">
              <a-descriptions-item label="合同名称">{{ contractData.name }}</a-descriptions-item>
              <a-descriptions-item label="合同编号">{{ contractData.code }}</a-descriptions-item>
              <a-descriptions-item label="签署状态">
                <a-tag :color="getStatusColor(contractData.status)">{{ getStatusText(contractData.status) }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="双方签署">
                <div class="sign-parties">
                  <div class="party">
                    <div class="party-name">我方：{{ contractData.ourCompany }}</div>
                    <div class="party-status">
                      <a-tag :color="contractData.ourSigned ? 'green' : 'orange'">
                        {{ contractData.ourSigned ? '已签署' : '待签署' }}
                      </a-tag>
                    </div>
                  </div>
                  <div class="party">
                    <div class="party-name">对方：{{ contractData.theirCompany }}</div>
                    <div class="party-status">
                      <a-tag :color="contractData.theirSigned ? 'green' : 'orange'">
                        {{ contractData.theirSigned ? '已签署' : '待签署' }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <a-card title="签名管理" style="margin-bottom: 16px">
            <div class="signature-list">
              <div class="signature-item" v-for="(item, index) in signatures" :key="index">
                <div class="signature-preview">
                  <img :src="item.url" :alt="item.name" />
                </div>
                <div class="signature-info">
                  <div class="signature-name">{{ item.name }}</div>
                  <div class="signature-actions">
                    <a-button type="link" size="small" @click="useSignature(item)">使用</a-button>
                    <a-button type="link" size="small" danger @click="deleteSignature(item)">删除</a-button>
                  </div>
                </div>
              </div>

              <div class="add-signature">
                <a-button type="dashed" block @click="showSignatureModal">
                  <plus-outlined /> 添加签名
                </a-button>
              </div>
            </div>
          </a-card>

          <a-card title="印章管理">
            <div class="seal-list">
              <div class="seal-item" v-for="(item, index) in seals" :key="index">
                <div class="seal-preview">
                  <img :src="item.url" :alt="item.name" />
                </div>
                <div class="seal-info">
                  <div class="seal-name">{{ item.name }}</div>
                  <div class="seal-actions">
                    <a-button type="link" size="small" @click="useSeal(item)">使用</a-button>
                    <a-button type="link" size="small" danger @click="deleteSeal(item)">删除</a-button>
                  </div>
                </div>
              </div>

              <div class="add-seal">
                <a-button type="dashed" block @click="showSealModal">
                  <plus-outlined /> 添加印章
                </a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 签名创建弹窗 -->
    <a-modal
      v-model:visible="signatureModalVisible"
      title="创建签名"
      width="600px"
      @ok="createSignature"
    >
      <a-tabs v-model:activeKey="signatureTabKey">
        <a-tab-pane key="draw" tab="手写签名">
          <div class="signature-pad-container">
            <div class="signature-pad" ref="signaturePad"></div>
            <div class="signature-pad-actions">
              <a-button @click="clearSignature">清除</a-button>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="upload" tab="上传签名">
          <a-upload-dragger
            v-model:fileList="signatureFileList"
            name="file"
            :multiple="false"
            action="/api/upload"
            @change="handleSignatureUpload"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽签名图片到此区域上传</p>
            <p class="ant-upload-hint">
              支持JPG、PNG格式的签名图片
            </p>
          </a-upload-dragger>
        </a-tab-pane>
        <a-tab-pane key="type" tab="输入签名">
          <div class="input-signature">
            <a-form layout="vertical">
              <a-form-item label="签名文字">
                <a-input v-model:value="typedSignature" placeholder="请输入您的签名文字" />
              </a-form-item>
              <a-form-item label="字体选择">
                <a-select v-model:value="signatureFont" style="width: 100%">
                  <a-select-option value="kaiti">楷体</a-select-option>
                  <a-select-option value="songti">宋体</a-select-option>
                  <a-select-option value="heiti">黑体</a-select-option>
                  <a-select-option value="lishu">隶书</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
            <div class="signature-preview">
              <div class="typed-signature" :style="{ fontFamily: signatureFont }">
                {{ typedSignature || '签名预览' }}
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
      <a-form layout="vertical" style="margin-top: 16px">
        <a-form-item label="签名名称">
          <a-input v-model:value="signatureName" placeholder="请输入签名名称，方便后续使用" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 印章创建弹窗 -->
    <a-modal
      v-model:visible="sealModalVisible"
      title="添加印章"
      width="600px"
      @ok="createSeal"
    >
      <a-tabs v-model:activeKey="sealTabKey">
        <a-tab-pane key="upload" tab="上传印章">
          <a-upload-dragger
            v-model:fileList="sealFileList"
            name="file"
            :multiple="false"
            action="/api/upload"
            @change="handleSealUpload"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽印章图片到此区域上传</p>
            <p class="ant-upload-hint">
              支持JPG、PNG格式的印章图片，建议使用透明背景
            </p>
          </a-upload-dragger>
        </a-tab-pane>
        <a-tab-pane key="create" tab="创建印章">
          <div class="create-seal">
            <a-form layout="vertical">
              <a-form-item label="印章类型">
                <a-radio-group v-model:value="sealType">
                  <a-radio value="company">公司印章</a-radio>
                  <a-radio value="personal">个人印章</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="印章文字">
                <a-input v-model:value="sealText" placeholder="请输入印章文字" />
              </a-form-item>
              <a-form-item label="印章颜色">
                <a-radio-group v-model:value="sealColor">
                  <a-radio value="red">红色</a-radio>
                  <a-radio value="blue">蓝色</a-radio>
                  <a-radio value="black">黑色</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
            <div class="seal-preview-container">
              <div class="seal-preview-wrapper" :style="{ backgroundColor: sealColor === 'red' ? '#f5222d' : (sealColor === 'blue' ? '#1890ff' : '#000000') }">
                <div class="seal-preview-text">{{ sealText || '印章预览' }}</div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
      <a-form layout="vertical" style="margin-top: 16px">
        <a-form-item label="印章名称">
          <a-input v-model:value="sealName" placeholder="请输入印章名称，方便后续使用" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { PlusOutlined, InboxOutlined } from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();

// 面包屑路由
const routes = [
  {
    path: '/contracts',
    breadcrumbName: '合同管理',
  },
  {
    path: '/contracts/list',
    breadcrumbName: '合同查询',
  },
  {
    path: '',
    breadcrumbName: '合同签署',
  },
];

// 合同数据
const contractData = reactive({
  id: 'CT-2024-0045',
  name: '销售合同-A公司-软件系统采购',
  code: 'HT-2024-0045',
  status: 'signing',
  ourCompany: '我方公司名称',
  theirCompany: '北京科技有限公司',
  ourSigned: false,
  theirSigned: false,
});

// 预览URL
const previewUrl = ref('https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf');

// 签名模式切换
const signatureMode = ref(false);

// 合同页面数据
const contractPages = ref([
  {
    id: 1,
    imageUrl: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    signatureAreas: [
      {
        id: 'sign-1',
        type: 'signature',
        position: { x: 70, y: 70 },
        size: { width: 20, height: 10 },
        role: 'our',
        signed: false,
        signatureUrl: '',
      },
      {
        id: 'sign-2',
        type: 'seal',
        position: { x: 70, y: 85 },
        size: { width: 15, height: 15 },
        role: 'our',
        signed: false,
        signatureUrl: '',
      }
    ]
  },
  {
    id: 2,
    imageUrl: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    signatureAreas: [
      {
        id: 'sign-3',
        type: 'signature',
        position: { x: 20, y: 70 },
        size: { width: 20, height: 10 },
        role: 'their',
        signed: false,
        signatureUrl: '',
      },
      {
        id: 'sign-4',
        type: 'seal',
        position: { x: 20, y: 85 },
        size: { width: 15, height: 15 },
        role: 'their',
        signed: false,
        signatureUrl: '',
      }
    ]
  }
]);

// 选中的签名区域
const selectedArea = ref(null);

// 已保存的签名
const signatures = ref([
  {
    id: 1,
    name: '个人签名1',
    url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  {
    id: 2,
    name: '个人签名2',
    url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
]);

// 已保存的印章
const seals = ref([
  {
    id: 1,
    name: '公司印章',
    url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
  {
    id: 2,
    name: '合同专用章',
    url: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
  },
]);

// 签名创建相关
const signatureModalVisible = ref(false);
const signatureTabKey = ref('draw');
const signatureName = ref('');
const signaturePad = ref(null);
const signatureFileList = ref([]);

// 输入签名相关
const typedSignature = ref('');
const signatureFont = ref('kaiti');

// 印章创建相关
const sealModalVisible = ref(false);
const sealTabKey = ref('upload');
const sealName = ref('');
const sealFileList = ref([]);
const sealType = ref('company');
const sealText = ref('');
const sealColor = ref('red');

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    approving: '审批中',
    approved: '已批准',
    rejected: '已驳回',
    signing: '签署中',
    signed: '已签署',
    completed: '已完成',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'blue',
    approving: 'orange',
    approved: 'green',
    rejected: 'red',
    signing: 'blue',
    signed: 'green',
    completed: 'green',
  };
  return colorMap[status] || 'default';
};

// 选择签名区域
const selectSignatureArea = (area: any) => {
  selectedArea.value = area;
  
  if (area.signed) {
    message.info('该区域已签署，如需修改，请先清除签名');
  }
};

// 添加签名区域
const addSignatureArea = () => {
  // 实际项目中应该允许用户拖拽或点击位置来添加
  message.info('添加签名区域功能');
};

// 添加盖章区域
const addSealArea = () => {
  // 实际项目中应该允许用户拖拽或点击位置来添加
  message.info('添加盖章区域功能');
};

// 显示签名创建弹窗
const showSignatureModal = () => {
  signatureModalVisible.value = true;
  signatureName.value = '';
  signatureTabKey.value = 'draw';
  typedSignature.value = '';
  
  // 初始化签名板
  nextTick(() => {
    if (signaturePad.value) {
      // 实际项目中应该初始化签名板插件
    }
  });
};

// 清除签名
const clearSignature = () => {
  // 实际项目中应该调用签名板插件的清除方法
  message.info('清除签名');
};

// 创建签名
const createSignature = () => {
  if (!signatureName.value) {
    message.error('请输入签名名称');
    return;
  }
  
  // 根据不同的签名方式创建签名
  let signatureUrl = '';
  
  if (signatureTabKey.value === 'draw') {
    // 实际项目中应该获取签名板的数据
    signatureUrl = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';
  } else if (signatureTabKey.value === 'upload') {
    if (signatureFileList.value.length === 0) {
      message.error('请上传签名图片');
      return;
    }
    signatureUrl = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';
  } else if (signatureTabKey.value === 'type') {
    if (!typedSignature.value) {
      message.error('请输入签名文字');
      return;
    }
    signatureUrl = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';
  }
  
  // 保存签名
  signatures.value.push({
    id: signatures.value.length + 1,
    name: signatureName.value,
    url: signatureUrl,
  });
  
  message.success('签名创建成功');
  signatureModalVisible.value = false;
};

// 处理签名上传
const handleSignatureUpload = (info: any) => {
  const { status } = info.file;
  if (status === 'done') {
    message.success(`${info.file.name} 上传成功`);
  } else if (status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
};

// 使用签名
const useSignature = (signature: any) => {
  if (!selectedArea.value) {
    message.warning('请先选择要签名的区域');
    return;
  }
  
  if (selectedArea.value.type !== 'signature') {
    message.warning('所选区域不是签名区域');
    return;
  }
  
  // 应用签名
  selectedArea.value.signed = true;
  selectedArea.value.signatureUrl = signature.url;
  
  message.success('签名应用成功');
  
  // 检查我方是否完成签署
  updateSignStatus();
};

// 删除签名
const deleteSignature = (signature: any) => {
  signatures.value = signatures.value.filter(item => item.id !== signature.id);
  message.success('签名已删除');
};

// 显示印章创建弹窗
const showSealModal = () => {
  sealModalVisible.value = true;
  sealName.value = '';
  sealTabKey.value = 'upload';
  sealType.value = 'company';
  sealText.value = '';
  sealColor.value = 'red';
};

// 创建印章
const createSeal = () => {
  if (!sealName.value) {
    message.error('请输入印章名称');
    return;
  }
  
  // 根据不同的创建方式创建印章
  let sealUrl = '';
  
  if (sealTabKey.value === 'upload') {
    if (sealFileList.value.length === 0) {
      message.error('请上传印章图片');
      return;
    }
    sealUrl = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';
  } else if (sealTabKey.value === 'create') {
    if (!sealText.value) {
      message.error('请输入印章文字');
      return;
    }
    sealUrl = 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png';
  }
  
  // 保存印章
  seals.value.push({
    id: seals.value.length + 1,
    name: sealName.value,
    url: sealUrl,
  });
  
  message.success('印章创建成功');
  sealModalVisible.value = false;
};

// 处理印章上传
const handleSealUpload = (info: any) => {
  const { status } = info.file;
  if (status === 'done') {
    message.success(`${info.file.name} 上传成功`);
  } else if (status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
};

// 使用印章
const useSeal = (seal: any) => {
  if (!selectedArea.value) {
    message.warning('请先选择要盖章的区域');
    return;
  }
  
  if (selectedArea.value.type !== 'seal') {
    message.warning('所选区域不是盖章区域');
    return;
  }
  
  // 应用印章
  selectedArea.value.signed = true;
  selectedArea.value.signatureUrl = seal.url;
  
  message.success('印章应用成功');
  
  // 检查我方是否完成签署
  updateSignStatus();
};

// 删除印章
const deleteSeal = (seal: any) => {
  seals.value = seals.value.filter(item => item.id !== seal.id);
  message.success('印章已删除');
};

// 更新签署状态
const updateSignStatus = () => {
  // 检查我方是否完成所有签名和盖章
  const ourAreas = contractPages.value.flatMap(page => 
    page.signatureAreas.filter(area => area.role === 'our')
  );
  
  const allSigned = ourAreas.every(area => area.signed);
  if (allSigned) {
    contractData.ourSigned = true;
  }
};

// 保存签署
const saveSignature = () => {
  message.success('签署已保存');
};

// 完成签署
const completeSignature = () => {
  // 检查所有我方区域是否已签署
  const ourAreas = contractPages.value.flatMap(page => 
    page.signatureAreas.filter(area => area.role === 'our')
  );
  
  const unsignedAreas = ourAreas.filter(area => !area.signed);
  if (unsignedAreas.length > 0) {
    message.error('请完成所有签名和盖章区域');
    return;
  }
  
  // 更新合同状态
  message.loading('正在处理...');
  setTimeout(() => {
    // 对方已签署的情况
    if (contractData.theirSigned) {
      contractData.status = 'signed';
      message.success('合同签署完成');
      setTimeout(() => {
        router.push('/contracts/list');
      }, 1500);
    } else {
      // 对方未签署的情况
      contractData.status = 'signing';
      contractData.ourSigned = true;
      message.success('我方签署完成，等待对方签署');
    }
  }, 1000);
};

onMounted(() => {
  // 获取合同编号
  const contractId = route.params.id as string;
  
  // 实际项目中应该根据ID请求合同详情和签署信息
  console.log('Loading contract signature data for ID:', contractId);
});
</script>

<style scoped>
.signature-page {
  padding: 0 16px;
}
.content-container {
  margin-top: 16px;
}
.contract-preview {
  position: relative;
  margin-bottom: 16px;
}
.preview-container {
  min-height: 500px;
  border: 1px solid #f0f0f0;
}
.signature-container {
  min-height: 500px;
  border: 1px solid #f0f0f0;
  overflow: auto;
}
.contract-page {
  position: relative;
  margin-bottom: 20px;
}
.signature-area {
  position: absolute;
  border: 1px dashed #d9d9d9;
  background-color: rgba(0, 0, 0, 0.02);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.signature-area.active {
  border: 2px solid #1890ff;
  z-index: 10;
}
.signature-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
.sign-parties {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.party {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.signature-list, .seal-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.signature-item, .seal-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}
.signature-preview, .seal-preview {
  width: 60px;
  height: 40px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.signature-preview img, .seal-preview img {
  max-width: 100%;
  max-height: 100%;
}
.signature-info, .seal-info {
  flex: 1;
}
.signature-name, .seal-name {
  margin-bottom: 4px;
}
.signature-actions, .seal-actions {
  display: flex;
  gap: 8px;
}
.add-signature, .add-seal {
  margin-top: 8px;
}
.signature-pad-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}
.signature-pad {
  width: 100%;
  height: 200px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
}
.input-signature {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.signature-preview {
  padding: 16px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.typed-signature {
  font-size: 32px;
}
.seal-preview-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
.seal-preview-wrapper {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}
.seal-preview-text {
  font-size: 16px;
  max-width: 80%;
  text-align: center;
}
.placeholder {
  color: #bfbfbf;
  font-size: 12px;
}
.signed-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>