<template>
  <div class="contract-upload">
    <a-page-header
      title="合同文本上传"
      sub-title="上传合同文档或选择合同模板开始起草合同"
      @back="goBack"
    >
      <template #extra>
        <a-button type="primary" @click="goToTemplates">使用合同模板</a-button>
      </template>
    </a-page-header>

    <a-card class="mt-4">
      <a-row :gutter="24">
        <a-col :span="16">
          <div class="upload-area">
            <a-upload-dragger
              v-model:fileList="fileList"
              :multiple="false"
              :before-upload="beforeUpload"
              @change="handleUploadChange"
              :showUploadList="showUploadList"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持单个文件上传，文件格式限制为：docx, pdf, doc（文件大小不超过10MB）
              </p>
            </a-upload-dragger>

            <div v-if="fileList.length > 0 && previewUrl" class="file-preview mt-4">
              <div class="preview-header">
                <span class="preview-title">文档预览</span>
                <a-space>
                  <a-button type="primary" size="small" @click="handleNext">
                    下一步：编辑合同信息
                  </a-button>
                  <a-button size="small" @click="handleReset">重新上传</a-button>
                </a-space>
              </div>
              <div class="preview-content">
                <iframe :src="previewUrl" width="100%" height="600" frameborder="0"></iframe>
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="upload-guide">
            <h3>上传指引</h3>
            <a-steps direction="vertical" :current="currentStep">
              <a-step title="上传合同文件" description="上传Word或PDF格式的合同文件" />
              <a-step title="填写合同信息" description="完善合同基本信息和关键条款" />
              <a-step title="提交审批" description="将合同提交至相关部门审批" />
              <a-step title="完成签署" description="完成合同签署和盖章流程" />
            </a-steps>

            <div class="tips mt-4">
              <h4>温馨提示</h4>
              <ul>
                <li>请确保上传的合同文件内容清晰可见</li>
                <li>合同关键信息将自动提取，请在下一步核对</li>
                <li>标准合同建议使用系统内置模板</li>
                <li>如有特殊格式需求，请联系法务部门</li>
              </ul>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <a-card class="mt-4" title="最近使用的合同模板">
      <a-row :gutter="16">
        <a-col :span="6" v-for="(template, index) in recentTemplates" :key="index">
          <a-card hoverable @click="selectTemplate(template)">
            <template #cover>
              <div class="template-cover">
                <file-text-outlined />
              </div>
            </template>
            <a-card-meta :title="template.name">
              <template #description>
                <div>
                  <p>{{ template.description }}</p>
                  <p>更新时间: {{ template.updateTime }}</p>
                </div>
              </template>
            </a-card-meta>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <a-modal v-model:visible="ocr.visible" title="文本识别中" :footer="null" :closable="false">
      <div class="text-center">
        <a-spin size="large" />
        <p class="mt-3">{{ ocr.status }}</p>
        <a-progress :percent="ocr.progress" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { InboxOutlined, FileTextOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const fileList = ref([])
const previewUrl = ref('')
const currentStep = ref(0)
const showUploadList = ref(false)

// OCR状态
const ocr = reactive({
  visible: false,
  status: '正在解析文档内容，请稍候...',
  progress: 0
})

// 最近使用的模板
const recentTemplates = ref([
  {
    id: 1,
    name: '销售合同标准模板',
    description: '适用于常规产品销售合同',
    updateTime: '2023-08-15'
  },
  {
    id: 2,
    name: '采购合同标准模板',
    description: '适用于原材料和设备采购',
    updateTime: '2023-07-20'
  },
  {
    id: 3,
    name: '技术服务合同',
    description: '适用于IT服务类合同',
    updateTime: '2023-06-12'
  },
  {
    id: 4,
    name: '框架协议模板',
    description: '长期业务合作框架协议',
    updateTime: '2023-05-30'
  }
])

// 返回上一页
const goBack = () => {
  router.push('/contracts')
}

// 前往模板页面
const goToTemplates = () => {
  router.push('/contracts/drafting/template')
}

// 选择模板
const selectTemplate = (template) => {
  message.success(`已选择模板: ${template.name}`)
  // 保存所选模板信息到本地，以便在编辑页面使用
  localStorage.setItem('selectedTemplate', JSON.stringify(template))
  router.push('/contracts/drafting/edit')
}

// 上传前校验
const beforeUpload = (file) => {
  const isDocOrPdf = file.type === 'application/pdf' || 
                    file.type === 'application/msword' ||
                    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  if (!isDocOrPdf) {
    message.error('只能上传PDF或Word文档!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  return true
}

// 处理上传变化
const handleUploadChange = ({ fileList: newFileList, file }) => {
  fileList.value = newFileList
  
  if (file.status === 'done' || file.status === 'uploading') {
    // 模拟文档上传和OCR识别过程
    if (file.status === 'uploading' && file.percent === 100) {
      simulateOcrProcess(file)
    }
  } else if (file.status === 'error') {
    message.error(`${file.name} 上传失败.`)
  }
}

// 模拟OCR识别过程
const simulateOcrProcess = (file) => {
  ocr.visible = true
  ocr.progress = 0
  
  const interval = setInterval(() => {
    ocr.progress += 10
    
    if (ocr.progress === 30) {
      ocr.status = '正在提取合同关键信息...'
    }
    
    if (ocr.progress === 60) {
      ocr.status = '正在生成预览...'
    }
    
    if (ocr.progress >= 100) {
      clearInterval(interval)
      ocr.visible = false
      
      // 模拟预览URL
      // 实际场景中应该从服务器获取处理后的PDF预览URL
      previewUrl.value = 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
      
      message.success(`${file.name} 上传成功，已完成文本识别`)
      
      // 保存上传的文件信息到本地存储
      const uploadedContract = {
        fileName: file.name,
        fileSize: file.size,
        uploadTime: new Date().toISOString(),
        previewUrl: previewUrl.value
      }
      localStorage.setItem('uploadedContract', JSON.stringify(uploadedContract))
    }
  }, 300)
}

// 下一步
const handleNext = () => {
  if (!fileList.value.length) {
    message.warning('请先上传合同文件')
    return
  }
  
  router.push('/contracts/drafting/edit')
}

// 重新上传
const handleReset = () => {
  fileList.value = []
  previewUrl.value = ''
  localStorage.removeItem('uploadedContract')
}

onMounted(() => {
  // 检查是否有上次未完成的上传
  const savedContract = localStorage.getItem('uploadedContract')
  if (savedContract) {
    const contractData = JSON.parse(savedContract)
    previewUrl.value = contractData.previewUrl
    fileList.value = [
      {
        uid: '-1',
        name: contractData.fileName,
        status: 'done',
        size: contractData.fileSize
      }
    ]
  }
})
</script>

<style scoped>
.upload-area {
  min-height: 400px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.preview-title {
  font-weight: 500;
  font-size: 16px;
}

.template-cover {
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  font-size: 48px;
  color: #1890ff;
}

.tips ul {
  padding-left: 20px;
  margin-top: 8px;
}

.tips ul li {
  margin-bottom: 8px;
  color: #666;
}
</style> 