<template>
  <div class="approval-page">
    <a-page-header
      title="合同审批流程"
      :breadcrumb="{ routes }"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button v-if="isApprover" type="primary" @click="approveContract">批准</a-button>
          <a-button v-if="isApprover" danger @click="rejectContract">驳回</a-button>
          <a-button v-if="canResubmit" type="primary" @click="resubmitContract">重新提交</a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-container">
      <a-row :gutter="16">
        <!-- 左侧内容 -->
        <a-col :span="16">
          <a-card title="合同基本信息" style="margin-bottom: 16px">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="合同名称" :span="2">{{ contractData.name }}</a-descriptions-item>
              <a-descriptions-item label="合同编号">{{ contractData.code }}</a-descriptions-item>
              <a-descriptions-item label="合同类型">{{ getContractType(contractData.type) }}</a-descriptions-item>
              <a-descriptions-item label="合同金额">{{ formatAmount(contractData.amount) }}</a-descriptions-item>
              <a-descriptions-item label="签约日期">{{ formatDate(contractData.signDate) }}</a-descriptions-item>
              <a-descriptions-item label="客户名称">{{ contractData.customerName }}</a-descriptions-item>
              <a-descriptions-item label="负责人">{{ contractData.managerName }}</a-descriptions-item>
              <a-descriptions-item label="提交时间">{{ formatDateTime(contractData.submitTime) }}</a-descriptions-item>
              <a-descriptions-item label="当前状态">
                <a-tag :color="getStatusColor(contractData.status)">{{ getStatusText(contractData.status) }}</a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <a-card title="合同预览" style="margin-bottom: 16px">
            <div class="preview-container">
              <iframe :src="previewUrl" width="100%" height="500" frameborder="0"></iframe>
            </div>
          </a-card>

          <a-card title="合同附件" style="margin-bottom: 16px">
            <a-table :columns="attachmentColumns" :data-source="contractData.attachments" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <a @click="previewAttachment(record)">预览</a>
                  <a-divider type="vertical" />
                  <a @click="downloadAttachment(record)">下载</a>
                </template>
              </template>
            </a-table>
          </a-card>

          <a-card title="审核意见" v-if="isApprover && !hasApproved">
            <a-form :model="approvalForm" layout="vertical">
              <a-form-item label="审核结果" name="result" :rules="[{ required: true, message: '请选择审核结果' }]">
                <a-radio-group v-model:value="approvalForm.result">
                  <a-radio value="approve">同意</a-radio>
                  <a-radio value="reject">驳回</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="审核意见" name="comment" :rules="[{ required: true, message: '请填写审核意见' }]">
                <a-textarea v-model:value="approvalForm.comment" :rows="4" placeholder="请填写审核意见" />
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="submitApproval">提交审核</a-button>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
        
        <!-- 右侧审批流程 -->
        <a-col :span="8">
          <a-card title="审批流程" style="margin-bottom: 16px">
            <a-steps direction="vertical" :current="getCurrentStep()">
              <a-step 
                v-for="(step, index) in approvalSteps" 
                :key="index" 
                :title="step.title"
                :description="getStepDescription(step)"
                :status="getStepStatus(step, index)"
              >
                <template #icon>
                  <check-circle-outlined v-if="step.status === 'approved'" />
                  <close-circle-outlined v-if="step.status === 'rejected'" />
                  <loading-outlined v-if="step.status === 'processing'" />
                </template>
              </a-step>
            </a-steps>
          </a-card>

          <a-card title="审批历史" style="margin-bottom: 16px">
            <a-timeline>
              <a-timeline-item v-for="(item, index) in approvalHistory" :key="index" :color="getHistoryColor(item)">
                <div class="timeline-item">
                  <div class="timeline-header">
                    <span class="user-name">{{ item.userName }}</span>
                    <span class="action-type">{{ getActionText(item.action) }}</span>
                  </div>
                  <div class="timeline-content">{{ item.comment }}</div>
                  <div class="timeline-footer">
                    <span class="time">{{ formatDateTime(item.time) }}</span>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>

          <a-card title="操作记录">
            <a-list item-layout="horizontal" :data-source="operationLogs">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.action }}</template>
                    <template #description>
                      <div>{{ item.userName }} - {{ formatDateTime(item.time) }}</div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 拒绝弹窗 -->
    <a-modal
      v-model:visible="rejectVisible"
      title="驳回合同"
      @ok="confirmReject"
      okText="确认驳回"
      cancelText="取消"
    >
      <a-form :model="rejectForm" layout="vertical">
        <a-form-item label="驳回原因" name="reason" :rules="[{ required: true, message: '请填写驳回原因' }]">
          <a-textarea v-model:value="rejectForm.reason" :rows="4" placeholder="请填写驳回原因" />
        </a-form-item>
        <a-form-item label="修改建议" name="suggestion">
          <a-textarea v-model:value="rejectForm.suggestion" :rows="4" placeholder="请提供修改建议(选填)" />
        </a-form-item>
        <a-form-item label="退回至" name="returnTo" :rules="[{ required: true, message: '请选择退回节点' }]">
          <a-select v-model:value="rejectForm.returnTo" placeholder="请选择退回节点">
            <a-select-option value="drafter">起草人</a-select-option>
            <a-select-option value="previous">上一级审批人</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  LoadingOutlined,
} from '@ant-design/icons-vue';

const route = useRoute();
const router = useRouter();

// 面包屑路由
const routes = [
  {
    path: '/contracts',
    breadcrumbName: '合同管理',
  },
  {
    path: '/contracts/list',
    breadcrumbName: '合同查询',
  },
  {
    path: '',
    breadcrumbName: '合同审批',
  },
];

// 合同数据
const contractData = reactive({
  id: 'CT-2024-0045',
  name: '销售合同-A公司-软件系统采购',
  code: 'HT-2024-0045',
  type: 'sales',
  amount: 450000,
  signDate: '2024-03-15',
  customerName: '北京科技有限公司',
  managerName: '张三',
  submitTime: '2024-03-16 09:45:30',
  status: 'approving',
  attachments: [
    { id: 1, name: '商务条款清单.xlsx', size: '125KB', uploadTime: '2024-03-15 09:30:52' },
    { id: 2, name: '技术协议.pdf', size: '2.5MB', uploadTime: '2024-03-15 10:15:37' },
  ]
});

// 预览URL
const previewUrl = ref('https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf');

// 审批流程步骤
const approvalSteps = ref([
  {
    title: '起草',
    handler: '张三(销售部)',
    time: '2024-03-16 09:30:25',
    status: 'approved',
  },
  {
    title: '部门主管审核',
    handler: '李四(销售部经理)',
    time: '2024-03-16 10:45:18',
    status: 'approved',
  },
  {
    title: '法务审核',
    handler: '王五(法务部)',
    time: '2024-03-16 14:20:45',
    status: 'processing',
  },
  {
    title: '财务审核',
    handler: '赵六(财务部)',
    time: null,
    status: 'waiting',
  },
  {
    title: '总经理审批',
    handler: '钱七(总经理)',
    time: null,
    status: 'waiting',
  },
]);

// 审批历史
const approvalHistory = ref([
  {
    userName: '张三(销售部)',
    action: 'submit',
    comment: '提交合同审核',
    time: '2024-03-16 09:30:25',
  },
  {
    userName: '李四(销售部经理)',
    action: 'approve',
    comment: '合同内容符合公司销售政策，同意通过',
    time: '2024-03-16 10:45:18',
  },
]);

// 操作记录
const operationLogs = ref([
  {
    userName: '张三',
    action: '创建合同',
    time: '2024-03-15 15:30:25',
  },
  {
    userName: '张三',
    action: '修改合同内容',
    time: '2024-03-15 16:45:18',
  },
  {
    userName: '张三',
    action: '上传附件',
    time: '2024-03-15 17:10:30',
  },
  {
    userName: '张三',
    action: '提交审批',
    time: '2024-03-16 09:30:25',
  },
]);

// 审核表单
const approvalForm = reactive({
  result: 'approve',
  comment: '',
});

// 拒绝表单
const rejectVisible = ref(false);
const rejectForm = reactive({
  reason: '',
  suggestion: '',
  returnTo: 'drafter',
});

// 附件列表列定义
const attachmentColumns = [
  {
    title: '文件名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
    width: 100,
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    key: 'uploadTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 判断当前用户是否为审批人
const isApprover = computed(() => {
  // 实际应用中，这里应该根据当前登录用户信息和审批流程判断
  // 此处为了演示，模拟当前用户为法务部审批人
  return true;
});

// 判断是否已经审批
const hasApproved = computed(() => {
  // 实际应用中，这里应该根据当前登录用户信息和审批记录判断
  return false;
});

// 判断是否可以重新提交
const canResubmit = computed(() => {
  // 实际应用中，这里应该根据当前登录用户信息和合同状态判断
  return contractData.status === 'rejected' && false; // 模拟不能重新提交
});

// 获取合同类型文本
const getContractType = (type: string) => {
  const typeMap: Record<string, string> = {
    sales: '销售合同',
    purchase: '采购合同',
    service: '服务合同',
    cooperation: '合作协议',
  };
  return typeMap[type] || type;
};

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥ ${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '';
  return date;
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  return dateTime;
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    approving: '审批中',
    approved: '已批准',
    rejected: '已驳回',
    signed: '已签署',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'blue',
    submitted: 'blue',
    approving: 'orange',
    approved: 'green',
    rejected: 'red',
    signed: 'green',
  };
  return colorMap[status] || 'default';
};

// 获取当前步骤
const getCurrentStep = () => {
  const idx = approvalSteps.value.findIndex(step => step.status === 'processing');
  return idx > -1 ? idx : 0;
};

// 获取步骤描述
const getStepDescription = (step: any) => {
  let description = step.handler || '';
  if (step.time) {
    description += `<br/>${step.time}`;
  }
  return description;
};

// 获取步骤状态
const getStepStatus = (step: any, index: number) => {
  switch (step.status) {
    case 'approved':
      return 'finish';
    case 'rejected':
      return 'error';
    case 'processing':
      return 'process';
    default:
      return 'wait';
  }
};

// 获取历史颜色
const getHistoryColor = (item: any) => {
  const colorMap: Record<string, string> = {
    submit: 'blue',
    approve: 'green',
    reject: 'red',
    comment: 'gray',
  };
  return colorMap[item.action] || 'blue';
};

// 获取操作文本
const getActionText = (action: string) => {
  const actionMap: Record<string, string> = {
    submit: '提交',
    approve: '批准',
    reject: '驳回',
    comment: '评论',
  };
  return actionMap[action] || action;
};

// 批准合同
const approveContract = () => {
  approvalForm.result = 'approve';
  submitApproval();
};

// 拒绝合同
const rejectContract = () => {
  rejectVisible.value = true;
};

// 确认拒绝
const confirmReject = () => {
  if (!rejectForm.reason) {
    message.error('请填写驳回原因');
    return;
  }
  
  // 模拟提交
  message.loading('正在处理...');
  setTimeout(() => {
    message.success('合同已驳回');
    rejectVisible.value = false;
    
    // 更新状态
    contractData.status = 'rejected';
    
    // 更新审批流程
    const currentStep = approvalSteps.value.find(step => step.status === 'processing');
    if (currentStep) {
      currentStep.status = 'rejected';
      currentStep.time = new Date().toLocaleString();
    }
    
    // 添加审批历史
    approvalHistory.value.push({
      userName: '王五(法务部)',
      action: 'reject',
      comment: rejectForm.reason,
      time: new Date().toLocaleString(),
    });
    
    // 添加操作记录
    operationLogs.value.push({
      userName: '王五',
      action: '驳回合同',
      time: new Date().toLocaleString(),
    });
  }, 1000);
};

// 重新提交合同
const resubmitContract = () => {
  message.success('合同已重新提交审批');
};

// 提交审核
const submitApproval = () => {
  if (!approvalForm.comment) {
    message.error('请填写审核意见');
    return;
  }
  
  // 模拟提交
  message.loading('正在处理...');
  setTimeout(() => {
    if (approvalForm.result === 'approve') {
      message.success('审核通过，已转交下一审批人');
      
      // 更新当前步骤状态
      const currentStepIndex = approvalSteps.value.findIndex(step => step.status === 'processing');
      if (currentStepIndex > -1) {
        approvalSteps.value[currentStepIndex].status = 'approved';
        approvalSteps.value[currentStepIndex].time = new Date().toLocaleString();
        
        // 设置下一步为处理中
        if (currentStepIndex < approvalSteps.value.length - 1) {
          approvalSteps.value[currentStepIndex + 1].status = 'processing';
        }
      }
      
      // 添加审批历史
      approvalHistory.value.push({
        userName: '王五(法务部)',
        action: 'approve',
        comment: approvalForm.comment,
        time: new Date().toLocaleString(),
      });
    } else {
      // 驳回逻辑
      confirmReject();
    }
    
    // 添加操作记录
    operationLogs.value.push({
      userName: '王五',
      action: approvalForm.result === 'approve' ? '通过审核' : '驳回合同',
      time: new Date().toLocaleString(),
    });
    
    // 重置表单
    approvalForm.comment = '';
  }, 1000);
};

// 预览附件
const previewAttachment = (record: any) => {
  message.success(`预览附件: ${record.name}`);
  // 实际项目中这里应该打开预览窗口
};

// 下载附件
const downloadAttachment = (record: any) => {
  message.success(`下载附件: ${record.name}`);
  // 实际项目中这里应该调用下载API
};

onMounted(() => {
  // 获取合同编号
  const contractId = route.params.id as string;
  
  // 实际项目中应该根据ID请求合同详情和审批流程
  console.log('Loading contract approval data for ID:', contractId);
});
</script>

<style scoped>
.approval-page {
  padding: 0 16px;
}
.content-container {
  margin-top: 16px;
}
.preview-container {
  min-height: 500px;
  border: 1px solid #f0f0f0;
}
.timeline-item {
  padding: 8px 0;
}
.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
.user-name {
  font-weight: 500;
}
.action-type {
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}
.timeline-content {
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
}
.timeline-footer {
  text-align: right;
  color: #999;
  font-size: 12px;
}
</style> 