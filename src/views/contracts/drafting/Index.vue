<template>
  <div class="contract-drafting-page">
    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">合同起草</h2>
          <p class="text-gray-500 mt-1">创建新合同或基于模板快速起草合同</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <a-card :bordered="false">
      <!-- 提示区域 -->
      <a-alert
        class="mb-6"
        message="请选择合同起草方式"
        description="您可以选择通过模板起草或上传文件方式创建合同"
        type="info"
        show-icon
      />

      <!-- 起草方式选择 -->
      <div class="drafting-options">
        <a-radio-group v-model:value="draftingMethod" size="large" class="mb-6">
          <a-radio-button value="template">使用模板起草</a-radio-button>
          <a-radio-button value="upload">上传文件起草</a-radio-button>
        </a-radio-group>
        
        <!-- 模板方式 -->
        <div v-if="draftingMethod === 'template'">
          <template-drafting @draft-success="handleDraftSuccess" />
        </div>
        
        <!-- 上传文件方式 -->
        <div v-else-if="draftingMethod === 'upload'">
          <upload-drafting @draft-success="handleDraftSuccess" />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import TemplateDrafting from './Template.vue';
import UploadDrafting from './Upload.vue';

const router = useRouter();
const draftingMethod = ref<'template' | 'upload'>('template');

// 处理起草成功事件
const handleDraftSuccess = (contractId: string) => {
  // 跳转到编辑页面
  router.push({
    name: 'contracts-drafting-edit',
    params: { id: contractId }
  });
};
</script>

<style scoped>
.contract-drafting-page {
  background-color: #f0f2f5;
}

.page-header {
  position: relative;
  margin-bottom: 16px;
}

.page-header h2 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card-body) {
  padding: 16px;
}

.drafting-options {
  display: flex;
  flex-direction: column;
}
</style> 