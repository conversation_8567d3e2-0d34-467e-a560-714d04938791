<template>
  <div class="my-approval-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">我的审批</h2>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="name">
                <a-input v-model:value="searchForm.name" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="code">
                <a-input v-model:value="searchForm.code" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="审批状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择审批状态" allowClear>
                  <a-select-option :value="0">待审批</a-select-option>
                  <a-select-option :value="1">已审批</a-select-option>
                  <a-select-option :value="2">已驳回</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="提交时间" name="submitTime">
                <a-range-picker v-model:value="searchForm.submitTime" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同类型" name="type">
                <a-select v-model:value="searchForm.type" placeholder="请选择合同类型" allowClear>
                  <a-select-option v-for="item in contractTypes" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="提交人" name="submitter">
                <a-input v-model:value="searchForm.submitter" placeholder="请输入提交人" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 数据统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="待审批" :value="statistics.pending" :precision="0" />
              <template #extra>
                <audit-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="今日审批" :value="statistics.today" :precision="0" />
              <template #extra>
                <calendar-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已批准" :value="statistics.approved" :precision="0" />
              <template #extra>
                <check-circle-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已驳回" :value="statistics.rejected" :precision="0" />
              <template #extra>
                <close-circle-outlined style="color: #f5222d; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 标签页切换 -->
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="pending" tab="待我审批">
          <div class="table-view">
            <a-table
              :columns="columns"
              :data-source="contractList"
              :loading="loading"
              :pagination="pagination"
              @change="handleTableChange"
              rowKey="id"
            >
              <!-- 紧急程度列 -->
              <template #urgency="{ text }">
                <a-tag v-if="text === 3" color="red">紧急</a-tag>
                <a-tag v-else-if="text === 2" color="orange">较急</a-tag>
                <a-tag v-else color="blue">普通</a-tag>
              </template>
              
              <!-- 合同名称列 -->
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'name'">
                  <a @click="viewApproval(record)">{{ record.name }}</a>
                </template>
                
                <!-- 合同类型列 -->
                <template v-if="column.dataIndex === 'type'">
                  {{ getContractType(record.type) }}
                </template>
                
                <!-- 审批环节列 -->
                <template v-if="column.dataIndex === 'step'">
                  <a-tag color="blue">{{ record.step }}</a-tag>
                </template>
                
                <!-- 时间列 -->
                <template v-if="column.dataIndex === 'submitTime'">
                  {{ record.submitTime }}
                </template>
                
                <!-- 操作列 -->
                <template v-if="column.dataIndex === 'action'">
                  <a-space>
                    <a-button type="primary" size="small" @click="handleApprove(record)">批准</a-button>
                    <a-button danger size="small" @click="handleReject(record)">驳回</a-button>
                    <a-button type="link" size="small" @click="viewApproval(record)">详情</a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="approved" tab="我已审批">
          <div class="table-view">
            <a-table
              :columns="historyColumns"
              :data-source="historyList"
              :loading="historyLoading"
              :pagination="historyPagination"
              @change="handleHistoryTableChange"
              rowKey="id"
            >
              <!-- 合同名称列 -->
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'name'">
                  <a @click="viewApproval(record)">{{ record.name }}</a>
                </template>
                
                <!-- 合同类型列 -->
                <template v-if="column.dataIndex === 'type'">
                  {{ getContractType(record.type) }}
                </template>
                
                <!-- 审批环节列 -->
                <template v-if="column.dataIndex === 'step'">
                  <a-tag color="blue">{{ record.step }}</a-tag>
                </template>
                
                <!-- 审批结果列 -->
                <template v-if="column.dataIndex === 'result'">
                  <a-tag :color="record.result === 1 ? 'green' : 'red'">
                    {{ record.result === 1 ? '已批准' : '已驳回' }}
                  </a-tag>
                </template>
                
                <!-- 时间列 -->
                <template v-if="column.dataIndex === 'approveTime'">
                  {{ record.approveTime }}
                </template>
                
                <!-- 操作列 -->
                <template v-if="column.dataIndex === 'action'">
                  <a-button type="link" size="small" @click="viewApproval(record)">详情</a-button>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 驳回弹窗 -->
    <a-modal
      v-model:visible="rejectModalVisible"
      title="驳回合同"
      @ok="confirmReject"
      :confirmLoading="rejectLoading"
      okText="确认驳回"
      cancelText="取消"
    >
      <a-form :model="rejectForm" layout="vertical">
        <a-form-item label="驳回原因" name="reason" :rules="[{ required: true, message: '请填写驳回原因' }]">
          <a-textarea v-model:value="rejectForm.reason" :rows="4" placeholder="请填写驳回原因" />
        </a-form-item>
        <a-form-item label="修改建议" name="suggestion">
          <a-textarea v-model:value="rejectForm.suggestion" :rows="4" placeholder="请提供修改建议(选填)" />
        </a-form-item>
        <a-form-item label="退回至" name="returnTo" :rules="[{ required: true, message: '请选择退回节点' }]">
          <a-select v-model:value="rejectForm.returnTo" placeholder="请选择退回节点">
            <a-select-option value="drafter">起草人</a-select-option>
            <a-select-option value="previous">上一级审批人</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  AuditOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons-vue';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface Contract {
  id: string;
  name: string;
  code: string;
  type: string;
  submitter: string;
  submitTime: string;
  step: string;
  urgency: number;
  result?: number;
  approveTime?: string;
}

const router = useRouter();
const loading = ref(false);
const historyLoading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const activeTab = ref('pending');
const rejectModalVisible = ref(false);
const rejectLoading = ref(false);
const currentContract = ref<Contract | null>(null);

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  status: undefined,
  submitTime: [],
  type: undefined,
  submitter: '',
});

// 驳回表单
const rejectForm = reactive({
  reason: '',
  suggestion: '',
  returnTo: 'drafter',
});

// 统计数据
const statistics = reactive({
  pending: 12,
  today: 4,
  approved: 86,
  rejected: 15
});

// 合同类型选项
const contractTypes = [
  { label: '销售合同', value: 'sales' },
  { label: '采购合同', value: 'purchase' },
  { label: '服务合同', value: 'service' },
  { label: '合作协议', value: 'cooperation' },
  { label: '其他', value: 'other' }
];

// 待审批列表列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'code',
    key: 'code',
    width: 150,
  },
  {
    title: '合同类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '提交人',
    dataIndex: 'submitter',
    key: 'submitter',
    width: 120,
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 170,
    sorter: true
  },
  {
    title: '审批环节',
    dataIndex: 'step',
    key: 'step',
    width: 150,
  },
  {
    title: '紧急程度',
    dataIndex: 'urgency',
    key: 'urgency',
    width: 100,
    slots: {
      customRender: 'urgency',
    }
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 230,
  }
];

// 审批历史列表列定义
const historyColumns = [
  {
    title: '合同名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'code',
    key: 'code',
    width: 150,
  },
  {
    title: '合同类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '提交人',
    dataIndex: 'submitter',
    key: 'submitter',
    width: 120,
  },
  {
    title: '审批环节',
    dataIndex: 'step',
    key: 'step',
    width: 150,
  },
  {
    title: '审批结果',
    dataIndex: 'result',
    key: 'result',
    width: 120,
  },
  {
    title: '审批时间',
    dataIndex: 'approveTime',
    key: 'approveTime',
    width: 170,
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 历史分页配置
const historyPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 合同列表数据
const contractList = ref<Contract[]>([]);

// 历史列表数据
const historyList = ref<Contract[]>([]);

// 获取合同类型文本
const getContractType = (type: string): string => {
  const found = contractTypes.find(item => item.value === type);
  return found ? found.label : type;
};

// 获取待审批合同列表
const fetchContractList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    contractList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取待审批合同列表失败');
    console.error('获取待审批合同列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取审批历史列表
const fetchHistoryList = async () => {
  historyLoading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateHistoryMockData();
    historyList.value = mockData.items;
    historyPagination.total = mockData.total;
  } catch (error) {
    message.error('获取审批历史列表失败');
    console.error('获取审批历史列表失败:', error);
  } finally {
    historyLoading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const types = contractTypes.map(t => t.value);
  const steps = ['部门审批', '法务审核', '财务审核', '总经理审批'];
  const total = 12;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const randomMinutes = Math.floor(Math.random() * 60 * 24 * 3); // 3天内的随机时间
    const submitTime = new Date(now.getTime() - randomMinutes * 60 * 1000);
    
    items.push({
      id: `C2023${String(i).padStart(4, '0')}`,
      name: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      code: `HT-${new Date().getFullYear()}-${String(i).padStart(4, '0')}`,
      type: types[Math.floor(Math.random() * types.length)],
      submitter: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`,
      submitTime: formatDateTime(submitTime),
      step: steps[Math.floor(Math.random() * steps.length)],
      urgency: Math.floor(Math.random() * 3) + 1,
    });
  }

  return {
    items,
    total
  };
};

// 生成历史模拟数据
const generateHistoryMockData = () => {
  const items = [];
  const types = contractTypes.map(t => t.value);
  const steps = ['部门审批', '法务审核', '财务审核', '总经理审批'];
  const total = 101;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * 30); // 30天内的随机时间
    const approveTime = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    
    items.push({
      id: `C2023${String(i + 100).padStart(4, '0')}`,
      name: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      code: `HT-${new Date().getFullYear()}-${String(i + 100).padStart(4, '0')}`,
      type: types[Math.floor(Math.random() * types.length)],
      submitter: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`,
      step: steps[Math.floor(Math.random() * steps.length)],
      result: Math.random() > 0.2 ? 1 : 0, // 80%概率批准，20%概率驳回
      approveTime: formatDateTime(approveTime),
    });
  }

  return {
    items,
    total
  };
};

// 格式化日期时间
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 查询
const handleSearch = () => {
  if (activeTab.value === 'pending') {
    pagination.current = 1;
    fetchContractList();
  } else {
    historyPagination.current = 1;
    fetchHistoryList();
  }
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  
  if (activeTab.value === 'pending') {
    fetchContractList();
  } else {
    fetchHistoryList();
  }
};

// 表格变更处理
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchContractList();
};

// 历史表格变更处理
const handleHistoryTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  historyPagination.current = pag.current;
  historyPagination.pageSize = pag.pageSize;
  fetchHistoryList();
};

// 标签页切换
const handleTabChange = () => {
  if (activeTab.value === 'pending') {
    fetchContractList();
  } else {
    fetchHistoryList();
  }
};

// 查看审批详情
const viewApproval = (record: Contract) => {
  router.push(`/contracts/draft-approval/approval/${record.id}`);
};

// 批准合同
const handleApprove = (record: Contract) => {
  Modal.confirm({
    title: '确认批准',
    content: `确定要批准合同 "${record.name}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      // 模拟提交
      message.loading('正在处理...');
      setTimeout(() => {
        message.success('合同已批准，流程已转交到下一审批节点');
        fetchContractList();
      }, 1000);
    }
  });
};

// 驳回合同
const handleReject = (record: Contract) => {
  currentContract.value = record;
  rejectForm.reason = '';
  rejectForm.suggestion = '';
  rejectForm.returnTo = 'drafter';
  rejectModalVisible.value = true;
};

// 确认驳回
const confirmReject = () => {
  if (!rejectForm.reason) {
    message.error('请填写驳回原因');
    return;
  }
  
  rejectLoading.value = true;
  
  // 模拟提交
  setTimeout(() => {
    rejectLoading.value = false;
    rejectModalVisible.value = false;
    message.success('合同已驳回');
    fetchContractList();
  }, 1000);
};

onMounted(() => {
  fetchContractList();
});
</script>

<style scoped>
.my-approval-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.table-view {
  margin-bottom: 16px;
}
</style> 