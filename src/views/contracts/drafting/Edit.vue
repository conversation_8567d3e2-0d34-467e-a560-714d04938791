<template>
  <div class="contract-edit-page">
    <a-page-header
      title="合同草案编辑"
      :breadcrumb="{ routes }"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button>保存草稿</a-button>
          <a-button type="primary">提交审批</a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="content-container">
      <a-row :gutter="16">
        <!-- 左侧合同编辑区 -->
        <a-col :span="16">
          <a-card title="合同内容" bordered>
            <a-form :model="contractForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <a-form-item label="合同标题">
                <a-input v-model:value="contractForm.title" placeholder="请输入合同标题" />
              </a-form-item>
              
              <a-form-item label="合同编号">
                <a-input v-model:value="contractForm.code" placeholder="系统自动生成合同编号" disabled />
              </a-form-item>
              
              <a-form-item label="合同类型">
                <a-select v-model:value="contractForm.type" placeholder="请选择合同类型">
                  <a-select-option value="sales">销售合同</a-select-option>
                  <a-select-option value="purchase">采购合同</a-select-option>
                  <a-select-option value="service">服务合同</a-select-option>
                  <a-select-option value="labor">劳务合同</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="合同正文">
                <div class="editor-container" style="border: 1px solid #d9d9d9; min-height: 500px; padding: 10px">
                  <!-- 这里可以集成富文本编辑器如TinyMCE或WangEditor等 -->
                  <div v-html="contractForm.content"></div>
                </div>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
        
        <!-- 右侧合同信息栏 -->
        <a-col :span="8">
          <a-card title="合同基本信息" style="margin-bottom: 16px">
            <a-form :model="contractInfo" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
              <a-form-item label="合同金额">
                <a-input-number 
                  v-model:value="contractInfo.amount" 
                  style="width: 100%"
                  :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                  :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
                  placeholder="请输入合同金额"
                />
              </a-form-item>
              
              <a-form-item label="签订日期">
                <a-date-picker v-model:value="contractInfo.signDate" style="width: 100%" />
              </a-form-item>
              
              <a-form-item label="生效日期">
                <a-date-picker v-model:value="contractInfo.effectiveDate" style="width: 100%" />
              </a-form-item>
              
              <a-form-item label="到期日期">
                <a-date-picker v-model:value="contractInfo.expiryDate" style="width: 100%" />
              </a-form-item>
              
              <a-form-item label="对方单位">
                <a-select 
                  v-model:value="contractInfo.counterparty" 
                  placeholder="请选择合作方"
                  :options="counterpartyOptions"
                  show-search
                />
              </a-form-item>
              
              <a-form-item label="我方签约人">
                <a-select 
                  v-model:value="contractInfo.ourSignatory" 
                  placeholder="请选择签约人"
                  :options="employeeOptions"
                />
              </a-form-item>
              
              <a-form-item label="关联项目">
                <a-select 
                  v-model:value="contractInfo.relatedProject" 
                  placeholder="请选择关联项目"
                  :options="projectOptions"
                />
              </a-form-item>
            </a-form>
          </a-card>
          
          <a-card title="附件">
            <a-upload-dragger
              v-model:fileList="fileList"
              name="file"
              :multiple="true"
              action="/api/contract/upload"
              @change="handleChange"
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">支持单个或批量上传附件</p>
            </a-upload-dragger>
            
            <div style="margin-top: 16px">
              <a-table 
                :columns="attachmentColumns"
                :data-source="attachmentList"
                size="small"
                :pagination="false"
              >
                <template #action="{ record }">
                  <div>
                    <a style="margin-right: 8px">预览</a>
                    <a>删除</a>
                  </div>
                </template>
              </a-table>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { InboxOutlined } from '@ant-design/icons-vue';

const router = useRouter();

// 面包屑路由
const routes = [
  {
    path: '/contracts',
    breadcrumbName: '合同管理',
  },
  {
    path: '/contracts/drafting',
    breadcrumbName: '合同起草',
  },
  {
    path: '',
    breadcrumbName: '合同编辑',
  },
];

// 合同表单数据
const contractForm = reactive({
  title: '销售合同模板-202403版',
  code: 'CT-2024-0045',
  type: 'sales',
  content: `<h1 style="text-align:center">销售合同</h1>
  <p>甲方：__________________</p>
  <p>乙方：__________________</p>
  <p>签订日期：__________________</p>
  <p>&nbsp;</p>
  <p>一、合同标的</p>
  <p>甲方同意向乙方购买以下商品：</p>
  <p>&nbsp;</p>
  <p>二、合同金额</p>
  <p>合同总金额为人民币：__________________元</p>
  <p>&nbsp;</p>
  <p>三、交付方式</p>
  <p>甲方应于__________________前向乙方交付上述商品。</p>
  <p>&nbsp;</p>
  <p>四、付款方式</p>
  <p>乙方应按照如下方式向甲方支付货款：</p>
  <p>1. 签订合同后支付30%；</p>
  <p>2. 交付货物后支付60%；</p>
  <p>3. 验收合格后支付10%。</p>`,
});

// 合同基本信息
const contractInfo = reactive({
  amount: 250000,
  signDate: null,
  effectiveDate: null,
  expiryDate: null,
  counterparty: null,
  ourSignatory: null,
  relatedProject: null,
});

// 选项数据
const counterpartyOptions = ref([
  { value: '1', label: '北京科技有限公司' },
  { value: '2', label: '上海贸易有限公司' },
  { value: '3', label: '广州电子科技有限公司' },
]);

const employeeOptions = ref([
  { value: '1', label: '张三 - 销售部经理' },
  { value: '2', label: '李四 - 法务总监' },
  { value: '3', label: '王五 - 总经理' },
]);

const projectOptions = ref([
  { value: '1', label: 'PRJ-20240001 - 智能化系统建设项目' },
  { value: '2', label: 'PRJ-20240002 - 软件开发服务项目' },
  { value: '3', label: 'PRJ-20240003 - 设备采购项目' },
]);

// 附件相关
const fileList = ref([]);
const attachmentList = ref([
  { key: '1', name: '商务条款清单.xlsx', size: '125KB', uploadTime: '2024-03-15 09:30:52' },
  { key: '2', name: '技术协议.pdf', size: '2.5MB', uploadTime: '2024-03-15 10:15:37' },
]);

const attachmentColumns = [
  {
    title: '文件名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    key: 'uploadTime',
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    customRender: () => {
      return null;
    },
    slots: {
      customRender: 'action',
    },
  },
];

const handleChange = (info) => {
  const { status } = info.file;
  
  if (status === 'done') {
    const newAttachment = {
      key: `${attachmentList.value.length + 1}`,
      name: info.file.name,
      size: `${Math.ceil(info.file.size / 1024)}KB`,
      uploadTime: new Date().toLocaleString(),
    };
    attachmentList.value.push(newAttachment);
  }
};

onMounted(() => {
  // 初始化加载数据
  // 可以在这里获取合同模板内容、合作方列表等
});
</script>

<style scoped>
.contract-edit-page {
  padding: 0 16px;
}
.content-container {
  margin-top: 16px;
}
.editor-container {
  border: 1px solid #d9d9d9;
  min-height: 500px;
  padding: 10px;
}
</style> 