<template>
  <div class="contract-template">
    <a-page-header
      title="合同模板选择"
      sub-title="选择合适的合同模板开始起草合同"
      @back="goBack"
    >
      <template #extra>
        <a-button @click="goToUpload">自行上传合同文件</a-button>
      </template>
    </a-page-header>

    <a-card class="mt-4">
      <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
        <a-tab-pane key="frequent" tab="常用模板">
          <div class="template-filter mb-4">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-input v-model:value="searchQuery" placeholder="搜索模板名称" 
                  allow-clear @change="filterTemplates" />
              </a-col>
              <a-col :span="6">
                <a-select 
                  v-model:value="filterCategory" 
                  placeholder="选择类别" 
                  style="width: 100%"
                  @change="filterTemplates">
                  <a-select-option value="">全部类别</a-select-option>
                  <a-select-option v-for="cat in categories" :key="cat" :value="cat">
                    {{ cat }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-select 
                  v-model:value="filterDepartment" 
                  placeholder="部门筛选" 
                  style="width: 100%"
                  @change="filterTemplates">
                  <a-select-option value="">全部部门</a-select-option>
                  <a-select-option v-for="dept in departments" :key="dept" :value="dept">
                    {{ dept }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-button type="primary" @click="filterTemplates">筛选</a-button>
                <a-button style="margin-left: 8px" @click="resetFilters">重置</a-button>
              </a-col>
            </a-row>
          </div>

          <div class="template-list">
            <a-row :gutter="16">
              <a-col :span="6" v-for="template in filteredTemplates" :key="template.id">
                <a-card hoverable class="template-card" @click="viewTemplate(template)">
                  <template #cover>
                    <div class="template-cover" :class="{'recommended': template.isRecommended}">
                      <file-outlined />
                      <span v-if="template.isRecommended" class="recommend-badge">推荐</span>
                    </div>
                  </template>
                  <a-card-meta :title="template.name">
                    <template #description>
                      <div>
                        <p class="template-description">{{ template.description }}</p>
                        <p class="template-info">
                          <span>{{ template.category }}</span>
                          <a-divider type="vertical" />
                          <span>使用次数: {{ template.usageCount }}</span>
                        </p>
                        <p class="template-meta">
                          <span>{{ template.department }}</span>
                          <a-divider type="vertical" />
                          <span>{{ template.updateTime }}</span>
                        </p>
                      </div>
                    </template>
                  </a-card-meta>
                  <template #actions>
                    <eye-outlined key="view" @click.stop="previewTemplate(template)" />
                    <copy-outlined key="copy" @click.stop="useTemplate(template)" />
                    <star-outlined v-if="!template.favorite" key="favorite" @click.stop="toggleFavorite(template)" />
                    <star-filled v-else key="unfavorite" style="color: #faad14" @click.stop="toggleFavorite(template)" />
                  </template>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="favorite" tab="收藏模板">
          <div class="favorite-templates">
            <div v-if="favoriteTemplates.length === 0" class="empty-data">
              <a-empty description="暂无收藏的模板" />
            </div>
            <a-row v-else :gutter="16">
              <a-col :span="6" v-for="template in favoriteTemplates" :key="template.id">
                <a-card hoverable class="template-card" @click="viewTemplate(template)">
                  <template #cover>
                    <div class="template-cover">
                      <file-outlined />
                    </div>
                  </template>
                  <a-card-meta :title="template.name">
                    <template #description>
                      <div>
                        <p class="template-description">{{ template.description }}</p>
                        <p class="template-info">
                          <span>{{ template.category }}</span>
                          <a-divider type="vertical" />
                          <span>使用次数: {{ template.usageCount }}</span>
                        </p>
                        <p class="template-meta">
                          <span>{{ template.department }}</span>
                          <a-divider type="vertical" />
                          <span>{{ template.updateTime }}</span>
                        </p>
                      </div>
                    </template>
                  </a-card-meta>
                  <template #actions>
                    <eye-outlined key="view" @click.stop="previewTemplate(template)" />
                    <copy-outlined key="copy" @click.stop="useTemplate(template)" />
                    <star-filled key="unfavorite" style="color: #faad14" @click.stop="toggleFavorite(template)" />
                  </template>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="recent" tab="最近使用">
          <div class="recent-templates">
            <div v-if="recentTemplates.length === 0" class="empty-data">
              <a-empty description="暂无最近使用的模板" />
            </div>
            <a-row v-else :gutter="16">
              <a-col :span="6" v-for="template in recentTemplates" :key="template.id">
                <a-card hoverable class="template-card" @click="viewTemplate(template)">
                  <template #cover>
                    <div class="template-cover">
                      <file-outlined />
                    </div>
                  </template>
                  <a-card-meta :title="template.name">
                    <template #description>
                      <div>
                        <p class="template-description">{{ template.description }}</p>
                        <p class="template-info">
                          <span>{{ template.category }}</span>
                          <a-divider type="vertical" />
                          <span>上次使用: {{ template.lastUsed }}</span>
                        </p>
                      </div>
                    </template>
                  </a-card-meta>
                  <template #actions>
                    <eye-outlined key="view" @click.stop="previewTemplate(template)" />
                    <copy-outlined key="copy" @click.stop="useTemplate(template)" />
                    <star-outlined v-if="!template.favorite" key="favorite" @click.stop="toggleFavorite(template)" />
                    <star-filled v-else key="unfavorite" style="color: #faad14" @click.stop="toggleFavorite(template)" />
                  </template>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <a-modal v-model:visible="previewVisible" title="模板预览" width="1000px" @ok="handleUseTemplate">
      <div class="template-preview">
        <div class="preview-info mb-4">
          <h3>{{ selectedTemplate?.name }}</h3>
          <p>{{ selectedTemplate?.description }}</p>
          <a-descriptions :column="3">
            <a-descriptions-item label="类别">{{ selectedTemplate?.category }}</a-descriptions-item>
            <a-descriptions-item label="所属部门">{{ selectedTemplate?.department }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ selectedTemplate?.updateTime }}</a-descriptions-item>
            <a-descriptions-item label="使用次数">{{ selectedTemplate?.usageCount }}</a-descriptions-item>
            <a-descriptions-item label="版本">{{ selectedTemplate?.version || 'v1.0' }}</a-descriptions-item>
            <a-descriptions-item label="责任人">{{ selectedTemplate?.owner || '法务部' }}</a-descriptions-item>
          </a-descriptions>
        </div>
        <div class="preview-content">
          <iframe :src="previewUrl" width="100%" height="500" frameborder="0"></iframe>
        </div>
      </div>
      <template #footer>
        <a-button key="back" @click="previewVisible = false">关闭</a-button>
        <a-button key="use" type="primary" @click="handleUseTemplate">使用此模板</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { 
  FileOutlined, 
  EyeOutlined, 
  CopyOutlined, 
  StarOutlined, 
  StarFilled 
} from '@ant-design/icons-vue'

const router = useRouter()
const activeTabKey = ref('frequent')
const searchQuery = ref('')
const filterCategory = ref('')
const filterDepartment = ref('')
const previewVisible = ref(false)
const previewUrl = ref('https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf')
const selectedTemplate = ref(null)

// 所有模板数据
const allTemplates = ref([
  {
    id: 1,
    name: '销售合同标准模板',
    description: '适用于产品销售的标准合同模板，包含质量标准、交付条款等内容',
    category: '销售合同',
    department: '销售部',
    updateTime: '2023-09-15',
    usageCount: 158,
    isRecommended: true,
    favorite: true,
    lastUsed: '2023-10-01'
  },
  {
    id: 2,
    name: '采购合同通用模板',
    description: '用于采购原材料、设备等的标准合同模板，含质保和验收条款',
    category: '采购合同',
    department: '采购部',
    updateTime: '2023-08-20',
    usageCount: 126,
    isRecommended: false,
    favorite: false,
    lastUsed: '2023-09-25'
  },
  {
    id: 3,
    name: '框架协议模板',
    description: '长期业务合作框架协议，规定合作方式和基本条款',
    category: '框架协议',
    department: '法务部',
    updateTime: '2023-07-05',
    usageCount: 87,
    isRecommended: true,
    favorite: true,
    lastUsed: '2023-09-18'
  },
  {
    id: 4,
    name: '技术服务合同',
    description: '适用于IT服务、技术咨询等服务类合同',
    category: '服务合同',
    department: '技术部',
    updateTime: '2023-06-12',
    usageCount: 76,
    isRecommended: false,
    favorite: false,
    lastUsed: '2023-08-30'
  },
  {
    id: 5,
    name: '保密协议(NDA)',
    description: '标准保密协议，用于保护商业机密和知识产权',
    category: '保密协议',
    department: '法务部',
    updateTime: '2023-05-18',
    usageCount: 203,
    isRecommended: true,
    favorite: false,
    lastUsed: '2023-10-05'
  },
  {
    id: 6,
    name: '劳务合同模板',
    description: '适用于临时劳务人员的合同模板',
    category: '人事合同',
    department: '人力资源部',
    updateTime: '2023-04-10',
    usageCount: 65,
    isRecommended: false,
    favorite: false,
    lastUsed: '2023-07-20'
  },
  {
    id: 7,
    name: '设备租赁合同',
    description: '用于设备租赁的标准合同，包含租赁期限、费用和责任条款',
    category: '租赁合同',
    department: '资产管理部',
    updateTime: '2023-03-25',
    usageCount: 42,
    isRecommended: false,
    favorite: true,
    lastUsed: '2023-09-10'
  },
  {
    id: 8,
    name: '国际贸易合同',
    description: '适用于国际贸易的合同模板，符合国际贸易惯例',
    category: '国际合同',
    department: '国际贸易部',
    updateTime: '2023-02-15',
    usageCount: 58,
    isRecommended: false,
    favorite: false,
    lastUsed: '2023-08-12'
  }
])

// 筛选后的模板列表
const filteredTemplates = ref([...allTemplates.value])

// 收藏的模板
const favoriteTemplates = computed(() => {
  return allTemplates.value.filter(template => template.favorite)
})

// 最近使用的模板
const recentTemplates = computed(() => {
  // 按最近使用时间排序
  return [...allTemplates.value].sort((a, b) => {
    return new Date(b.lastUsed) - new Date(a.lastUsed)
  }).slice(0, 8) // 只显示最近8个
})

// 可用类别列表
const categories = computed(() => {
  const cats = new Set(allTemplates.value.map(t => t.category))
  return Array.from(cats)
})

// 部门列表
const departments = computed(() => {
  const depts = new Set(allTemplates.value.map(t => t.department))
  return Array.from(depts)
})

// 返回上一页
const goBack = () => {
  router.push('/contracts')
}

// 前往上传页面
const goToUpload = () => {
  router.push('/contracts/drafting/upload')
}

// 处理标签页切换
const handleTabChange = (key) => {
  console.log('Tab changed to:', key)
}

// 筛选模板
const filterTemplates = () => {
  filteredTemplates.value = allTemplates.value.filter(template => {
    const nameMatch = template.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const categoryMatch = !filterCategory.value || template.category === filterCategory.value
    const departmentMatch = !filterDepartment.value || template.department === filterDepartment.value
    
    return nameMatch && categoryMatch && departmentMatch
  })
}

// 重置筛选条件
const resetFilters = () => {
  searchQuery.value = ''
  filterCategory.value = ''
  filterDepartment.value = ''
  filteredTemplates.value = [...allTemplates.value]
}

// 查看模板详情
const viewTemplate = (template) => {
  selectedTemplate.value = template
  previewVisible.value = true
}

// 预览模板
const previewTemplate = (template) => {
  selectedTemplate.value = template
  previewVisible.value = true
}

// 使用模板
const useTemplate = (template) => {
  selectedTemplate.value = template
  handleUseTemplate()
}

// 确认使用模板
const handleUseTemplate = () => {
  if (!selectedTemplate.value) return
  
  // 更新使用次数和最后使用时间
  const idx = allTemplates.value.findIndex(t => t.id === selectedTemplate.value.id)
  if (idx > -1) {
    allTemplates.value[idx].usageCount++
    allTemplates.value[idx].lastUsed = new Date().toISOString().split('T')[0]
  }
  
  // 保存所选模板信息到本地，以便在编辑页面使用
  localStorage.setItem('selectedTemplate', JSON.stringify(selectedTemplate.value))
  
  message.success(`已选择模板: ${selectedTemplate.value.name}`)
  previewVisible.value = false
  
  // 跳转到编辑页面
  router.push('/contracts/drafting/edit')
}

// 切换收藏状态
const toggleFavorite = (template) => {
  const idx = allTemplates.value.findIndex(t => t.id === template.id)
  if (idx > -1) {
    allTemplates.value[idx].favorite = !allTemplates.value[idx].favorite
    
    if (allTemplates.value[idx].favorite) {
      message.success(`已收藏模板: ${template.name}`)
    } else {
      message.success(`已取消收藏: ${template.name}`)
    }
  }
}

onMounted(() => {
  // 初始化筛选
  filterTemplates()
})
</script>

<style scoped>
.template-card {
  margin-bottom: 16px;
  height: 100%;
}

.template-cover {
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  font-size: 48px;
  color: #1890ff;
  position: relative;
}

.template-cover.recommended {
  background-color: #e6f7ff;
}

.recommend-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: #ff4d4f;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.template-description {
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #666;
  margin-bottom: 8px;
}

.template-info, .template-meta {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.empty-data {
  padding: 48px 0;
}

.preview-info h3 {
  margin-bottom: 8px;
}

.preview-info p {
  margin-bottom: 16px;
  color: #666;
}
</style> 