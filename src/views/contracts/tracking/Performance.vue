<template>
  <div class="performance-page">
    <a-card :bordered="false">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">合同履约评估</h2>
          <div class="action-buttons">
            <a-button type="primary" @click="createPerformanceRecord">
              <template #icon><plus-outlined /></template>
              新增评估记录
            </a-button>
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评估类型" name="assessmentType">
                <a-select v-model:value="searchForm.assessmentType" placeholder="请选择评估类型" allowClear>
                  <a-select-option v-for="item in assessmentTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评估结果" name="result">
                <a-select v-model:value="searchForm.result" placeholder="请选择评估结果" allowClear>
                  <a-select-option v-for="item in resultOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评估日期" name="assessmentDate">
                <a-range-picker v-model:value="searchForm.assessmentDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="评估人" name="assessor">
                <a-input v-model:value="searchForm.assessor" placeholder="请输入评估人" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="合同总数" :value="statistics.totalContracts" />
              <template #extra>
                <file-text-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="优良合同数" :value="statistics.excellentContracts" />
              <template #extra>
                <trophy-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="履约风险合同" :value="statistics.riskContracts" />
              <template #extra>
                <warning-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="平均评分" :value="statistics.averageScore" :precision="1" suffix="分" />
              <template #extra>
                <line-chart-outlined style="color: #722ed1; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <div class="table-view">
        <a-table
          :columns="columns"
          :data-source="performanceList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <!-- 合同名称列 -->
            <template v-if="column.dataIndex === 'contractName'">
              <a @click="viewContract(record)">{{ record.contractName }}</a>
            </template>
            
            <!-- 评分列 -->
            <template v-if="column.dataIndex === 'score'">
              <a-progress
                :percent="record.score * 10"
                :format="() => record.score"
                :status="getScoreStatus(record.score)"
                size="small"
              />
            </template>
            
            <!-- 评估结果列 -->
            <template v-if="column.dataIndex === 'result'">
              <a-tag :color="getResultColor(record.result)">
                {{ getResultText(record.result) }}
              </a-tag>
            </template>
            
            <!-- 操作列 -->
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="editRecord(record)">编辑</a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="downloadAttachments(record)">下载附件</a-menu-item>
                      <a-menu-item @click="generateReport(record)">生成报告</a-menu-item>
                      <a-menu-item @click="deleteRecord(record)">删除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import {
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DownOutlined,
  FileTextOutlined,
  TrophyOutlined,
  WarningOutlined,
  LineChartOutlined
} from '@ant-design/icons-vue';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface PerformanceRecord {
  id: string;
  contractId: string;
  contractName: string;
  contractCode: string;
  assessmentType: string;
  assessmentDate: string;
  assessor: string;
  score: number;
  result: string;
  remark: string;
  attachments?: string[];
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const performanceList = ref<PerformanceRecord[]>([]);

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  assessmentType: undefined,
  result: undefined,
  assessmentDate: [],
  assessor: '',
});

// 统计数据
const statistics = reactive({
  totalContracts: 156,
  excellentContracts: 87,
  riskContracts: 12,
  averageScore: 8.6
});

// 评估类型选项
const assessmentTypeOptions = [
  { label: '质量评估', value: 'quality' },
  { label: '进度评估', value: 'progress' },
  { label: '成本评估', value: 'cost' },
  { label: '合规评估', value: 'compliance' },
  { label: '综合评估', value: 'comprehensive' },
];

// 评估结果选项
const resultOptions = [
  { label: '优秀', value: 'excellent' },
  { label: '良好', value: 'good' },
  { label: '一般', value: 'average' },
  { label: '较差', value: 'poor' },
  { label: '风险', value: 'risk' },
];

// 表格列定义
const columns = [
  {
    title: '评估编号',
    dataIndex: 'id',
    key: 'id',
    width: 150,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '评估类型',
    dataIndex: 'assessmentType',
    key: 'assessmentType',
    width: 100,
  },
  {
    title: '评估日期',
    dataIndex: 'assessmentDate',
    key: 'assessmentDate',
    width: 120,
    sorter: true,
  },
  {
    title: '评估人',
    dataIndex: 'assessor',
    key: 'assessor',
    width: 100,
  },
  {
    title: '评分',
    dataIndex: 'score',
    key: 'score',
    width: 150,
    sorter: true,
  },
  {
    title: '评估结果',
    dataIndex: 'result',
    key: 'result',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right',
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 获取评估类型文本
const getAssessmentTypeText = (type: string): string => {
  const found = assessmentTypeOptions.find(item => item.value === type);
  return found ? found.label : type;
};

// 获取评估结果文本
const getResultText = (result: string): string => {
  const found = resultOptions.find(item => item.value === result);
  return found ? found.label : result;
};

// 获取评估结果颜色
const getResultColor = (result: string): string => {
  const colorMap: Record<string, string> = {
    excellent: 'green',
    good: 'cyan',
    average: 'blue',
    poor: 'orange',
    risk: 'red',
  };
  return colorMap[result] || 'default';
};

// 获取评分状态
const getScoreStatus = (score: number): string => {
  if (score >= 9) return 'success';
  if (score >= 7) return 'normal';
  if (score >= 5) return 'active';
  return 'exception';
};

// 获取数据列表
const fetchDataList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    performanceList.value = mockData.records;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取数据失败');
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const records = [];
  const results = resultOptions.map(r => r.value);
  const assessmentTypes = assessmentTypeOptions.map(t => t.value);
  const total = 68;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const assessmentDate = new Date(now.getTime() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000);
    const result = results[Math.floor(Math.random() * results.length)];
    
    let score;
    switch (result) {
      case 'excellent': score = Math.floor(Math.random() * 10) / 10 + 9; break;
      case 'good': score = Math.floor(Math.random() * 20) / 10 + 7; break;
      case 'average': score = Math.floor(Math.random() * 20) / 10 + 5; break;
      case 'poor': score = Math.floor(Math.random() * 20) / 10 + 3; break;
      case 'risk': score = Math.floor(Math.random() * 30) / 10 + 0; break;
      default: score = 5;
    }
    
    records.push({
      id: `PF${new Date().getFullYear()}${String(i).padStart(4, '0')}`,
      contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      assessmentType: assessmentTypes[Math.floor(Math.random() * assessmentTypes.length)],
      assessmentDate: formatDate(assessmentDate),
      assessor: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '红', '强', '伟', '磊'][Math.floor(Math.random() * 5)]}`,
      score: Number(score.toFixed(1)),
      result: result,
      remark: getRandomRemark(result),
      attachments: Math.random() > 0.5 ? ['评估报告.pdf', '履约证明.docx'] : undefined
    });
  }

  return {
    records,
    total
  };
};

// 获取随机备注
const getRandomRemark = (result: string): string => {
  const remarks = {
    excellent: [
      '合同履约情况优秀，各项指标均超过预期',
      '各项任务按时高质量完成，客户满意度极高',
      '履约过程规范，团队执行力强，值得表扬'
    ],
    good: [
      '合同履约情况良好，基本完成预期目标',
      '任务按时完成，质量符合标准，客户评价良好',
      '执行过程有序，少量细节需要完善'
    ],
    average: [
      '合同履约情况一般，部分指标未达预期',
      '任务完成及时性有待提高，质量基本符合要求',
      '存在一些执行偏差，需要加强监督'
    ],
    poor: [
      '合同履约情况较差，多项指标未达预期',
      '进度严重滞后，质量问题较多，客户有投诉',
      '执行过程混乱，团队配合不足'
    ],
    risk: [
      '存在重大履约风险，可能导致合同违约',
      '多项关键指标严重偏离，客户强烈不满',
      '需立即干预，否则将造成重大损失'
    ]
  };
  
  const remarkList = remarks[result as keyof typeof remarks] || remarks.average;
  return remarkList[Math.floor(Math.random() * remarkList.length)];
};

// 格式化日期
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchDataList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchDataList();
};

// 表格变更处理
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchDataList();
};

// 查看合同
const viewContract = (record: PerformanceRecord) => {
  router.push(`/contracts/detail/${record.contractId}`);
};

// 查看详情
const viewDetail = (record: PerformanceRecord) => {
  message.info(`查看评估详情：${record.id}`);
  // 实际项目中应该跳转到详情页面或打开详情弹窗
};

// 编辑记录
const editRecord = (record: PerformanceRecord) => {
  message.info(`编辑评估记录：${record.id}`);
  // 实际项目中应该跳转到编辑页面或打开编辑弹窗
};

// 下载附件
const downloadAttachments = (record: PerformanceRecord) => {
  if (record.attachments && record.attachments.length > 0) {
    message.success(`附件下载成功: ${record.attachments.join(', ')}`);
  } else {
    message.warning('该评估记录无附件');
  }
  // 实际项目中应该触发文件下载
};

// 生成报告
const generateReport = (record: PerformanceRecord) => {
  message.success(`已生成评估报告：${record.contractName}_履约评估报告.pdf`);
  // 实际项目中应该生成并下载报告
};

// 删除记录
const deleteRecord = (record: PerformanceRecord) => {
  Modal.confirm({
    title: '确认删除',
    content: `确认要删除评估记录"${record.id}"吗？`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      // 模拟删除操作
      const index = performanceList.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        performanceList.value.splice(index, 1);
        pagination.total -= 1;
      }
      message.success('删除成功');
    }
  });
};

// 创建评估记录
const createPerformanceRecord = () => {
  message.info('新增评估记录');
  // 实际项目中应该跳转到新增评估记录页面或打开创建弹窗
};

// 导出数据
const handleExport = () => {
  message.success('评估记录导出成功');
  // 实际项目中应该触发Excel导出
};

onMounted(() => {
  fetchDataList();
});
</script>

<style scoped>
.performance-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.table-view {
  margin-bottom: 16px;
}
</style> 