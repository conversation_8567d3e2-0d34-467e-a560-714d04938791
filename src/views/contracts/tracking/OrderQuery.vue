<template>
  <div class="order-query-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">订单关联查询</h2>
          <div class="action-buttons">
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="订单编号" name="orderNo">
                <a-input v-model:value="searchForm.orderNo" placeholder="请输入订单编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="订单状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择订单状态" allowClear>
                  <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="下单日期" name="orderDate">
                <a-range-picker v-model:value="searchForm.orderDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="客户名称" name="customerName">
                <a-input v-model:value="searchForm.customerName" placeholder="请输入客户名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="订单总数" :value="statistics.total" :precision="0" />
              <template #extra>
                <shopping-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="本月新增" :value="statistics.newThisMonth" :precision="0" />
              <template #extra>
                <rise-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已完成" :value="statistics.completed" :precision="0" />
              <template #extra>
                <check-circle-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="订单金额" :value="statistics.totalAmount" :precision="2" prefix="¥" />
              <template #extra>
                <dollar-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <div class="table-view">
        <a-table
          :columns="columns"
          :data-source="orderList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
        >
          <!-- 自定义列内容 -->
          <template #bodyCell="{ column, record }">
            <!-- 合同名称列 -->
            <template v-if="column.dataIndex === 'contractName'">
              <a @click="viewContract(record)">{{ record.contractName }}</a>
            </template>
            
            <!-- 订单编号列 -->
            <template v-if="column.dataIndex === 'orderNo'">
              <a @click="viewOrder(record)">{{ record.orderNo }}</a>
            </template>
            
            <!-- 订单状态列 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <!-- 金额列 -->
            <template v-if="column.dataIndex === 'amount'">
              ¥{{ record.amount.toLocaleString() }}
            </template>
            
            <!-- 操作列 -->
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewOrderDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="viewInvoiceInfo(record)">发票信息</a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewDeliveryInfo(record)">查看物流</a-menu-item>
                      <a-menu-item @click="downloadOrderDoc(record)">下载订单</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Statistic } from 'ant-design-vue';
import { 
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
  ShoppingOutlined,
  RiseOutlined,
  CheckCircleOutlined,
  DollarOutlined
} from '@ant-design/icons-vue';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface Order {
  id: string;
  orderNo: string;
  contractId: string;
  contractName: string;
  contractCode: string;
  customerName: string;
  status: string;
  orderDate: string;
  deliveryDate: string;
  amount: number;
  products: { id: string; name: string; quantity: number; price: number }[];
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const orderList = ref<Order[]>([]);

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  orderNo: '',
  status: undefined,
  orderDate: [],
  customerName: '',
});

// 统计数据
const statistics = reactive({
  total: 235,
  newThisMonth: 45,
  completed: 189,
  totalAmount: 3674520.50
});

// 状态选项
const statusOptions = [
  { label: '待确认', value: 'pending' },
  { label: '待发货', value: 'confirmed' },
  { label: '已发货', value: 'shipped' },
  { label: '已送达', value: 'delivered' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
];

// 表格列定义
const columns = [
  {
    title: '订单编号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    width: 150,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    ellipsis: true,
  },
  {
    title: '订单金额',
    dataIndex: 'amount',
    key: 'amount',
    align: 'right',
    width: 120,
    sorter: true,
  },
  {
    title: '下单日期',
    dataIndex: 'orderDate',
    key: 'orderDate',
    width: 120,
    sorter: true,
  },
  {
    title: '交付日期',
    dataIndex: 'deliveryDate',
    key: 'deliveryDate',
    width: 120,
    sorter: true,
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right',
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 获取状态文本
const getStatusText = (status: string): string => {
  const found = statusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    pending: 'blue',
    confirmed: 'purple',
    shipped: 'orange',
    delivered: 'volcano',
    completed: 'green',
    cancelled: 'default',
  };
  return colorMap[status] || 'default';
};

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    orderList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取订单列表失败');
    console.error('获取订单列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const statuses = statusOptions.map(s => s.value);
  const total = 235;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const orderDate = new Date(now.getTime() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000);
    const deliveryDate = new Date(orderDate.getTime() + Math.floor(Math.random() * 30 + 5) * 24 * 60 * 60 * 1000);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const amount = Math.floor(Math.random() * 100000 + 5000);
    
    items.push({
      id: `O2023${String(i).padStart(6, '0')}`,
      orderNo: `OR${new Date().getFullYear()}${String(i).padStart(8, '0')}`,
      contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      customerName: `${['上海', '北京', '广州', '深圳'][Math.floor(Math.random() * 4)]}${['科技', '贸易', '实业', '集团'][Math.floor(Math.random() * 4)]}有限公司`,
      status: status,
      orderDate: formatDate(orderDate),
      deliveryDate: formatDate(deliveryDate),
      amount: amount,
      products: [
        { id: `P${i}001`, name: `产品${i}-1`, quantity: Math.floor(Math.random() * 10) + 1, price: Math.floor(Math.random() * 1000) + 100 },
        { id: `P${i}002`, name: `产品${i}-2`, quantity: Math.floor(Math.random() * 5) + 1, price: Math.floor(Math.random() * 2000) + 500 }
      ]
    });
  }

  return {
    items,
    total
  };
};

// 格式化日期
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchOrderList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchOrderList();
};

// 表格变更处理
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchOrderList();
};

// 查看合同
const viewContract = (record: Order) => {
  router.push(`/contracts/detail/${record.contractId}`);
};

// 查看订单
const viewOrder = (record: Order) => {
  message.info(`查看订单：${record.orderNo}`);
  // 实际项目中应该跳转到订单详情页面
};

// 查看订单详情
const viewOrderDetail = (record: Order) => {
  message.info(`查看订单详情：${record.orderNo}`);
  // 实际项目中应该跳转到订单详情页面或打开详情弹窗
};

// 查看发票信息
const viewInvoiceInfo = (record: Order) => {
  message.info(`查看发票信息：${record.orderNo}`);
  // 实际项目中应该跳转到发票信息页面或打开发票信息弹窗
};

// 查看物流信息
const viewDeliveryInfo = (record: Order) => {
  message.info(`查看物流信息：${record.orderNo}`);
  // 实际项目中应该跳转到物流信息页面或打开物流信息弹窗
};

// 下载订单文档
const downloadOrderDoc = (record: Order) => {
  message.success(`订单${record.orderNo}下载成功`);
  // 实际项目中应该触发文件下载
};

// 导出数据
const handleExport = () => {
  message.success('订单数据导出成功');
  // 实际项目中应该触发Excel导出
};

onMounted(() => {
  fetchOrderList();
});
</script>

<style scoped>
.order-query-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.table-view {
  margin-bottom: 16px;
}
</style> 