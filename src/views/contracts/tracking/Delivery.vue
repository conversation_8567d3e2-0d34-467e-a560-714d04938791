<template>
  <div class="delivery-tracking-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">交付情况跟踪</h2>
          <div class="action-buttons">
            <a-button type="primary" @click="createDeliveryRecord">
              <template #icon><plus-outlined /></template>
              新增交付记录
            </a-button>
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="交付项目" name="deliveryItem">
                <a-input v-model:value="searchForm.deliveryItem" placeholder="请输入交付项目" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="交付状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择交付状态" allowClear>
                  <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="交付日期" name="deliveryDate">
                <a-range-picker v-model:value="searchForm.deliveryDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="负责人" name="manager">
                <a-input v-model:value="searchForm.manager" placeholder="请输入负责人" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="进行中交付" :value="statistics.ongoing" :precision="0" />
              <template #extra>
                <loading-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="本月完成" :value="statistics.completedThisMonth" :precision="0" />
              <template #extra>
                <check-circle-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="逾期交付" :value="statistics.delayed" :precision="0" />
              <template #extra>
                <exclamation-circle-outlined style="color: #ff4d4f; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="交付满意度" :value="statistics.satisfaction" :precision="1" suffix="%" />
              <template #extra>
                <like-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <div class="table-view">
        <a-table
          :columns="columns"
          :data-source="deliveryList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
        >
          <!-- 自定义列内容 -->
          <template #bodyCell="{ column, record }">
            <!-- 合同名称列 -->
            <template v-if="column.dataIndex === 'contractName'">
              <a @click="viewContract(record)">{{ record.contractName }}</a>
            </template>
            
            <!-- 交付状态列 -->
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <!-- 进度列 -->
            <template v-if="column.dataIndex === 'progress'">
              <a-progress :percent="record.progress" size="small" />
            </template>
            
            <!-- 操作列 -->
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDeliveryRecord(record)">查看</a-button>
                <a-button v-if="record.status !== 'completed'" type="link" size="small" @click="editDeliveryRecord(record)">更新</a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-if="record.status === 'ongoing'" @click="markAsCompleted(record)">标记为已完成</a-menu-item>
                      <a-menu-item @click="downloadAttachments(record)">下载附件</a-menu-item>
                      <a-menu-item v-if="record.status !== 'completed'" @click="deleteDeliveryRecord(record)">删除</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 完成确认对话框 -->
    <a-modal
      v-model:visible="completeModalVisible"
      title="完成确认"
      @ok="confirmComplete"
      okText="确认"
      cancelText="取消"
    >
      <p>确认将交付项目"{{ selectedRecord?.deliveryItem }}"标记为已完成吗？</p>
      <a-form layout="vertical">
        <a-form-item label="完成日期" required>
          <a-date-picker v-model:value="completeDate" style="width: 100%" :disabledDate="disabledDate" />
        </a-form-item>
        <a-form-item label="满意度评分">
          <a-rate v-model:value="satisfactionRate" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea v-model:value="completeRemark" :rows="3" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import { 
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DownOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LikeOutlined
} from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface DeliveryRecord {
  id: string;
  contractId: string;
  contractName: string;
  contractCode: string;
  deliveryItem: string;
  description: string;
  plannedDate: string;
  actualDate: string | null;
  status: string;
  progress: number;
  manager: string;
  attachments: { id: string; name: string; url: string }[];
  satisfactionRate: number | null;
  remark: string;
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const deliveryList = ref<DeliveryRecord[]>([]);
const selectedRecord = ref<DeliveryRecord | null>(null);
const completeModalVisible = ref(false);
const completeDate = ref<Dayjs>(dayjs());
const satisfactionRate = ref(5);
const completeRemark = ref('');

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  deliveryItem: '',
  status: undefined,
  deliveryDate: [],
  manager: '',
});

// 统计数据
const statistics = reactive({
  ongoing: 32,
  completedThisMonth: 15,
  delayed: 8,
  satisfaction: 94.7
});

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'pending' },
  { label: '进行中', value: 'ongoing' },
  { label: '已完成', value: 'completed' },
  { label: '已延期', value: 'delayed' },
  { label: '已取消', value: 'cancelled' },
];

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '交付项目',
    dataIndex: 'deliveryItem',
    key: 'deliveryItem',
    ellipsis: true,
  },
  {
    title: '计划交付日期',
    dataIndex: 'plannedDate',
    key: 'plannedDate',
    width: 120,
    sorter: true,
  },
  {
    title: '实际交付日期',
    dataIndex: 'actualDate',
    key: 'actualDate',
    width: 120,
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 150,
  },
  {
    title: '交付状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '负责人',
    dataIndex: 'manager',
    key: 'manager',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 获取状态文本
const getStatusText = (status: string): string => {
  const found = statusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    pending: 'blue',
    ongoing: 'processing',
    completed: 'success',
    delayed: 'error',
    cancelled: 'default',
  };
  return colorMap[status] || 'default';
};

// 禁用日期：不能选择未来日期
const disabledDate = (current: dayjs.Dayjs) => {
  return current && current > dayjs().endOf('day');
};

// 获取交付记录列表
const fetchDeliveryList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    deliveryList.value = mockData.items;
    pagination.total = mockData.total;
  } catch (error) {
    message.error('获取交付记录列表失败');
    console.error('获取交付记录列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const items = [];
  const statuses = statusOptions.map(s => s.value);
  const total = 85;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const plannedDate = new Date(now.getTime() + (Math.floor(Math.random() * 60) - 30) * 24 * 60 * 60 * 1000);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    const progress = status === 'completed' ? 100 : 
                    status === 'cancelled' ? 0 : 
                    Math.floor(Math.random() * 100);
                    
    const actualDate = status === 'completed' ? 
                      new Date(plannedDate.getTime() + Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000) : 
                      null;
    
    items.push({
      id: `D2023${String(i).padStart(4, '0')}`,
      contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      deliveryItem: `${['软件', '硬件', '服务', '设备', '文档'][Math.floor(Math.random() * 5)]}交付项${i}`,
      description: `${['软件', '硬件', '服务', '设备', '文档'][Math.floor(Math.random() * 5)]}交付详细说明...`,
      plannedDate: formatDate(plannedDate),
      actualDate: actualDate ? formatDate(actualDate) : null,
      status: status,
      progress: progress,
      manager: `${['张', '李', '王', '赵', '陈'][Math.floor(Math.random() * 5)]}${['明', '伟', '芳', '磊', '丽'][Math.floor(Math.random() * 5)]}`,
      attachments: [
        { id: `A${i}001`, name: '交付说明文档.docx', url: '#' },
        { id: `A${i}002`, name: '验收报告.pdf', url: '#' }
      ],
      satisfactionRate: status === 'completed' ? Math.floor(Math.random() * 3) + 3 : null,
      remark: status === 'completed' ? '按期交付，客户满意' : ''
    });
  }

  return {
    items,
    total
  };
};

// 格式化日期
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchDeliveryList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchDeliveryList();
};

// 表格变更处理
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchDeliveryList();
};

// 查看合同
const viewContract = (record: DeliveryRecord) => {
  router.push(`/contracts/detail/${record.contractId}`);
};

// 查看交付记录
const viewDeliveryRecord = (record: DeliveryRecord) => {
  message.info(`查看交付记录：${record.deliveryItem}`);
  // 实际项目中应该跳转到交付记录详情页面
};

// 编辑交付记录
const editDeliveryRecord = (record: DeliveryRecord) => {
  message.info(`编辑交付记录：${record.deliveryItem}`);
  // 实际项目中应该跳转到交付记录编辑页面
};

// 新增交付记录
const createDeliveryRecord = () => {
  message.info('新增交付记录');
  // 实际项目中应该跳转到新增交付记录页面
};

// 标记为已完成
const markAsCompleted = (record: DeliveryRecord) => {
  selectedRecord.value = record;
  completeDate.value = dayjs();
  satisfactionRate.value = 5;
  completeRemark.value = '';
  completeModalVisible.value = true;
};

// 确认完成交付
const confirmComplete = () => {
  if (selectedRecord.value && completeDate.value) {
    const index = deliveryList.value.findIndex(item => item.id === selectedRecord.value?.id);
    if (index !== -1) {
      deliveryList.value[index].status = 'completed';
      deliveryList.value[index].actualDate = completeDate.value.format('YYYY-MM-DD');
      deliveryList.value[index].progress = 100;
      deliveryList.value[index].satisfactionRate = satisfactionRate.value;
      deliveryList.value[index].remark = completeRemark.value;
    }
    message.success('交付记录已标记为完成');
    completeModalVisible.value = false;
  }
};

// 下载附件
const downloadAttachments = (record: DeliveryRecord) => {
  message.success(`${record.deliveryItem}的附件下载成功`);
};

// 删除交付记录
const deleteDeliveryRecord = (record: DeliveryRecord) => {
  Modal.confirm({
    title: '确认删除',
    content: `确认要删除"${record.deliveryItem}"的交付记录吗？`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      // 模拟删除操作
      const index = deliveryList.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        deliveryList.value.splice(index, 1);
        pagination.total -= 1;
      }
      message.success('删除成功');
    }
  });
};

// 导出数据
const handleExport = () => {
  message.success('交付记录数据导出成功');
};

onMounted(() => {
  fetchDeliveryList();
});
</script>

<style scoped>
.delivery-tracking-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.table-view {
  margin-bottom: 16px;
}
</style> 