<template>
  <div class="payment-page">
    <a-card :bordered="false" class="content-card">
      <!-- 页头部分 -->
      <div class="page-header">
        <div class="title-section">
          <h2 class="page-title">收款与发票管理</h2>
          <div class="action-buttons">
            <a-button type="primary" @click="createPaymentRecord">
              <template #icon><plus-outlined /></template>
              新增收款记录
            </a-button>
            <a-button @click="handleExport">
              <template #icon><export-outlined /></template>
              导出数据
            </a-button>
          </div>
        </div>
      </div>

      <!-- 搜索筛选部分 -->
      <a-card class="search-card" :bordered="false">
        <a-form layout="horizontal" :model="searchForm" ref="searchFormRef">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="合同名称" name="contractName">
                <a-input v-model:value="searchForm.contractName" placeholder="请输入合同名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="合同编号" name="contractCode">
                <a-input v-model:value="searchForm.contractCode" placeholder="请输入合同编号" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="收款类型" name="paymentType">
                <a-select v-model:value="searchForm.paymentType" placeholder="请选择收款类型" allowClear>
                  <a-select-option v-for="item in paymentTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="收款状态" name="status">
                <a-select v-model:value="searchForm.status" placeholder="请选择收款状态" allowClear>
                  <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="收款日期" name="paymentDate">
                <a-range-picker v-model:value="searchForm.paymentDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="发票状态" name="invoiceStatus">
                <a-select v-model:value="searchForm.invoiceStatus" placeholder="请选择发票状态" allowClear>
                  <a-select-option v-for="item in invoiceStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12" class="search-buttons">
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 统计卡片 -->
      <div class="statistics-cards">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="合同总金额" :value="statistics.totalAmount" :precision="2" prefix="¥" />
              <template #extra>
                <dollar-outlined style="color: #1890ff; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="已收款金额" :value="statistics.receivedAmount" :precision="2" prefix="¥" />
              <template #extra>
                <bank-outlined style="color: #52c41a; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="待收款金额" :value="statistics.pendingAmount" :precision="2" prefix="¥" />
              <template #extra>
                <wallet-outlined style="color: #fa8c16; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="statistic-card">
              <statistic title="收款比例" :value="statistics.receivedPercent" :precision="1" suffix="%" />
              <template #extra>
                <pie-chart-outlined style="color: #722ed1; font-size: 32px" />
              </template>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 表格视图 -->
      <div class="table-view">
        <a-tabs v-model:activeKey="activeTabKey">
          <a-tab-pane key="payment" tab="收款记录">
            <a-table
              :columns="paymentColumns"
              :data-source="paymentList"
              :loading="loading"
              :pagination="pagination"
              @change="handleTableChange"
              rowKey="id"
            >
              <!-- 自定义列内容 -->
              <template #bodyCell="{ column, record }">
                <!-- 合同名称列 -->
                <template v-if="column.dataIndex === 'contractName'">
                  <a @click="viewContract(record)">{{ record.contractName }}</a>
                </template>
                
                <!-- 收款状态列 -->
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>
                
                <!-- 金额列 -->
                <template v-if="column.dataIndex === 'amount'">
                  ¥{{ record.amount.toLocaleString() }}
                </template>
                
                <!-- 操作列 -->
                <template v-if="column.dataIndex === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewPaymentDetail(record)">详情</a-button>
                    <a-button v-if="record.status === 'pending'" type="link" size="small" @click="confirmPayment(record)">确认收款</a-button>
                    <a-dropdown>
                      <a-button type="link" size="small">
                        更多 <down-outlined />
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="issueInvoice(record)">开具发票</a-menu-item>
                          <a-menu-item @click="uploadReceipt(record)">上传回单</a-menu-item>
                          <a-menu-item v-if="record.status === 'pending'" @click="deletePayment(record)">删除</a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="invoice" tab="发票管理">
            <a-table
              :columns="invoiceColumns"
              :data-source="invoiceList"
              :loading="loading"
              :pagination="pagination"
              @change="handleTableChange"
              rowKey="id"
            >
              <!-- 自定义列内容 -->
              <template #bodyCell="{ column, record }">
                <!-- 发票状态列 -->
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="getInvoiceStatusColor(record.status)">
                    {{ getInvoiceStatusText(record.status) }}
                  </a-tag>
                </template>
                
                <!-- 金额列 -->
                <template v-if="column.dataIndex === 'amount'">
                  ¥{{ record.amount.toLocaleString() }}
                </template>
                
                <!-- 操作列 -->
                <template v-if="column.dataIndex === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewInvoiceDetail(record)">详情</a-button>
                    <a-button v-if="record.status === 'waiting'" type="link" size="small" @click="confirmInvoice(record)">确认开票</a-button>
                    <a-dropdown>
                      <a-button type="link" size="small">
                        更多 <down-outlined />
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="downloadInvoice(record)">下载发票</a-menu-item>
                          <a-menu-item @click="sendInvoiceEmail(record)">发送邮件</a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>

    <!-- 确认收款对话框 -->
    <a-modal
      v-model:visible="confirmPaymentVisible"
      title="确认收款"
      @ok="handleConfirmPayment"
      okText="确认"
      cancelText="取消"
    >
      <p>确认已收到以下款项吗？</p>
      <a-descriptions bordered>
        <a-descriptions-item label="合同" :span="3">{{ selectedPayment?.contractName }}</a-descriptions-item>
        <a-descriptions-item label="收款金额" :span="3">¥{{ selectedPayment?.amount.toLocaleString() }}</a-descriptions-item>
        <a-descriptions-item label="付款方" :span="3">{{ selectedPayment?.payerName }}</a-descriptions-item>
        <a-descriptions-item label="收款方式" :span="3">{{ getPaymentTypeText(selectedPayment?.paymentType || '') }}</a-descriptions-item>
      </a-descriptions>
      <a-form layout="vertical" style="margin-top: 16px;">
        <a-form-item label="实际收款日期" required>
          <a-date-picker v-model:value="actualPaymentDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea v-model:value="paymentRemark" :rows="3" placeholder="请输入备注信息" />
        </a-form-item>
        <a-form-item label="上传回单">
          <a-upload
            v-model:file-list="uploadedFiles"
            :action="uploadUrl"
            :before-upload="beforeUpload"
          >
            <a-button>
              <template #icon><upload-outlined /></template>
              上传文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal, Statistic } from 'ant-design-vue';
import { 
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DownOutlined,
  DollarOutlined,
  BankOutlined,
  WalletOutlined,
  PieChartOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

// 类型定义
interface FormInstance {
  resetFields: () => void;
}

interface Payment {
  id: string;
  contractId: string;
  contractName: string;
  contractCode: string;
  paymentNo: string;
  phase: string;
  amount: number;
  planDate: string;
  actualDate: string | null;
  paymentType: string;
  payerName: string;
  status: string;
  remark: string;
  receiptUrl: string | null;
}

interface Invoice {
  id: string;
  contractId: string;
  contractName: string;
  invoiceNo: string;
  invoiceType: string;
  amount: number;
  taxRate: number;
  taxAmount: number;
  issueDate: string | null;
  buyerName: string;
  buyerTaxNo: string;
  sellerName: string;
  sellerTaxNo: string;
  status: string;
  pdfUrl: string | null;
}

const router = useRouter();
const loading = ref(false);
const searchFormRef = ref<FormInstance | null>(null);
const paymentList = ref<Payment[]>([]);
const invoiceList = ref<Invoice[]>([]);
const activeTabKey = ref('payment');
const confirmPaymentVisible = ref(false);
const selectedPayment = ref<Payment | null>(null);
const actualPaymentDate = ref<Dayjs>(dayjs());
const paymentRemark = ref('');
const uploadedFiles = ref<any[]>([]);

// 搜索表单
const searchForm = reactive({
  contractName: '',
  contractCode: '',
  paymentType: undefined,
  status: undefined,
  paymentDate: [],
  invoiceStatus: undefined,
});

// 统计数据
const statistics = reactive({
  totalAmount: 5268420.50,
  receivedAmount: 3647590.30,
  pendingAmount: 1620830.20,
  receivedPercent: 69.2
});

// 收款类型选项
const paymentTypeOptions = [
  { label: '预付款', value: 'prepayment' },
  { label: '进度款', value: 'progress' },
  { label: '尾款', value: 'final' },
  { label: '质保金', value: 'warranty' },
  { label: '其他', value: 'other' },
];

// 收款状态选项
const statusOptions = [
  { label: '未收款', value: 'pending' },
  { label: '已收款', value: 'received' },
  { label: '部分收款', value: 'partial' },
  { label: '逾期未收', value: 'overdue' },
];

// 发票状态选项
const invoiceStatusOptions = [
  { label: '待开票', value: 'waiting' },
  { label: '已开票', value: 'issued' },
  { label: '已作废', value: 'cancelled' },
];

// 上传配置
const uploadUrl = '/api/upload';

// 上传前检查
const beforeUpload = (file: File) => {
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过5MB!');
  }
  return isLt5M || false;
};

// 收款记录表格列定义
const paymentColumns = [
  {
    title: '收款编号',
    dataIndex: 'paymentNo',
    key: 'paymentNo',
    width: 150,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    key: 'contractCode',
    width: 150,
  },
  {
    title: '收款阶段',
    dataIndex: 'phase',
    key: 'phase',
    width: 100,
  },
  {
    title: '收款金额',
    dataIndex: 'amount',
    key: 'amount',
    align: 'right',
    width: 120,
    sorter: true,
  },
  {
    title: '计划收款日期',
    dataIndex: 'planDate',
    key: 'planDate',
    width: 120,
    sorter: true,
  },
  {
    title: '实际收款日期',
    dataIndex: 'actualDate',
    key: 'actualDate',
    width: 120,
  },
  {
    title: '收款方式',
    dataIndex: 'paymentType',
    key: 'paymentType',
    width: 100,
  },
  {
    title: '收款状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right',
  }
];

// 发票表格列定义
const invoiceColumns = [
  {
    title: '发票编号',
    dataIndex: 'invoiceNo',
    key: 'invoiceNo',
    width: 180,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    key: 'invoiceType',
    width: 120,
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    align: 'right',
    width: 120,
    sorter: true,
  },
  {
    title: '税率',
    dataIndex: 'taxRate',
    key: 'taxRate',
    align: 'right',
    width: 80,
    render: (text: number) => `${text}%`,
  },
  {
    title: '开票日期',
    dataIndex: 'issueDate',
    key: 'issueDate',
    width: 120,
    sorter: true,
  },
  {
    title: '购买方',
    dataIndex: 'buyerName',
    key: 'buyerName',
    ellipsis: true,
    width: 180,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right',
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 获取收款类型文本
const getPaymentTypeText = (type: string): string => {
  const found = paymentTypeOptions.find(item => item.value === type);
  return found ? found.label : type;
};

// 获取收款状态文本
const getStatusText = (status: string): string => {
  const found = statusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取发票状态文本
const getInvoiceStatusText = (status: string): string => {
  const found = invoiceStatusOptions.find(item => item.value === status);
  return found ? found.label : status;
};

// 获取收款状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    pending: 'blue',
    received: 'green',
    partial: 'orange',
    overdue: 'red',
  };
  return colorMap[status] || 'default';
};

// 获取发票状态颜色
const getInvoiceStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    waiting: 'blue',
    issued: 'green',
    cancelled: 'red',
  };
  return colorMap[status] || 'default';
};

// 获取数据列表
const fetchDataList = async () => {
  loading.value = true;
  try {
    // 模拟数据，实际项目中应该调用API
    const mockData = generateMockData();
    if (activeTabKey.value === 'payment') {
      paymentList.value = mockData.payments;
      pagination.total = mockData.paymentTotal;
    } else {
      invoiceList.value = mockData.invoices;
      pagination.total = mockData.invoiceTotal;
    }
  } catch (error) {
    message.error('获取数据失败');
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = () => {
  const payments = [];
  const invoices = [];
  const paymentStatuses = statusOptions.map(s => s.value);
  const invoiceStatuses = invoiceStatusOptions.map(s => s.value);
  const paymentTypes = paymentTypeOptions.map(t => t.value);
  const paymentTotal = 95;
  const invoiceTotal = 78;
  
  for (let i = 1; i <= 10; i++) {
    const now = new Date();
    const planDate = new Date(now.getTime() + (Math.floor(Math.random() * 60) - 30) * 24 * 60 * 60 * 1000);
    const status = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
    const amount = Math.floor(Math.random() * 100000 + 10000);
    
    const actualDate = status === 'received' || status === 'partial' ? 
                       new Date(planDate.getTime() + Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000) : 
                       null;
    
    payments.push({
      id: `P2023${String(i).padStart(6, '0')}`,
      contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
      contractCode: `HT-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
      paymentNo: `SK${new Date().getFullYear()}${String(i).padStart(6, '0')}`,
      phase: `第${i%3 + 1}期`,
      amount: amount,
      planDate: formatDate(planDate),
      actualDate: actualDate ? formatDate(actualDate) : null,
      paymentType: paymentTypes[Math.floor(Math.random() * paymentTypes.length)],
      payerName: `${['上海', '北京', '广州', '深圳'][Math.floor(Math.random() * 4)]}${['科技', '贸易', '实业', '集团'][Math.floor(Math.random() * 4)]}有限公司`,
      status: status,
      remark: status === 'received' ? '按期收款，客户信用良好' : '',
      receiptUrl: status === 'received' ? '#' : null
    });
    
    if (status === 'received' || status === 'partial') {
      const invoiceStatus = invoiceStatuses[Math.floor(Math.random() * invoiceStatuses.length)];
      const taxRate = [3, 6, 9, 13][Math.floor(Math.random() * 4)];
      const taxAmount = amount * (taxRate / 100);
      
      invoices.push({
        id: `I2023${String(i).padStart(6, '0')}`,
        contractId: `C2023${String(Math.floor(Math.random() * 100) + 1).padStart(4, '0')}`,
        contractName: `${['销售', '采购', '服务', '合作'][Math.floor(Math.random() * 4)]}合同-${Math.floor(Math.random() * 1000)}号`,
        invoiceNo: `FP${new Date().getFullYear()}${String(i).padStart(8, '0')}`,
        invoiceType: ['增值税专用发票', '增值税普通发票', '电子发票'][Math.floor(Math.random() * 3)],
        amount: amount,
        taxRate: taxRate,
        taxAmount: taxAmount,
        issueDate: invoiceStatus === 'issued' ? formatDate(actualDate || new Date()) : null,
        buyerName: `${['上海', '北京', '广州', '深圳'][Math.floor(Math.random() * 4)]}${['科技', '贸易', '实业', '集团'][Math.floor(Math.random() * 4)]}有限公司`,
        buyerTaxNo: `91${String(Math.floor(Math.random() * 100000000000000)).padStart(15, '0')}`,
        sellerName: '北京叶彩科技有限公司',
        sellerTaxNo: '91110105MA00385XY4',
        status: invoiceStatus,
        pdfUrl: invoiceStatus === 'issued' ? '#' : null
      });
    }
  }

  return {
    payments,
    invoices,
    paymentTotal,
    invoiceTotal
  };
};

// 格式化日期
const formatDate = (date: Date | null): string => {
  if (!date) return '';
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 查询
const handleSearch = () => {
  pagination.current = 1;
  fetchDataList();
};

// 重置搜索
const resetSearch = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  fetchDataList();
};

// 表格变更处理
const handleTableChange = (
  pag: any, 
  filters: Record<string, string[]>, 
  sorter: { field?: string; order?: string }
) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchDataList();
};

// 查看合同
const viewContract = (record: Payment | Invoice) => {
  router.push(`/contracts/detail/${record.contractId}`);
};

// 查看收款详情
const viewPaymentDetail = (record: Payment) => {
  message.info(`查看收款详情：${record.paymentNo}`);
  // 实际项目中应该跳转到收款详情页面或打开详情弹窗
};

// 确认收款对话框
const confirmPayment = (record: Payment) => {
  selectedPayment.value = record;
  actualPaymentDate.value = dayjs();
  paymentRemark.value = '';
  uploadedFiles.value = [];
  confirmPaymentVisible.value = true;
};

// 处理确认收款
const handleConfirmPayment = () => {
  if (selectedPayment.value) {
    const index = paymentList.value.findIndex(item => item.id === selectedPayment.value?.id);
    if (index !== -1) {
      paymentList.value[index].status = 'received';
      paymentList.value[index].actualDate = actualPaymentDate.value.format('YYYY-MM-DD');
      paymentList.value[index].remark = paymentRemark.value;
      paymentList.value[index].receiptUrl = '#';
    }
    statistics.receivedAmount += selectedPayment.value.amount;
    statistics.pendingAmount -= selectedPayment.value.amount;
    statistics.receivedPercent = Math.round(statistics.receivedAmount / statistics.totalAmount * 1000) / 10;
    
    message.success('收款确认成功');
    confirmPaymentVisible.value = false;
  }
};

// 开具发票
const issueInvoice = (record: Payment) => {
  message.info(`为收款${record.paymentNo}开具发票`);
  // 实际项目中应该跳转到开票页面或打开开票弹窗
};

// 上传回单
const uploadReceipt = (record: Payment) => {
  message.info(`上传收款${record.paymentNo}的回单`);
  // 实际项目中应该打开上传对话框
};

// 删除收款记录
const deletePayment = (record: Payment) => {
  Modal.confirm({
    title: '确认删除',
    content: `确认要删除收款记录"${record.paymentNo}"吗？`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      // 模拟删除操作
      const index = paymentList.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        paymentList.value.splice(index, 1);
        pagination.total -= 1;
      }
      message.success('删除成功');
    }
  });
};

// 查看发票详情
const viewInvoiceDetail = (record: Invoice) => {
  message.info(`查看发票详情：${record.invoiceNo}`);
  // 实际项目中应该跳转到发票详情页面或打开详情弹窗
};

// 确认开票
const confirmInvoice = (record: Invoice) => {
  const index = invoiceList.value.findIndex(item => item.id === record.id);
  if (index !== -1) {
    invoiceList.value[index].status = 'issued';
    invoiceList.value[index].issueDate = formatDate(new Date());
    invoiceList.value[index].pdfUrl = '#';
  }
  message.success(`发票${record.invoiceNo}已开具成功`);
};

// 下载发票
const downloadInvoice = (record: Invoice) => {
  if (record.pdfUrl) {
    message.success(`发票${record.invoiceNo}下载成功`);
  } else {
    message.warning('该发票暂无电子版本');
  }
  // 实际项目中应该打开下载链接
};

// 发送发票邮件
const sendInvoiceEmail = (record: Invoice) => {
  message.success(`发票${record.invoiceNo}发送成功`);
  // 实际项目中应该打开邮件发送对话框
};

// 创建收款记录
const createPaymentRecord = () => {
  message.info('新增收款记录');
  // 实际项目中应该跳转到新增收款记录页面
};

// 导出数据
const handleExport = () => {
  message.success(`${activeTabKey.value === 'payment' ? '收款记录' : '发票记录'}导出成功`);
  // 实际项目中应该触发Excel导出
};

onMounted(() => {
  fetchDataList();
});
</script>

<style scoped>
.payment-page {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  font-size: 20px;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.search-card {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistic-card {
  display: flex;
  justify-content: space-between;
  height: 100%;
}

.table-view {
  margin-bottom: 16px;
}
</style> 