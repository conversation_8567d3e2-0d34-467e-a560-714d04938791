<template>
  <div class="pending-signature-list">
    <a-table
      :columns="columns"
      :data-source="pendingSignatures"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 合同名称列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'contractName'">
          <div>
            <a @click="viewContract(record, 'signature')" class="font-medium">{{ record.contractName }}</a>
            <div class="text-xs text-gray-500">合同编号: {{ record.contractCode }}</div>
          </div>
        </template>
        
        <!-- 合同类型列 -->
        <template v-else-if="column.dataIndex === 'contractType'">
          <a-tag>{{ getContractTypeName(record.contractType) }}</a-tag>
        </template>
        
        <!-- 签署状态列 -->
        <template v-else-if="column.dataIndex === 'signatureStatus'">
          <a-tag :color="getSignatureStatusColor(record.signatureStatus)">
            {{ getSignatureStatusName(record.signatureStatus) }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.dataIndex === 'action'">
          <a-button type="primary" size="small" @click="viewContract(record, 'signature')">
            签署
          </a-button>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits } from 'vue';
import { useContractStore, ContractType, ContractStatus, SignatureStatus } from '@/stores/contract';

const emit = defineEmits<{
  (e: 'view', type: 'approval' | 'signature', id: string): void
}>();

const contractStore = useContractStore();
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 待签署合同列表
const pendingSignatures = ref<any[]>([]);

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    key: 'contractType',
    width: 120,
  },
  {
    title: '当前状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    render: (status: ContractStatus) => {
      const statusMap = {
        [ContractStatus.APPROVED]: '已审批',
        [ContractStatus.SIGNED]: '部分签署',
        // 其他状态...
      };
      return statusMap[status] || '未知状态';
    }
  },
  {
    title: '签署状态',
    dataIndex: 'signatureStatus',
    key: 'signatureStatus',
    width: 130,
  },
  {
    title: '对方单位',
    dataIndex: 'partyB',
    key: 'partyB',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
  }
];

// 获取合同类型名称
const getContractTypeName = (type: ContractType) => {
  const typeMap = {
    [ContractType.SALES]: '销售合同',
    [ContractType.PURCHASE]: '采购合同',
    [ContractType.SERVICE]: '服务合同',
    [ContractType.FRAMEWORK]: '框架协议',
    [ContractType.OTHER]: '其他合同',
  };
  return typeMap[type] || '未知类型';
};

// 获取签署状态名称
const getSignatureStatusName = (status: SignatureStatus) => {
  const statusMap = {
    [SignatureStatus.UNSIGNED]: '未签章',
    [SignatureStatus.PENDING_OUR]: '待我方签章',
    [SignatureStatus.PENDING_OTHER]: '待对方签章',
    [SignatureStatus.SIGNED]: '已签订',
    [SignatureStatus.INVALID]: '已作废',
    [SignatureStatus.ABNORMAL]: '异常',
  };
  return statusMap[status] || '未知状态';
};

// 获取签署状态颜色
const getSignatureStatusColor = (status: SignatureStatus) => {
  const colorMap = {
    [SignatureStatus.UNSIGNED]: 'default',
    [SignatureStatus.PENDING_OUR]: 'warning',
    [SignatureStatus.PENDING_OTHER]: 'processing',
    [SignatureStatus.SIGNED]: 'success',
    [SignatureStatus.INVALID]: 'error',
    [SignatureStatus.ABNORMAL]: 'error',
  };
  return colorMap[status] || 'default';
};

// 加载待签署合同
const fetchPendingSignatures = async () => {
  loading.value = true;
  try {
    const contracts = await contractStore.fetchContracts();
    // 筛选出待我方签署的合同
    pendingSignatures.value = contracts.filter(contract => 
      (contract.status === ContractStatus.APPROVED || contract.status === ContractStatus.SIGNED) && 
      contract.signatureStatus === SignatureStatus.PENDING_OUR
    );
    pagination.total = pendingSignatures.value.length;
  } catch (error) {
    console.error('获取待签署合同失败:', error);
  } finally {
    loading.value = false;
  }
};

// 查看/处理合同
const viewContract = (record: any, type: 'approval' | 'signature') => {
  emit('view', type, record.id);
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

onMounted(() => {
  fetchPendingSignatures();
});
</script>

<style scoped>
.pending-signature-list {
  margin-top: 16px;
}
</style> 