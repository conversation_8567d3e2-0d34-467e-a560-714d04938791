<template>
  <div class="approval-history-list">
    <a-table
      :columns="columns"
      :data-source="approvalHistory"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 合同名称列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'contractName'">
          <div>
            <a @click="viewContract(record)" class="font-medium">{{ record.contractName }}</a>
            <div class="text-xs text-gray-500">合同编号: {{ record.contractCode }}</div>
          </div>
        </template>
        
        <!-- 合同类型列 -->
        <template v-else-if="column.dataIndex === 'contractType'">
          <a-tag>{{ getContractTypeName(record.contractType) }}</a-tag>
        </template>
        
        <!-- 状态列 -->
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusName(record.status) }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.dataIndex === 'action'">
          <a @click="viewContract(record)">查看详情</a>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits } from 'vue';
import { useContractStore, ContractType, ContractStatus } from '@/stores/contract';

const emit = defineEmits<{
  (e: 'view', type: 'approval' | 'signature', id: string): void
}>();

const contractStore = useContractStore();
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 审批历史列表
const approvalHistory = ref<any[]>([]);

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    key: 'contractType',
    width: 120,
  },
  {
    title: '提交人',
    dataIndex: 'createdBy',
    key: 'createdBy',
    width: 100,
  },
  {
    title: '审批时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
    render: (text: string) => new Date(text).toLocaleString(),
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '已审批', value: ContractStatus.APPROVED },
      { text: '已驳回', value: ContractStatus.REJECTED },
    ],
    onFilter: (value: string, record: any) => record.status === value,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
  }
];

// 获取合同类型名称
const getContractTypeName = (type: ContractType) => {
  const typeMap = {
    [ContractType.SALES]: '销售合同',
    [ContractType.PURCHASE]: '采购合同',
    [ContractType.SERVICE]: '服务合同',
    [ContractType.FRAMEWORK]: '框架协议',
    [ContractType.OTHER]: '其他合同',
  };
  return typeMap[type] || '未知类型';
};

// 获取合同状态名称
const getStatusName = (status: ContractStatus) => {
  const statusMap = {
    [ContractStatus.DRAFT]: '草稿',
    [ContractStatus.REVIEWING]: '审核中',
    [ContractStatus.REJECTED]: '已驳回',
    [ContractStatus.APPROVED]: '已审批',
    [ContractStatus.SIGNED]: '已签署',
    [ContractStatus.COMPLETED]: '已完成',
    [ContractStatus.ARCHIVED]: '已归档',
    [ContractStatus.TERMINATED]: '已终止',
  };
  return statusMap[status] || '未知状态';
};

// 获取状态颜色
const getStatusColor = (status: ContractStatus) => {
  const colorMap = {
    [ContractStatus.DRAFT]: 'default',
    [ContractStatus.REVIEWING]: 'processing',
    [ContractStatus.REJECTED]: 'error',
    [ContractStatus.APPROVED]: 'success',
    [ContractStatus.SIGNED]: 'success',
    [ContractStatus.COMPLETED]: 'success',
    [ContractStatus.ARCHIVED]: 'default',
    [ContractStatus.TERMINATED]: 'error',
  };
  return colorMap[status] || 'default';
};

// 加载审批历史
const fetchApprovalHistory = async () => {
  loading.value = true;
  try {
    const contracts = await contractStore.fetchContracts();
    // 筛选出已审批或已驳回的合同
    approvalHistory.value = contracts.filter(contract => 
      contract.status === ContractStatus.APPROVED || 
      contract.status === ContractStatus.REJECTED
    );
    pagination.total = approvalHistory.value.length;
  } catch (error) {
    console.error('获取审批历史失败:', error);
  } finally {
    loading.value = false;
  }
};

// 查看合同详情
const viewContract = (record: any) => {
  emit('view', 'approval', record.id);
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

onMounted(() => {
  fetchApprovalHistory();
});
</script>

<style scoped>
.approval-history-list {
  margin-top: 16px;
}
</style> 