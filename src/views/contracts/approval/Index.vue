<template>
  <div class="contract-approval-page">
    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">合同审批与签署</h2>
          <p class="text-gray-500 mt-1">管理合同审批流程和签署状态</p>
        </div>
      </div>
    </div>

    <!-- 选项卡 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="pending" tab="待我审批">
          <pending-approval-list @view="handleViewContract" />
        </a-tab-pane>
        <a-tab-pane key="signature" tab="待签署">
          <pending-signature-list @view="handleViewContract" />
        </a-tab-pane>
        <a-tab-pane key="history" tab="审批历史">
          <approval-history-list @view="handleViewContract" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import PendingApprovalList from './PendingApprovalList.vue';
import PendingSignatureList from './PendingSignatureList.vue';
import ApprovalHistoryList from './ApprovalHistoryList.vue';

const router = useRouter();
const activeTab = ref<string>('pending');

// 查看合同详情
const handleViewContract = (type: 'approval' | 'signature', id: string) => {
  if (type === 'approval') {
    router.push({
      name: 'contracts-approval-process',
      params: { id }
    });
  } else {
    router.push({
      name: 'contracts-signature',
      params: { id }
    });
  }
};
</script>

<style scoped>
.contract-approval-page {
  background-color: #f0f2f5;
}

.page-header {
  position: relative;
  margin-bottom: 16px;
}

.page-header h2 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

.page-header .desc {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  margin-bottom: 0;
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 0;
  padding: 0 16px;
}

:deep(.ant-tabs-content) {
  padding-top: 16px;
}
</style> 