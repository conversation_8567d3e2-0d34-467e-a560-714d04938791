<template>
  <div class="contract-signature-page">
    <a-card class="card-container">
      <template #title>
        <div class="flex justify-between items-center">
          <h2>合同签署</h2>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
        </div>
      </template>

      <a-spin :spinning="loading">
        <!-- 合同基本信息 -->
        <div v-if="contract" class="mb-6">
          <a-descriptions title="合同基本信息" bordered :column="2">
            <a-descriptions-item label="合同编号">{{ contract.contractCode }}</a-descriptions-item>
            <a-descriptions-item label="合同名称">{{ contract.contractName }}</a-descriptions-item>
            <a-descriptions-item label="合同类型">{{ getContractTypeName(contract.contractType) }}</a-descriptions-item>
            <a-descriptions-item label="合同状态">
              <a-tag :color="getStatusColor(contract.status)">
                {{ getStatusName(contract.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="甲方">{{ contract.partyA }}</a-descriptions-item>
            <a-descriptions-item label="乙方">{{ contract.partyB }}</a-descriptions-item>
            <a-descriptions-item label="合同金额">¥{{ formatNumber(contract.amount) }}</a-descriptions-item>
            <a-descriptions-item label="签署状态">
              <a-tag :color="getSignatureStatusColor(contract.signatureStatus)">
                {{ getSignatureStatusName(contract.signatureStatus) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 合同预览 -->
        <div class="mb-6">
          <a-divider orientation="left">合同内容预览</a-divider>
          <div class="contract-preview" v-html="contract?.content || '加载中...'"></div>
        </div>

        <!-- 电子签名区域 -->
        <div class="signature-area mb-6">
          <a-divider orientation="left">电子签名</a-divider>
          
          <div class="flex justify-center mb-4">
            <a-radio-group v-model:value="signatureType" button-style="solid">
              <a-radio-button value="electronic">电子签名</a-radio-button>
              <a-radio-button value="upload">上传签章</a-radio-button>
            </a-radio-group>
          </div>
          
          <div v-if="signatureType === 'electronic'" class="signature-pad-container">
            <div class="signature-pad" ref="signaturePad"></div>
            <div class="signature-actions">
              <a-button type="primary" @click="saveSignature" :disabled="!hasSignature">
                保存签名
              </a-button>
              <a-button @click="clearSignature">
                清除
              </a-button>
            </div>
          </div>
          
          <div v-else class="upload-signature">
            <a-upload
              list-type="picture-card"
              :file-list="fileList"
              :before-upload="beforeUpload"
              @preview="handlePreview"
              @change="handleChange"
            >
              <div v-if="fileList.length < 1">
                <upload-outlined />
                <div style="margin-top: 8px">上传签章</div>
              </div>
            </a-upload>
            <a-modal
              v-model:visible="previewVisible"
              :title="previewTitle"
              :footer="null"
            >
              <img alt="预览图片" style="width: 100%" :src="previewImage" />
            </a-modal>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submission-actions">
          <a-space>
            <a-button type="primary" @click="submitSignature" :disabled="!canSubmit">
              完成签署
            </a-button>
            <a-button @click="goBack">
              取消
            </a-button>
          </a-space>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { useContractStore, ContractType, ContractStatus, SignatureStatus } from '@/stores/contract';
import type { Contract } from '@/stores/contract';
import SignaturePad from 'signature_pad'; // 注意：这里模拟引入签名库，实际开发需要安装

const route = useRoute();
const router = useRouter();
const contractStore = useContractStore();

const contractId = ref(route.params.id as string);
const contract = ref<Contract | null>(null);
const loading = ref(false);
const signaturePad = ref<any>(null);
const signaturePadElement = ref<HTMLElement | null>(null);
const hasSignature = ref(false);
const signatureType = ref<'electronic' | 'upload'>('electronic');

// 文件上传相关状态
const fileList = ref<any[]>([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 获取合同类型名称
const getContractTypeName = (type: ContractType) => {
  const typeMap = {
    [ContractType.SALES]: '销售合同',
    [ContractType.PURCHASE]: '采购合同',
    [ContractType.SERVICE]: '服务合同',
    [ContractType.FRAMEWORK]: '框架协议',
    [ContractType.OTHER]: '其他合同',
  };
  return typeMap[type] || '未知类型';
};

// 获取合同状态名称
const getStatusName = (status: ContractStatus) => {
  const statusMap = {
    [ContractStatus.DRAFT]: '草稿',
    [ContractStatus.REVIEWING]: '审核中',
    [ContractStatus.REJECTED]: '已驳回',
    [ContractStatus.APPROVED]: '已审批',
    [ContractStatus.SIGNED]: '已签署',
    [ContractStatus.COMPLETED]: '已完成',
    [ContractStatus.ARCHIVED]: '已归档',
    [ContractStatus.TERMINATED]: '已终止',
  };
  return statusMap[status] || '未知状态';
};

// 获取签署状态名称
const getSignatureStatusName = (status: SignatureStatus) => {
  const statusMap = {
    [SignatureStatus.UNSIGNED]: '未签章',
    [SignatureStatus.PENDING_OUR]: '待我方签章',
    [SignatureStatus.PENDING_OTHER]: '待对方签章',
    [SignatureStatus.SIGNED]: '已签订',
    [SignatureStatus.INVALID]: '已作废',
    [SignatureStatus.ABNORMAL]: '异常',
  };
  return statusMap[status] || '未知状态';
};

// 获取状态颜色
const getStatusColor = (status: ContractStatus) => {
  const colorMap = {
    [ContractStatus.DRAFT]: 'default',
    [ContractStatus.REVIEWING]: 'processing',
    [ContractStatus.REJECTED]: 'error',
    [ContractStatus.APPROVED]: 'success',
    [ContractStatus.SIGNED]: 'success',
    [ContractStatus.COMPLETED]: 'success',
    [ContractStatus.ARCHIVED]: 'default',
    [ContractStatus.TERMINATED]: 'error',
  };
  return colorMap[status] || 'default';
};

// 获取签署状态颜色
const getSignatureStatusColor = (status: SignatureStatus) => {
  const colorMap = {
    [SignatureStatus.UNSIGNED]: 'default',
    [SignatureStatus.PENDING_OUR]: 'warning',
    [SignatureStatus.PENDING_OTHER]: 'processing',
    [SignatureStatus.SIGNED]: 'success',
    [SignatureStatus.INVALID]: 'error',
    [SignatureStatus.ABNORMAL]: 'error',
  };
  return colorMap[status] || 'default';
};

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 返回上一页
const goBack = () => {
  router.push({ name: 'contracts-approval' });
};

// 是否可以提交
const canSubmit = computed(() => {
  if (signatureType.value === 'electronic') {
    return hasSignature.value;
  } else {
    return fileList.value.length > 0;
  }
});

// 初始化签名板
const initSignaturePad = () => {
  if (signaturePadElement.value) {
    // 实际开发中，这里应该使用真正的签名库
    signaturePad.value = new SignaturePad(signaturePadElement.value, {
      backgroundColor: 'rgb(255, 255, 255)',
      penColor: 'rgb(0, 0, 0)',
    });
    
    // 监听签名事件
    signaturePad.value.addEventListener('endStroke', () => {
      hasSignature.value = !signaturePad.value.isEmpty();
    });
  }
};

// 清除签名
const clearSignature = () => {
  if (signaturePad.value) {
    signaturePad.value.clear();
    hasSignature.value = false;
  }
};

// 保存签名
const saveSignature = () => {
  if (signaturePad.value && !signaturePad.value.isEmpty()) {
    const signatureData = signaturePad.value.toDataURL('image/png');
    console.log('Signature saved:', signatureData);
    message.success('签名已保存');
  }
};

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.indexOf('image/') === 0;
  if (!isImage) {
    message.error('只能上传图片文件!');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片必须小于2MB!');
  }
  return isImage && isLt2M;
};

// 处理文件变更
const handleChange = ({ fileList: newFileList }: any) => {
  fileList.value = newFileList;
};

// 处理预览
const handlePreview = async (file: any) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj);
  }
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
};

// 转换文件为base64
const getBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// 提交签署
const submitSignature = async () => {
  try {
    loading.value = true;
    
    // 根据签署类型获取签名数据
    let signatureData;
    if (signatureType.value === 'electronic') {
      signatureData = signaturePad.value.toDataURL('image/png');
    } else {
      signatureData = fileList.value[0]?.url || await getBase64(fileList.value[0].originFileObj);
    }
    
    // 模拟上传签名数据
    console.log('Uploading signature:', signatureData);
    
    // 更新合同签署状态
    if (contract.value) {
      await contractStore.signContract(contract.value.id, SignatureStatus.SIGNED);
      message.success('合同签署成功');
      router.push({ name: 'contracts-approval' });
    }
  } catch (error) {
    console.error('签署提交失败:', error);
    message.error('签署提交失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 加载合同详情
const loadContractDetails = async () => {
  loading.value = true;
  try {
    contract.value = await contractStore.fetchContractById(contractId.value);
  } catch (error) {
    console.error('获取合同详情失败:', error);
    message.error('获取合同详情失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadContractDetails();
  // 延迟初始化签名板，确保DOM已经渲染
  setTimeout(() => {
    signaturePadElement.value = document.querySelector('.signature-pad');
    initSignaturePad();
  }, 100);
});

onUnmounted(() => {
  // 清理资源
  if (signaturePad.value) {
    signaturePad.value.off();
  }
});
</script>

<style scoped>
.contract-signature-page {
  padding: 20px;
}

.card-container {
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.contract-preview {
  border: 1px solid #f0f0f0;
  padding: 20px;
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  background-color: #fafafa;
}

.signature-area {
  margin-top: 20px;
}

.signature-pad-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.signature-pad {
  width: 600px;
  height: 200px;
  border: 1px solid #d9d9d9;
  background-color: white;
  border-radius: 4px;
  margin-bottom: 16px;
}

.signature-actions {
  display: flex;
  gap: 16px;
}

.upload-signature {
  display: flex;
  justify-content: center;
}

.submission-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style> 