<template>
  <div class="pending-approval-list">
    <a-table
      :columns="columns"
      :data-source="pendingApprovals"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 合同名称列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'contractName'">
          <div>
            <a @click="viewContract(record, 'approval')" class="font-medium">{{ record.contractName }}</a>
            <div class="text-xs text-gray-500">合同编号: {{ record.contractCode }}</div>
          </div>
        </template>
        
        <!-- 合同类型列 -->
        <template v-else-if="column.dataIndex === 'contractType'">
          <a-tag>{{ getContractTypeName(record.contractType) }}</a-tag>
        </template>
        
        <!-- 状态列 -->
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag color="processing">待审批</a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.dataIndex === 'action'">
          <a-button type="primary" size="small" @click="viewContract(record, 'approval')">
            审批
          </a-button>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits } from 'vue';
import { useContractStore, ContractType, ContractStatus } from '@/stores/contract';

const emit = defineEmits<{
  (e: 'view', type: 'approval' | 'signature', id: string): void
}>();

const contractStore = useContractStore();
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 待审批合同列表
const pendingApprovals = ref<any[]>([]);

// 表格列定义
const columns = [
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
    ellipsis: true,
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    key: 'contractType',
    width: 120,
  },
  {
    title: '提交人',
    dataIndex: 'createdBy',
    key: 'createdBy',
    width: 100,
  },
  {
    title: '提交时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
    render: (text: string) => new Date(text).toLocaleString(),
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
  }
];

// 获取合同类型名称
const getContractTypeName = (type: ContractType) => {
  const typeMap = {
    [ContractType.SALES]: '销售合同',
    [ContractType.PURCHASE]: '采购合同',
    [ContractType.SERVICE]: '服务合同',
    [ContractType.FRAMEWORK]: '框架协议',
    [ContractType.OTHER]: '其他合同',
  };
  return typeMap[type] || '未知类型';
};

// 加载待审批合同
const fetchPendingApprovals = async () => {
  loading.value = true;
  try {
    const contracts = await contractStore.fetchContracts();
    // 筛选出待审批的合同
    pendingApprovals.value = contracts.filter(contract => 
      contract.status === ContractStatus.REVIEWING
    );
    pagination.total = pendingApprovals.value.length;
  } catch (error) {
    console.error('获取待审批合同失败:', error);
  } finally {
    loading.value = false;
  }
};

// 查看/处理合同
const viewContract = (record: any, type: 'approval' | 'signature') => {
  emit('view', type, record.id);
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

onMounted(() => {
  fetchPendingApprovals();
});
</script>

<style scoped>
.pending-approval-list {
  margin-top: 16px;
}
</style> 