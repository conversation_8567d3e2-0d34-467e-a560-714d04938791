<template>
  <div class="contract-approval-process-page">
    <a-card class="card-container">
      <template #title>
        <div class="flex justify-between items-center">
          <h2>合同审批</h2>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
        </div>
      </template>

      <a-spin :spinning="loading">
        <!-- 合同基本信息 -->
        <div v-if="contract" class="mb-6">
          <a-descriptions title="合同基本信息" bordered :column="2">
            <a-descriptions-item label="合同编号">{{ contract.contractCode }}</a-descriptions-item>
            <a-descriptions-item label="合同名称">{{ contract.contractName }}</a-descriptions-item>
            <a-descriptions-item label="合同类型">{{ getContractTypeName(contract.contractType) }}</a-descriptions-item>
            <a-descriptions-item label="合同状态">
              <a-tag :color="getStatusColor(contract.status)">
                {{ getStatusName(contract.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="甲方">{{ contract.partyA }}</a-descriptions-item>
            <a-descriptions-item label="乙方">{{ contract.partyB }}</a-descriptions-item>
            <a-descriptions-item label="合同金额">¥{{ formatNumber(contract.amount) }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ formatDate(contract.createdAt) }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 合同预览 -->
        <div class="mb-6">
          <a-divider orientation="left">合同内容预览</a-divider>
          <div class="contract-preview" v-html="contract?.content || '加载中...'"></div>
        </div>

        <!-- 审批表单 -->
        <a-form :model="approvalForm" :rules="rules" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="审批意见" name="opinion">
            <a-textarea v-model:value="approvalForm.opinion" :rows="4" placeholder="请输入审批意见" />
          </a-form-item>
          
          <a-form-item label="审批结果" name="status">
            <a-radio-group v-model:value="approvalForm.status">
              <a-radio value="approved">通过</a-radio>
              <a-radio value="rejected">驳回</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
            <a-space>
              <a-button type="primary" @click="submitApproval">
                提交审批
              </a-button>
              <a-button @click="goBack">
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, FormInstance } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { useContractStore, ContractType, ContractStatus } from '@/stores/contract';

const route = useRoute();
const router = useRouter();
const contractStore = useContractStore();
const formRef = ref<FormInstance>();

const contractId = ref(route.params.id as string);
const contract = ref(null);
const loading = ref(false);

// 审批表单
const approvalForm = reactive({
  opinion: '',
  status: 'approved',
});

// 表单验证规则
const rules = {
  opinion: [{ required: true, message: '请输入审批意见', trigger: 'blur' }],
  status: [{ required: true, message: '请选择审批结果', trigger: 'change' }],
};

// 获取合同类型名称
const getContractTypeName = (type: ContractType) => {
  const typeMap = {
    [ContractType.SALES]: '销售合同',
    [ContractType.PURCHASE]: '采购合同',
    [ContractType.SERVICE]: '服务合同',
    [ContractType.FRAMEWORK]: '框架协议',
    [ContractType.OTHER]: '其他合同',
  };
  return typeMap[type] || '未知类型';
};

// 获取合同状态名称
const getStatusName = (status: ContractStatus) => {
  const statusMap = {
    [ContractStatus.DRAFT]: '草稿',
    [ContractStatus.REVIEWING]: '审核中',
    [ContractStatus.REJECTED]: '已驳回',
    [ContractStatus.APPROVED]: '已审批',
    [ContractStatus.SIGNED]: '已签署',
    [ContractStatus.COMPLETED]: '已完成',
    [ContractStatus.ARCHIVED]: '已归档',
    [ContractStatus.TERMINATED]: '已终止',
  };
  return statusMap[status] || '未知状态';
};

// 获取状态颜色
const getStatusColor = (status: ContractStatus) => {
  const colorMap = {
    [ContractStatus.DRAFT]: 'default',
    [ContractStatus.REVIEWING]: 'processing',
    [ContractStatus.REJECTED]: 'error',
    [ContractStatus.APPROVED]: 'success',
    [ContractStatus.SIGNED]: 'success',
    [ContractStatus.COMPLETED]: 'success',
    [ContractStatus.ARCHIVED]: 'default',
    [ContractStatus.TERMINATED]: 'error',
  };
  return colorMap[status] || 'default';
};

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 返回上一页
const goBack = () => {
  router.push({ name: 'contracts-approval' });
};

// 提交审批
const submitApproval = () => {
  formRef.value?.validate().then(async () => {
    try {
      loading.value = true;
      
      // 根据审批结果调用不同的方法
      if (approvalForm.status === 'approved') {
        await contractStore.approveContract(contractId.value, 'current-user', approvalForm.opinion);
        message.success('合同审批通过');
      } else {
        // 假设有一个拒绝合同的方法
        // await contractStore.rejectContract(contractId.value, 'current-user', approvalForm.opinion);
        message.success('合同已驳回');
      }
      
      // 返回审批列表页
      router.push({ name: 'contracts-approval' });
    } catch (error) {
      console.error('审批提交失败:', error);
      message.error('审批提交失败，请重试');
    } finally {
      loading.value = false;
    }
  });
};

// 加载合同详情
const loadContractDetails = async () => {
  loading.value = true;
  try {
    contract.value = await contractStore.fetchContractById(contractId.value);
  } catch (error) {
    console.error('获取合同详情失败:', error);
    message.error('获取合同详情失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadContractDetails();
});
</script>

<style scoped>
.contract-approval-process-page {
  padding: 20px;
}

.card-container {
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.contract-preview {
  border: 1px solid #f0f0f0;
  padding: 20px;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  background-color: #fafafa;
}
</style> 