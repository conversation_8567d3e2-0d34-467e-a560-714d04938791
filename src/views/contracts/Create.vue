<template>
  <div class="contract-create-page">
    <!-- 页面标题区域 -->
    <div class="page-header mb-4">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">新增合同</h2>
          <p class="text-gray-500 mt-1">填写合同基本信息并上传相关附件</p>
        </div>
        <div>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleSave" :loading="saving">保存</a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <a-card :bordered="false">
      <a-form
        :model="formData"
        :rules="formRules"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 合同基本信息 -->
        <div class="form-section">
          <h3 class="section-title">合同基本信息</h3>
          
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="合同名称" name="name">
                <a-input v-model:value="formData.name" placeholder="请输入合同名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合同编号" name="code">
                <a-input v-model:value="formData.code" placeholder="请输入合同编号" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="合同类别" name="category">
                <a-select 
                  v-model:value="formData.category" 
                  placeholder="请选择合同类别"
                  @change="handleCategoryChange"
                >
                  <a-select-option value="sales">销售合同</a-select-option>
                  <a-select-option value="procurement">采购合同</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="关联项目" name="projectId">
                <a-select v-model:value="formData.projectId" placeholder="请选择关联项目" allowClear show-search>
                  <a-select-option v-for="project in projectOptions" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="合同类型" name="type">
                <a-select v-model:value="formData.type" placeholder="请选择合同类型">
                  <a-select-option value="product">产品销售</a-select-option>
                  <a-select-option value="service">服务合同</a-select-option>
                  <a-select-option value="project">项目合同</a-select-option>
                  <a-select-option value="framework">框架协议</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <!-- 根据合同类别显示不同的选择字段 -->
              <a-form-item 
                v-if="formData.category === 'sales'" 
                label="客户名称" 
                name="customerName"
              >
                <a-input v-model:value="formData.customerName" placeholder="请输入客户名称" />
              </a-form-item>
              <a-form-item 
                v-if="formData.category === 'procurement'" 
                label="供应商" 
                name="supplierId"
              >
                <a-select 
                  v-model:value="formData.supplierId" 
                  placeholder="请选择供应商" 
                  allowClear 
                  show-search
                  :filter-option="filterSupplierOption"
                  :loading="loadingSuppliers"
                >
                  <a-select-option 
                    v-for="supplier in supplierOptions" 
                    :key="supplier.id" 
                    :value="supplier.id"
                  >
                    {{ supplier.name }} ({{ supplier.code }})
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item 
                v-if="!formData.category || (formData.category !== 'sales' && formData.category !== 'procurement')" 
                label="合作方名称" 
                name="partnerName"
              >
                <a-input v-model:value="formData.partnerName" placeholder="请输入合作方名称" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="合同金额" name="amount">
                <a-input-number
                  v-model:value="formData.amount"
                  placeholder="请输入合同金额"
                  :min="0"
                  :step="0.01"
                  :precision="2"
                  style="width: 100%"
                  addon-before="¥"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="签约日期" name="signDate">
                <a-date-picker
                  v-model:value="formData.signDate"
                  placeholder="请选择签约日期"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="开始日期" name="startDate">
                <a-date-picker
                  v-model:value="formData.startDate"
                  placeholder="请选择开始日期"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="结束日期" name="endDate">
                <a-date-picker
                  v-model:value="formData.endDate"
                  placeholder="请选择结束日期"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="合同描述" name="description" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                <a-textarea
                  v-model:value="formData.description"
                  placeholder="请输入合同描述"
                  :rows="4"
                  show-count
                  :maxlength="500"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { useSupplierStore } from '@/stores/supplier';

const router = useRouter();
const formRef = ref<FormInstance>();
const saving = ref(false);
const projectOptions = ref<any[]>([]);
const supplierStore = useSupplierStore();

// 供应商相关状态
const supplierOptions = ref<any[]>([]);
const loadingSuppliers = ref(false);

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  category: undefined,
  projectId: undefined,
  type: undefined,
  customerName: '',
  supplierId: undefined,
  partnerName: '',
  amount: undefined,
  signDate: undefined,
  startDate: undefined,
  endDate: undefined,
  description: '',
  attachments: []
});

// 动态表单验证规则
const formRules = computed(() => {
  const baseRules = {
    name: [
      { required: true, message: '请输入合同名称', trigger: 'blur' }
    ],
    code: [
      { required: true, message: '请输入合同编号', trigger: 'blur' }
    ],
    category: [
      { required: true, message: '请选择合同类别', trigger: 'change' }
    ],
    projectId: [
      { required: true, message: '请选择关联项目', trigger: 'change' }
    ],
    type: [
      { required: true, message: '请选择合同类型', trigger: 'change' }
    ],
    amount: [
      { required: true, message: '请输入合同金额', trigger: 'blur' }
    ],
    signDate: [
      { required: true, message: '请选择签约日期', trigger: 'change' }
    ]
  };

  // 根据合同类别添加相应的验证规则
  if (formData.category === 'sales') {
    (baseRules as any).customerName = [
      { required: true, message: '请输入客户名称', trigger: 'blur' }
    ];
  } else if (formData.category === 'procurement') {
    (baseRules as any).supplierId = [
      { required: true, message: '请选择供应商', trigger: 'change' }
    ];
  } else {
    (baseRules as any).partnerName = [
      { required: true, message: '请输入合作方名称', trigger: 'blur' }
    ];
  }

  return baseRules;
});

// 处理合同类别变化
const handleCategoryChange = (category: string) => {
  console.log('合同类别变化:', category);
  // 清空相关字段
  formData.customerName = '';
  formData.supplierId = undefined;
  formData.partnerName = '';
  
  // 如果选择采购合同，加载供应商列表
  if (category === 'procurement') {
    console.log('开始加载供应商列表');
    loadSupplierOptions();
  }
};

// 加载供应商选项
const loadSupplierOptions = async () => {
  try {
    loadingSuppliers.value = true;
    console.log('正在加载供应商数据...');
    const result = await supplierStore.fetchSuppliers({
      current: 1,
      pageSize: 1000 // 获取所有供应商
    });
    supplierOptions.value = result.data;
    console.log('供应商数据加载完成:', result.data.length, '个供应商');
  } catch (error) {
    console.error('加载供应商列表失败:', error);
    message.error('加载供应商列表失败');
  } finally {
    loadingSuppliers.value = false;
  }
};

// 供应商选项过滤
const filterSupplierOption = (input: string, option: any) => {
  const supplier = supplierOptions.value.find(s => s.id === option.value);
  if (!supplier) return false;
  
  const searchText = input.toLowerCase();
  return supplier.name.toLowerCase().includes(searchText) || 
         supplier.code.toLowerCase().includes(searchText);
};

// 保存合同
const handleSave = async () => {
  try {
    await formRef.value?.validate();
    saving.value = true;
    
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success('合同创建成功！');
    router.push('/contracts');
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    saving.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  router.back();
};

// 初始化项目选项和供应商数据
onMounted(() => {
  // 初始化项目选项
  projectOptions.value = Array.from({ length: 10 }, (_, index) => ({
    id: `PI${String(10000 + index).padStart(5, '0')}`,
    name: `${['新零售系统', '医疗平台', '云计算平台', '智能家居', 'AI助手'][index % 5]}项目${index + 1}`,
  }));
  
  // 预加载供应商数据
  loadSupplierOptions();
});
</script>

<style scoped>
.contract-create-page {
  background-color: #f0f2f5;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.page-header {
  position: relative;
  margin-bottom: 16px;
}

.page-header h2 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-card) {
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

:deep(.ant-card-body) {
  padding: 24px;
}

:deep(.ant-upload-dragger) {
  background-color: #fafafa;
}

:deep(.ant-upload-drag-icon) {
  font-size: 48px;
  color: #d9d9d9;
}
</style> 