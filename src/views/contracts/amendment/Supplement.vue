<template>
  <div class="supplement-page">
    <a-card :bordered="false">
      <a-page-header
        style="padding: 0"
        :title="isEdit ? '编辑补充协议' : '补充协议详情'"
        @back="goBack"
      />
      
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :disabled="!isEdit"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="合同编号" name="contractCode">
              <a-input v-model:value="formState.contractCode" placeholder="请输入合同编号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合同名称" name="contractName">
              <a-input v-model:value="formState.contractName" placeholder="请输入合同名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="协议编号" name="supplementCode">
              <a-input v-model:value="formState.supplementCode" placeholder="请输入补充协议编号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="协议名称" name="supplementName">
              <a-input v-model:value="formState.supplementName" placeholder="请输入补充协议名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="协议状态" name="status">
              <a-select v-model:value="formState.status" placeholder="请选择协议状态">
                <a-select-option 
                  v-for="status in statusOptions" 
                  :key="status.value" 
                  :value="status.value"
                >
                  {{ status.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生效日期" name="effectiveDate">
              <a-date-picker 
                v-model:value="formState.effectiveDate" 
                style="width: 100%" 
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="创建日期" name="createTime">
              <a-date-picker 
                v-model:value="formState.createTime" 
                style="width: 100%" 
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="更新日期" name="updateTime">
              <a-date-picker 
                v-model:value="formState.updateTime" 
                style="width: 100%" 
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="补充内容" name="content">
          <a-textarea 
            v-model:value="formState.content" 
            placeholder="请输入补充协议内容" 
            :rows="6" 
          />
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea 
            v-model:value="formState.remark" 
            placeholder="请输入备注信息" 
            :rows="3" 
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
          <a-button type="primary" @click="handleSubmit" v-if="isEdit">保存</a-button>
          <a-button @click="goBack" style="margin-left: 10px">取消</a-button>
          <a-button type="primary" @click="enableEdit" v-if="!isEdit">编辑</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

// 路由和导航
const router = useRouter();
const route = useRoute();

// 编辑模式状态
const isEdit = ref(false);

// 协议状态枚举
enum SupplementStatus {
  DRAFT = 'draft',        // 草稿
  PENDING = 'pending',    // 审批中
  APPROVED = 'approved',  // 已批准
  REJECTED = 'rejected',  // 已拒绝
  SIGNED = 'signed',      // 已签署
  CANCELLED = 'cancelled' // 已取消
}

// 协议状态选项
const statusOptions = [
  { label: '草稿', value: SupplementStatus.DRAFT },
  { label: '审批中', value: SupplementStatus.PENDING },
  { label: '已批准', value: SupplementStatus.APPROVED },
  { label: '已拒绝', value: SupplementStatus.REJECTED },
  { label: '已签署', value: SupplementStatus.SIGNED },
  { label: '已取消', value: SupplementStatus.CANCELLED },
];

// 表单状态
interface SupplementFormState {
  id: string;
  contractCode: string;
  contractName: string;
  supplementCode: string;
  supplementName: string;
  status: SupplementStatus | undefined;
  effectiveDate: string;
  createTime: string;
  updateTime: string;
  content: string;
  remark: string;
  [key: string]: any; // 索引签名
}

// 表单状态
const formState = reactive<SupplementFormState>({
  id: '',
  contractCode: '',
  contractName: '',
  supplementCode: '',
  supplementName: '',
  status: undefined,
  effectiveDate: '',
  createTime: '',
  updateTime: '',
  content: '',
  remark: '',
});

// 获取详情数据
const fetchSupplementDetail = async (id: string) => {
  try {
    // 实际项目中这里应当调用API获取数据
    // 这里使用模拟数据
    setTimeout(() => {
      // 模拟数据
      const mockData: SupplementFormState = {
        id: id,
        contractCode: 'HT-2023-001',
        contractName: '软件开发服务合同',
        supplementCode: 'BC-2023-001',
        supplementName: '软件开发服务合同补充协议一',
        status: SupplementStatus.APPROVED,
        effectiveDate: '2023-07-01',
        createTime: '2023-06-15',
        updateTime: '2023-06-20',
        content: '鉴于原合同中关于服务范围的约定需要进一步明确，双方经协商一致，达成如下补充协议：\n1. 服务范围扩展至包含移动端应用开发；\n2. 服务期限延长3个月至2023年12月31日；\n3. 合同总金额相应增加20万元。',
        remark: '本补充协议经双方签字盖章后生效，与原合同具有同等法律效力，是原合同不可分割的组成部分。',
      };
      
      // 使用Object.assign直接赋值所有属性
      Object.assign(formState, mockData);
    }, 500);
  } catch (error) {
    message.error('获取补充协议详情失败');
    console.error(error);
  }
};

// 启用编辑模式
const enableEdit = () => {
  isEdit.value = true;
};

// 返回列表页
const goBack = () => {
  router.push('/contracts/amendment');
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 实际项目中这里应当调用API保存数据
    message.success('保存成功');
    goBack();
  } catch (error) {
    message.error('保存失败');
    console.error(error);
  }
};

// 组件挂载时执行
onMounted(() => {
  const id = route.params.id as string;
  if (id) {
    fetchSupplementDetail(id);
  } else {
    // 如果是新建，启用编辑模式
    isEdit.value = true;
  }
});
</script>

<style scoped>
.supplement-page {
  padding: 24px;
  background: #f0f2f5;
}
</style> 