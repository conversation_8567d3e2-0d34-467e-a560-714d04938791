<template>
  <div class="amendment-change-page">
    <a-card :bordered="false">
      <a-page-header
        style="padding: 0"
        :title="isEdit ? '编辑合同变更' : '合同变更详情'"
        @back="goBack"
      />
      
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :disabled="!isEdit"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="合同编号" name="contractCode">
              <a-input v-model:value="formState.contractCode" placeholder="请输入合同编号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合同名称" name="contractName">
              <a-input v-model:value="formState.contractName" placeholder="请输入合同名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="变更类型" name="changeType">
              <a-select v-model:value="formState.changeType" placeholder="请选择变更类型">
                <a-select-option 
                  v-for="type in amendmentTypeOptions" 
                  :key="type.value" 
                  :value="type.value"
                >
                  {{ type.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="变更状态" name="status">
              <a-select v-model:value="formState.status" placeholder="请选择变更状态">
                <a-select-option 
                  v-for="status in amendmentStatusOptions" 
                  :key="status.value" 
                  :value="status.value"
                >
                  {{ status.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="创建日期" name="createTime">
              <a-date-picker 
                v-model:value="formState.createTime" 
                style="width: 100%" 
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="更新日期" name="updateTime">
              <a-date-picker 
                v-model:value="formState.updateTime" 
                style="width: 100%" 
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="变更原因" name="reason">
          <a-textarea 
            v-model:value="formState.reason" 
            placeholder="请输入变更原因" 
            :rows="4" 
          />
        </a-form-item>

        <a-form-item label="变更内容" name="content">
          <a-textarea 
            v-model:value="formState.content" 
            placeholder="请输入变更内容" 
            :rows="4" 
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
          <a-button type="primary" @click="handleSubmit" v-if="isEdit">保存</a-button>
          <a-button @click="goBack" style="margin-left: 10px">取消</a-button>
          <a-button type="primary" @click="enableEdit" v-if="!isEdit">编辑</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';

// 路由和导航
const router = useRouter();
const route = useRoute();

// 编辑模式状态
const isEdit = ref(false);

// 变更类型枚举
enum AmendmentType {
  PRICE_CHANGE = 1,
  TERM_CHANGE = 2,
  PARTY_CHANGE = 3,
  OTHER = 4,
}

// 变更状态枚举
enum AmendmentStatus {
  PENDING = 1,
  APPROVED = 2,
  REJECTED = 3,
  CANCELED = 4,
}

// 变更类型选项
const amendmentTypeOptions = [
  { label: '价格变更', value: AmendmentType.PRICE_CHANGE },
  { label: '条款变更', value: AmendmentType.TERM_CHANGE },
  { label: '主体变更', value: AmendmentType.PARTY_CHANGE },
  { label: '其他变更', value: AmendmentType.OTHER },
];

// 变更状态选项
const amendmentStatusOptions = [
  { label: '待审批', value: AmendmentStatus.PENDING },
  { label: '已通过', value: AmendmentStatus.APPROVED },
  { label: '已拒绝', value: AmendmentStatus.REJECTED },
  { label: '已取消', value: AmendmentStatus.CANCELED },
];

// 表单状态
interface FormState {
  id: string;
  contractCode: string;
  contractName: string;
  changeType: AmendmentType | undefined;
  status: AmendmentStatus | undefined;
  createTime: string;
  updateTime: string;
  reason: string;
  content: string;
  [key: string]: any; // 添加索引签名
}

// 表单状态
const formState = reactive<FormState>({
  id: '',
  contractCode: '',
  contractName: '',
  changeType: undefined,
  status: undefined,
  createTime: '',
  updateTime: '',
  reason: '',
  content: '',
});

// 获取详情数据
const fetchAmendmentDetail = async (id: string) => {
  try {
    // 实际项目中这里应当调用API获取数据
    // 这里使用模拟数据
    setTimeout(() => {
      // 模拟数据
      const mockData: FormState = {
        id: id,
        contractCode: 'HT-2023-001',
        contractName: '软件开发服务合同',
        changeType: AmendmentType.PRICE_CHANGE,
        status: AmendmentStatus.APPROVED,
        createTime: '2023-06-10',
        updateTime: '2023-06-15',
        reason: '由于项目范围扩大，需要增加合同金额。',
        content: '原合同金额100万元，变更后为120万元。新增功能包括高级数据分析模块。',
      };
      
      // 使用Object.assign直接赋值所有属性
      Object.assign(formState, mockData);
    }, 500);
  } catch (error) {
    message.error('获取变更详情失败');
    console.error(error);
  }
};

// 启用编辑模式
const enableEdit = () => {
  isEdit.value = true;
};

// 返回列表页
const goBack = () => {
  router.push('/contracts/amendment');
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 实际项目中这里应当调用API保存数据
    message.success('保存成功');
    goBack();
  } catch (error) {
    message.error('保存失败');
    console.error(error);
  }
};

// 组件挂载时执行
onMounted(() => {
  const id = route.params.id as string;
  if (id) {
    fetchAmendmentDetail(id);
  } else {
    // 如果是新建，启用编辑模式
    isEdit.value = true;
  }
});
</script>

<style scoped>
.amendment-change-page {
  padding: 24px;
  background: #f0f2f5;
}
</style> 