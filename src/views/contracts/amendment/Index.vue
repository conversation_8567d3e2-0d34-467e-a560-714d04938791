<template>
  <div class="contract-amendment-page">
    <page-header>
      <template #title>合同修改与补充</template>
      <template #description>管理合同的变更、修改与补充协议</template>
    </page-header>

    <a-card :bordered="false" class="card-container">
      <!-- Tab selection for amendment/supplement -->
      <a-tabs v-model:activeKey="activeTabKey">
        <a-tab-pane key="amendment" tab="合同修改">
          <!-- Search and filter for amendments -->
          <div class="search-container">
            <a-form layout="inline" :model="amendmentFilter">
              <a-row :gutter="[16, 16]" style="width: 100%">
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="合同编号" name="contractNumber">
                    <a-input v-model:value="amendmentFilter.contractNumber" placeholder="请输入合同编号" />
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="合同名称" name="contractName">
                    <a-input v-model:value="amendmentFilter.contractName" placeholder="请输入合同名称" />
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="修改类型" name="amendmentType">
                    <a-select v-model:value="amendmentFilter.amendmentType" placeholder="请选择修改类型" allowClear>
                      <a-select-option v-for="type in amendmentTypes" :key="type.value" :value="type.value">
                        {{ type.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="状态" name="status">
                    <a-select v-model:value="amendmentFilter.status" placeholder="请选择状态" allowClear>
                      <a-select-option v-for="status in amendmentStatuses" :key="status.value" :value="status.value">
                        {{ status.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="24" :md="24" :lg="24" style="text-align: right">
                  <a-button type="primary" @click="searchAmendments">
                    <template #icon><SearchOutlined /></template>
                    查询
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetAmendmentFilter">
                    <template #icon><ReloadOutlined /></template>
                    重置
                  </a-button>
                </a-col>
              </a-row>
            </a-form>
          </div>

          <!-- Amendment table -->
          <div class="table-container">
            <div class="table-operations">
              <a-button type="primary" @click="handleAddAmendment">
                <template #icon><PlusOutlined /></template>
                新增修改
              </a-button>
              <a-button style="margin-left: 8px" @click="refreshAmendments">
                <template #icon><SyncOutlined /></template>
                刷新
              </a-button>
            </div>
            <a-table
              :columns="amendmentColumns"
              :data-source="amendmentData"
              :pagination="amendmentPagination"
              :loading="amendmentLoading"
              rowKey="id"
              @change="handleAmendmentTableChange"
            >
              <template #bodyCell="{ column, record }">
                <!-- Custom cell content for specific columns -->
                <template v-if="column.key === 'contractName'">
                  <a @click="viewContract(record.contractId)">{{ record.contractName }}</a>
                </template>
                <template v-if="column.key === 'amendmentType'">
                  <a-tag :color="getAmendmentTypeColor(record.amendmentType)">
                    {{ getAmendmentTypeName(record.amendmentType) }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'status'">
                  <a-badge :status="getAmendmentStatusBadge(record.status)" :text="getAmendmentStatusName(record.status)" />
                </template>
                <template v-if="column.key === 'action'">
                  <a-space size="small">
                    <a @click="viewAmendment(record.id)">查看</a>
                    <a @click="editAmendment(record.id)" v-if="record.status === AmendmentStatus.DRAFT">编辑</a>
                    <a-popconfirm
                      title="确定要删除这个修改吗?"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="deleteAmendment(record.id)"
                      v-if="record.status === AmendmentStatus.DRAFT"
                    >
                      <a class="danger-link">删除</a>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <a-tab-pane key="supplement" tab="合同补充">
          <!-- Search and filter for supplements -->
          <div class="search-container">
            <a-form layout="inline" :model="supplementFilter">
              <a-row :gutter="[16, 16]" style="width: 100%">
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="合同编号" name="contractNumber">
                    <a-input v-model:value="supplementFilter.contractNumber" placeholder="请输入合同编号" />
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="合同名称" name="contractName">
                    <a-input v-model:value="supplementFilter.contractName" placeholder="请输入合同名称" />
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="补充类型" name="supplementType">
                    <a-select v-model:value="supplementFilter.supplementType" placeholder="请选择补充类型" allowClear>
                      <a-select-option v-for="type in supplementTypes" :key="type.value" :value="type.value">
                        {{ type.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :lg="6">
                  <a-form-item label="状态" name="status">
                    <a-select v-model:value="supplementFilter.status" placeholder="请选择状态" allowClear>
                      <a-select-option v-for="status in supplementStatuses" :key="status.value" :value="status.value">
                        {{ status.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="24" :md="24" :lg="24" style="text-align: right">
                  <a-button type="primary" @click="searchSupplements">
                    <template #icon><SearchOutlined /></template>
                    查询
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetSupplementFilter">
                    <template #icon><ReloadOutlined /></template>
                    重置
                  </a-button>
                </a-col>
              </a-row>
            </a-form>
          </div>

          <!-- Supplement table -->
          <div class="table-container">
            <div class="table-operations">
              <a-button type="primary" @click="handleAddSupplement">
                <template #icon><PlusOutlined /></template>
                新增补充
              </a-button>
              <a-button style="margin-left: 8px" @click="refreshSupplements">
                <template #icon><SyncOutlined /></template>
                刷新
              </a-button>
            </div>
            <a-table
              :columns="supplementColumns"
              :data-source="supplementData"
              :pagination="supplementPagination"
              :loading="supplementLoading"
              rowKey="id"
              @change="handleSupplementTableChange"
            >
              <template #bodyCell="{ column, record }">
                <!-- Custom cell content for specific columns -->
                <template v-if="column.key === 'contractName'">
                  <a @click="viewContract(record.contractId)">{{ record.contractName }}</a>
                </template>
                <template v-if="column.key === 'supplementType'">
                  <a-tag :color="getSupplementTypeColor(record.supplementType)">
                    {{ getSupplementTypeName(record.supplementType) }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'status'">
                  <a-badge :status="getSupplementStatusBadge(record.status)" :text="getSupplementStatusName(record.status)" />
                </template>
                <template v-if="column.key === 'action'">
                  <a-space size="small">
                    <a @click="viewSupplement(record.id)">查看</a>
                    <a @click="editSupplement(record.id)" v-if="record.status === SupplementStatus.DRAFT">编辑</a>
                    <a-popconfirm
                      title="确定要删除这个补充吗?"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="deleteSupplement(record.id)"
                      v-if="record.status === SupplementStatus.DRAFT"
                    >
                      <a class="danger-link">删除</a>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { SearchOutlined, PlusOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { TableProps } from 'ant-design-vue';

// Define enums for amendment and supplement types and statuses
enum AmendmentType {
  TERMS = 'terms',
  PRICE = 'price',
  SCHEDULE = 'schedule',
  SCOPE = 'scope',
  OTHER = 'other',
}

enum AmendmentStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

enum SupplementType {
  ADDENDUM = 'addendum',
  EXTENSION = 'extension',
  CLARIFICATION = 'clarification',
  WAIVER = 'waiver',
  OTHER = 'other',
}

enum SupplementStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

// Setup router
const router = useRouter();

// Active tab
const activeTabKey = ref<string>('amendment');

// Filter forms
const amendmentFilter = reactive({
  contractNumber: '',
  contractName: '',
  amendmentType: undefined as AmendmentType | undefined,
  status: undefined as AmendmentStatus | undefined,
});

const supplementFilter = reactive({
  contractNumber: '',
  contractName: '',
  supplementType: undefined as SupplementType | undefined,
  status: undefined as SupplementStatus | undefined,
});

// Options for dropdowns
const amendmentTypes = [
  { label: '条款修改', value: AmendmentType.TERMS },
  { label: '价格修改', value: AmendmentType.PRICE },
  { label: '时间修改', value: AmendmentType.SCHEDULE },
  { label: '范围修改', value: AmendmentType.SCOPE },
  { label: '其他修改', value: AmendmentType.OTHER },
];

const amendmentStatuses = [
  { label: '草稿', value: AmendmentStatus.DRAFT },
  { label: '待审批', value: AmendmentStatus.PENDING_APPROVAL },
  { label: '已审批', value: AmendmentStatus.APPROVED },
  { label: '已拒绝', value: AmendmentStatus.REJECTED },
  { label: '已取消', value: AmendmentStatus.CANCELLED },
];

const supplementTypes = [
  { label: '附录', value: SupplementType.ADDENDUM },
  { label: '延期', value: SupplementType.EXTENSION },
  { label: '澄清', value: SupplementType.CLARIFICATION },
  { label: '豁免', value: SupplementType.WAIVER },
  { label: '其他', value: SupplementType.OTHER },
];

const supplementStatuses = [
  { label: '草稿', value: SupplementStatus.DRAFT },
  { label: '待审批', value: SupplementStatus.PENDING_APPROVAL },
  { label: '已审批', value: SupplementStatus.APPROVED },
  { label: '已拒绝', value: SupplementStatus.REJECTED },
  { label: '已取消', value: SupplementStatus.CANCELLED },
];

// Table columns
const amendmentColumns = [
  {
    title: '修改编号',
    dataIndex: 'code',
    key: 'code',
    sorter: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
  },
  {
    title: '修改类型',
    dataIndex: 'amendmentType',
    key: 'amendmentType',
    filters: amendmentTypes.map(type => ({ text: type.label, value: type.value })),
  },
  {
    title: '提交日期',
    dataIndex: 'submitDate',
    key: 'submitDate',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    filters: amendmentStatuses.map(status => ({ text: status.label, value: status.value })),
  },
  {
    title: '操作',
    key: 'action',
  },
];

const supplementColumns = [
  {
    title: '补充编号',
    dataIndex: 'code',
    key: 'code',
    sorter: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    key: 'contractName',
  },
  {
    title: '补充类型',
    dataIndex: 'supplementType',
    key: 'supplementType',
    filters: supplementTypes.map(type => ({ text: type.label, value: type.value })),
  },
  {
    title: '提交日期',
    dataIndex: 'submitDate',
    key: 'submitDate',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    filters: supplementStatuses.map(status => ({ text: status.label, value: status.value })),
  },
  {
    title: '操作',
    key: 'action',
  },
];

// Data and loading states
const amendmentData = ref<any[]>([]);
const supplementData = ref<any[]>([]);
const amendmentLoading = ref<boolean>(false);
const supplementLoading = ref<boolean>(false);

// Pagination
const amendmentPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

const supplementPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

// Load data functions
const loadAmendmentData = async () => {
  amendmentLoading.value = true;
  try {
    // Mock API call - replace with actual API implementation
    setTimeout(() => {
      // Simulating data fetch
      amendmentData.value = generateMockAmendments();
      amendmentPagination.total = amendmentData.value.length;
      amendmentLoading.value = false;
    }, 800);
  } catch (error) {
    message.error('加载合同修改数据失败');
    amendmentLoading.value = false;
  }
};

const loadSupplementData = async () => {
  supplementLoading.value = true;
  try {
    // Mock API call - replace with actual API implementation
    setTimeout(() => {
      // Simulating data fetch
      supplementData.value = generateMockSupplements();
      supplementPagination.total = supplementData.value.length;
      supplementLoading.value = false;
    }, 800);
  } catch (error) {
    message.error('加载合同补充数据失败');
    supplementLoading.value = false;
  }
};

// Search functions
const searchAmendments = () => {
  amendmentPagination.current = 1;
  loadAmendmentData();
};

const searchSupplements = () => {
  supplementPagination.current = 1;
  loadSupplementData();
};

// Reset filters
const resetAmendmentFilter = () => {
  Object.keys(amendmentFilter).forEach(key => {
    amendmentFilter[key as keyof typeof amendmentFilter] = '';
  });
  searchAmendments();
};

const resetSupplementFilter = () => {
  Object.keys(supplementFilter).forEach(key => {
    supplementFilter[key as keyof typeof supplementFilter] = '';
  });
  searchSupplements();
};

// Table change handlers
const handleAmendmentTableChange: TableProps['onChange'] = (
  pagination,
  filters,
  sorter
) => {
  amendmentPagination.current = pagination.current || 1;
  amendmentPagination.pageSize = pagination.pageSize || 10;
  // Handle filters and sorter
  loadAmendmentData();
};

const handleSupplementTableChange: TableProps['onChange'] = (
  pagination,
  filters,
  sorter
) => {
  supplementPagination.current = pagination.current || 1;
  supplementPagination.pageSize = pagination.pageSize || 10;
  // Handle filters and sorter
  loadSupplementData();
};

// Refresh data
const refreshAmendments = () => {
  loadAmendmentData();
};

const refreshSupplements = () => {
  loadSupplementData();
};

// Actions for amendments
const handleAddAmendment = () => {
  router.push('/contracts/amendment/create');
};

const viewAmendment = (id: string) => {
  router.push(`/contracts/amendment/view/${id}`);
};

const editAmendment = (id: string) => {
  router.push(`/contracts/amendment/edit/${id}`);
};

const deleteAmendment = (id: string) => {
  // Mock API call - replace with actual API implementation
  const index = amendmentData.value.findIndex(item => item.id === id);
  if (index !== -1) {
    amendmentData.value.splice(index, 1);
    message.success('删除成功');
  }
};

// Actions for supplements
const handleAddSupplement = () => {
  router.push('/contracts/supplement/create');
};

const viewSupplement = (id: string) => {
  router.push(`/contracts/supplement/view/${id}`);
};

const editSupplement = (id: string) => {
  router.push(`/contracts/supplement/edit/${id}`);
};

const deleteSupplement = (id: string) => {
  // Mock API call - replace with actual API implementation
  const index = supplementData.value.findIndex(item => item.id === id);
  if (index !== -1) {
    supplementData.value.splice(index, 1);
    message.success('删除成功');
  }
};

// View contract
const viewContract = (id: string) => {
  router.push(`/contracts/details/${id}`);
};

// Utility functions for displaying type and status
const getAmendmentTypeColor = (type: AmendmentType) => {
  const colorMap: Record<AmendmentType, string> = {
    [AmendmentType.TERMS]: 'blue',
    [AmendmentType.PRICE]: 'gold',
    [AmendmentType.SCHEDULE]: 'green',
    [AmendmentType.SCOPE]: 'purple',
    [AmendmentType.OTHER]: 'default',
  };
  return colorMap[type] || 'default';
};

const getAmendmentTypeName = (type: AmendmentType) => {
  const nameMap: Record<AmendmentType, string> = {
    [AmendmentType.TERMS]: '条款修改',
    [AmendmentType.PRICE]: '价格修改',
    [AmendmentType.SCHEDULE]: '时间修改',
    [AmendmentType.SCOPE]: '范围修改',
    [AmendmentType.OTHER]: '其他修改',
  };
  return nameMap[type] || '未知类型';
};

const getAmendmentStatusBadge = (status: AmendmentStatus) => {
  const statusMap: Record<AmendmentStatus, any> = {
    [AmendmentStatus.DRAFT]: 'default',
    [AmendmentStatus.PENDING_APPROVAL]: 'processing',
    [AmendmentStatus.APPROVED]: 'success',
    [AmendmentStatus.REJECTED]: 'error',
    [AmendmentStatus.CANCELLED]: 'warning',
  };
  return statusMap[status] || 'default';
};

const getAmendmentStatusName = (status: AmendmentStatus) => {
  const nameMap: Record<AmendmentStatus, string> = {
    [AmendmentStatus.DRAFT]: '草稿',
    [AmendmentStatus.PENDING_APPROVAL]: '待审批',
    [AmendmentStatus.APPROVED]: '已审批',
    [AmendmentStatus.REJECTED]: '已拒绝',
    [AmendmentStatus.CANCELLED]: '已取消',
  };
  return nameMap[status] || '未知状态';
};

const getSupplementTypeColor = (type: SupplementType) => {
  const colorMap: Record<SupplementType, string> = {
    [SupplementType.ADDENDUM]: 'blue',
    [SupplementType.EXTENSION]: 'orange',
    [SupplementType.CLARIFICATION]: 'green',
    [SupplementType.WAIVER]: 'purple',
    [SupplementType.OTHER]: 'default',
  };
  return colorMap[type] || 'default';
};

const getSupplementTypeName = (type: SupplementType) => {
  const nameMap: Record<SupplementType, string> = {
    [SupplementType.ADDENDUM]: '附录',
    [SupplementType.EXTENSION]: '延期',
    [SupplementType.CLARIFICATION]: '澄清',
    [SupplementType.WAIVER]: '豁免',
    [SupplementType.OTHER]: '其他',
  };
  return nameMap[type] || '未知类型';
};

const getSupplementStatusBadge = (status: SupplementStatus) => {
  const statusMap: Record<SupplementStatus, any> = {
    [SupplementStatus.DRAFT]: 'default',
    [SupplementStatus.PENDING_APPROVAL]: 'processing',
    [SupplementStatus.APPROVED]: 'success',
    [SupplementStatus.REJECTED]: 'error',
    [SupplementStatus.CANCELLED]: 'warning',
  };
  return statusMap[status] || 'default';
};

const getSupplementStatusName = (status: SupplementStatus) => {
  const nameMap: Record<SupplementStatus, string> = {
    [SupplementStatus.DRAFT]: '草稿',
    [SupplementStatus.PENDING_APPROVAL]: '待审批',
    [SupplementStatus.APPROVED]: '已审批',
    [SupplementStatus.REJECTED]: '已拒绝',
    [SupplementStatus.CANCELLED]: '已取消',
  };
  return nameMap[status] || '未知状态';
};

// Mock data generators
const generateMockAmendments = () => {
  return Array.from({ length: 15 }, (_, index) => ({
    id: `AMD-${100 + index}`,
    code: `AMD-${100 + index}`,
    contractId: `CTR-${200 + index}`,
    contractName: `销售合同 ${200 + index}`,
    amendmentType: Object.values(AmendmentType)[index % Object.values(AmendmentType).length],
    description: `修改合同的${getAmendmentTypeName(Object.values(AmendmentType)[index % Object.values(AmendmentType).length])}内容`,
    submitDate: new Date(Date.now() - index * 86400000).toISOString().split('T')[0],
    status: Object.values(AmendmentStatus)[index % Object.values(AmendmentStatus).length],
    createdBy: '张三',
    approvedBy: index % 2 === 0 ? '李四' : null,
    approvalDate: index % 2 === 0 ? new Date(Date.now() - (index * 43200000)).toISOString().split('T')[0] : null,
  }));
};

const generateMockSupplements = () => {
  return Array.from({ length: 12 }, (_, index) => ({
    id: `SUP-${100 + index}`,
    code: `SUP-${100 + index}`,
    contractId: `CTR-${200 + index}`,
    contractName: `销售合同 ${200 + index}`,
    supplementType: Object.values(SupplementType)[index % Object.values(SupplementType).length],
    description: `补充合同的${getSupplementTypeName(Object.values(SupplementType)[index % Object.values(SupplementType).length])}内容`,
    submitDate: new Date(Date.now() - index * 86400000).toISOString().split('T')[0],
    status: Object.values(SupplementStatus)[index % Object.values(SupplementStatus).length],
    createdBy: '张三',
    approvedBy: index % 2 === 0 ? '李四' : null,
    approvalDate: index % 2 === 0 ? new Date(Date.now() - (index * 43200000)).toISOString().split('T')[0] : null,
  }));
};

// Initialize
onMounted(() => {
  loadAmendmentData();
  loadSupplementData();
});
</script>

<style scoped>
.contract-amendment-page {
  background-color: #f0f2f5;
}

.card-container {
  margin-bottom: 24px;
}

.search-container {
  margin-bottom: 16px;
}

.table-container {
  margin-top: 16px;
}

.table-operations {
  margin-bottom: 16px;
}

.danger-link {
  color: #ff4d4f;
}

.danger-link:hover {
  color: #ff7875;
}
</style> 