<template>
  <div class="settings-page">
    <a-card class="mb-4">
      <a-tabs>
        <!-- 基本信息设置 -->
        <a-tab-pane key="basic" tab="基本信息">
          <a-form
            :model="basicForm"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="公司名称" name="companyName">
              <a-input v-model:value="basicForm.companyName" placeholder="请输入公司名称" />
            </a-form-item>
            <a-form-item label="系统Logo" name="logo">
              <a-upload
                v-model:file-list="basicForm.logoList"
                list-type="picture-card"
                :max-count="1"
                @preview="handlePreview"
              >
                <div v-if="basicForm.logoList.length < 1">
                  <plus-outlined />
                  <div class="ant-upload-text">上传</div>
                </div>
              </a-upload>
            </a-form-item>
            <a-form-item label="系统主题" name="theme">
              <a-radio-group v-model:value="basicForm.theme">
                <a-radio value="light">浅色</a-radio>
                <a-radio value="dark">深色</a-radio>
                <a-radio value="auto">跟随系统</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="时区设置" name="timezone">
              <a-select
                v-model:value="basicForm.timezone"
                placeholder="请选择时区"
                :options="timezoneOptions"
              />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 4 }">
              <a-button type="primary" @click="handleBasicSave">保存设置</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>

        <!-- 安全设置 -->
        <a-tab-pane key="security" tab="安全设置">
          <a-form
            :model="securityForm"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="密码有效期" name="passwordExpiry">
              <a-select
                v-model:value="securityForm.passwordExpiry"
                placeholder="请选择密码有效期"
              >
                <a-select-option value="30">30天</a-select-option>
                <a-select-option value="60">60天</a-select-option>
                <a-select-option value="90">90天</a-select-option>
                <a-select-option value="-1">永不过期</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="登录安全" name="loginSecurity">
              <a-checkbox-group v-model:value="securityForm.loginSecurity">
                <a-checkbox value="2fa">开启两步验证</a-checkbox>
                <a-checkbox value="ip">异地登录提醒</a-checkbox>
                <a-checkbox value="device">新设备登录提醒</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item label="会话超时" name="sessionTimeout">
              <a-input-number
                v-model:value="securityForm.sessionTimeout"
                :min="5"
                :max="1440"
                addon-after="分钟"
              />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 4 }">
              <a-button type="primary" @click="handleSecuritySave">保存设置</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>

        <!-- 通知设置 -->
        <a-tab-pane key="notification" tab="通知设置">
          <a-form
            :model="notificationForm"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="系统通知">
              <a-checkbox-group v-model:value="notificationForm.systemNotifications">
                <div class="flex flex-col gap-4">
                  <a-checkbox value="maintenance">系统维护通知</a-checkbox>
                  <a-checkbox value="update">系统更新通知</a-checkbox>
                  <a-checkbox value="security">安全提醒</a-checkbox>
                </div>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item label="业务通知">
              <a-checkbox-group v-model:value="notificationForm.businessNotifications">
                <div class="flex flex-col gap-4">
                  <a-checkbox value="contract">合同到期提醒</a-checkbox>
                  <a-checkbox value="payment">付款提醒</a-checkbox>
                  <a-checkbox value="task">任务提醒</a-checkbox>
                </div>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item label="通知方式">
              <a-checkbox-group v-model:value="notificationForm.notificationMethods">
                <div class="flex flex-col gap-4">
                  <a-checkbox value="email">邮件通知</a-checkbox>
                  <a-checkbox value="sms">短信通知</a-checkbox>
                  <a-checkbox value="wechat">微信通知</a-checkbox>
                </div>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 4 }">
              <a-button type="primary" @click="handleNotificationSave">保存设置</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 图片预览弹窗 -->
    <a-modal
      v-model:visible="previewVisible"
      title="预览"
      :footer="null"
    >
      <img :src="previewImage" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';

// 基本信息表单
const basicForm = reactive({
  companyName: '示例公司',
  logoList: [],
  theme: 'light',
  timezone: 'Asia/Shanghai',
});

// 时区选项
const timezoneOptions = [
  { label: '(GMT+08:00) 北京', value: 'Asia/Shanghai' },
  { label: '(GMT+08:00) 香港', value: 'Asia/Hong_Kong' },
  { label: '(GMT+08:00) 台北', value: 'Asia/Taipei' },
  { label: '(GMT+09:00) 东京', value: 'Asia/Tokyo' },
  { label: '(GMT+00:00) 伦敦', value: 'Europe/London' },
  { label: '(GMT-05:00) 纽约', value: 'America/New_York' },
];

// 安全设置表单
const securityForm = reactive({
  passwordExpiry: '90',
  loginSecurity: ['2fa', 'ip'],
  sessionTimeout: 30,
});

// 通知设置表单
const notificationForm = reactive({
  systemNotifications: ['maintenance', 'security'],
  businessNotifications: ['contract', 'payment'],
  notificationMethods: ['email', 'wechat'],
});

// 图片预览
const previewVisible = ref(false);
const previewImage = ref('');

// 处理图片预览
function handlePreview(file: any) {
  previewImage.value = file.url || file.preview;
  previewVisible.value = true;
}

// 保存基本信息设置
async function handleBasicSave() {
  try {
    // TODO: 调用保存接口
    message.success('保存成功');
  } catch (error) {
    message.error('保存失败');
  }
}

// 保存安全设置
async function handleSecuritySave() {
  try {
    // TODO: 调用保存接口
    message.success('保存成功');
  } catch (error) {
    message.error('保存失败');
  }
}

// 保存通知设置
async function handleNotificationSave() {
  try {
    // TODO: 调用保存接口
    message.success('保存成功');
  } catch (error) {
    message.error('保存失败');
  }
}
</script> 