<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <a-card>
        <template #title>
          <div class="flex items-center gap-2">
            <team-outlined class="text-blue-500" />
            <span>客户总数</span>
          </div>
        </template>
        <div class="text-2xl font-bold">{{ statistics.customerCount }}</div>
        <div class="text-gray-500 text-sm mt-2">
          较上月{{ statistics.customerGrowth > 0 ? '增长' : '减少' }}
          {{ Math.abs(statistics.customerGrowth) }}%
        </div>
      </a-card>

      <a-card>
        <template #title>
          <div class="flex items-center gap-2">
            <project-outlined class="text-green-500" />
            <span>项目总数</span>
          </div>
        </template>
        <div class="text-2xl font-bold">{{ statistics.projectCount }}</div>
        <div class="text-gray-500 text-sm mt-2">
          较上月{{ statistics.projectGrowth > 0 ? '增长' : '减少' }}
          {{ Math.abs(statistics.projectGrowth) }}%
        </div>
      </a-card>

      <a-card>
        <template #title>
          <div class="flex items-center gap-2">
            <file-text-outlined class="text-purple-500" />
            <span>合同总数</span>
          </div>
        </template>
        <div class="text-2xl font-bold">{{ statistics.contractCount }}</div>
        <div class="text-gray-500 text-sm mt-2">
          较上月{{ statistics.contractGrowth > 0 ? '增长' : '减少' }}
          {{ Math.abs(statistics.contractGrowth) }}%
        </div>
      </a-card>

      <a-card>
        <template #title>
          <div class="flex items-center gap-2">
            <dollar-outlined class="text-yellow-500" />
            <span>销售总额</span>
          </div>
        </template>
        <div class="text-2xl font-bold">¥{{ formatMoney(statistics.totalSales) }}</div>
        <div class="text-gray-500 text-sm mt-2">
          较上月{{ statistics.salesGrowth > 0 ? '增长' : '减少' }}
          {{ Math.abs(statistics.salesGrowth) }}%
        </div>
      </a-card>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 销售趋势图 -->
      <a-card title="销售趋势">
        <div ref="salesChartRef" style="height: 300px"></div>
      </a-card>

      <!-- 客户行业分布 -->
      <a-card title="客户行业分布">
        <div ref="industryChartRef" style="height: 300px"></div>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { TeamOutlined, ProjectOutlined, FileTextOutlined, DollarOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts';

// 统计数据
const statistics = ref({
  customerCount: 256,
  customerGrowth: 12.5,
  projectCount: 128,
  projectGrowth: 8.3,
  contractCount: 96,
  contractGrowth: 15.7,
  totalSales: 12580000,
  salesGrowth: 23.4,
});

// 格式化金额
function formatMoney(amount: number) {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 图表实例
const salesChartRef = ref<HTMLElement>();
const industryChartRef = ref<HTMLElement>();
let salesChart: echarts.ECharts;
let industryChart: echarts.ECharts;

// 初始化销售趋势图
function initSalesChart() {
  if (!salesChartRef.value) return;
  
  salesChart = echarts.init(salesChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: {
      type: 'value',
      name: '销售额（万元）',
    },
    series: [
      {
        name: '销售额',
        type: 'line',
        smooth: true,
        data: [120, 132, 101, 134, 90, 230],
        itemStyle: {
          color: '#1890ff',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(24,144,255,0.3)',
            },
            {
              offset: 1,
              color: 'rgba(24,144,255,0)',
            },
          ]),
        },
      },
    ],
  };
  salesChart.setOption(option);
}

// 初始化行业分布图
function initIndustryChart() {
  if (!industryChartRef.value) return;
  
  industryChart = echarts.init(industryChartRef.value);
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '行业分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: [
          { value: 35, name: '互联网' },
          { value: 25, name: '金融' },
          { value: 20, name: '制造业' },
          { value: 15, name: '教育' },
          { value: 5, name: '其他' },
        ],
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
      },
    ],
  };
  industryChart.setOption(option);
}

// 监听窗口大小变化
function handleResize() {
  salesChart?.resize();
  industryChart?.resize();
}

onMounted(() => {
  initSalesChart();
  initIndustryChart();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  salesChart?.dispose();
  industryChart?.dispose();
});
</script> 