<template>
  <div class="report-page">
    <!-- 数据概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <a-card>
        <statistic
          title="本月新增客户"
          :value="statistics.newCustomers"
          :precision="0"
          style="margin-right: 50px"
        >
          <template #suffix>
            <span class="text-sm text-gray-500 ml-2">
              较上月
              <trend :value="statistics.newCustomersGrowth" />
            </span>
          </template>
        </statistic>
      </a-card>
      <a-card>
        <statistic
          title="本月新增合同"
          :value="statistics.newContracts"
          :precision="0"
        >
          <template #suffix>
            <span class="text-sm text-gray-500 ml-2">
              较上月
              <trend :value="statistics.newContractsGrowth" />
            </span>
          </template>
        </statistic>
      </a-card>
      <a-card>
        <statistic
          title="本月销售额"
          :value="statistics.monthSales"
          :precision="2"
          :value-style="{ color: '#3f8600' }"
        >
          <template #prefix>¥</template>
          <template #suffix>
            <span class="text-sm text-gray-500 ml-2">
              较上月
              <trend :value="statistics.monthSalesGrowth" />
            </span>
          </template>
        </statistic>
      </a-card>
      <a-card>
        <statistic
          title="销售机会转化率"
          :value="statistics.conversionRate"
          :precision="2"
          suffix="%"
        >
          <template #suffix>
            <span class="text-sm text-gray-500 ml-2">
              较上月
              <trend :value="statistics.conversionRateGrowth" />
            </span>
          </template>
        </statistic>
      </a-card>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- 销售趋势图 -->
      <a-card title="销售趋势">
        <div ref="salesTrendChart" style="height: 300px"></div>
      </a-card>

      <!-- 销售漏斗图 -->
      <a-card title="销售漏斗">
        <div ref="salesFunnelChart" style="height: 300px"></div>
      </a-card>

      <!-- 客户行业分布 -->
      <a-card title="客户行业分布">
        <div ref="industryPieChart" style="height: 300px"></div>
      </a-card>

      <!-- 销售排行榜 -->
      <a-card title="销售排行榜">
        <a-table
          :columns="rankColumns"
          :data-source="salesRank"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'rank'">
              <template v-if="record.rank <= 3">
                <crown-outlined :style="{ color: getRankColor(record.rank) }" />
              </template>
              <template v-else>
                {{ record.rank }}
              </template>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, reactive, h } from 'vue';
import { Statistic } from 'ant-design-vue';
import { CrownOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts';

// 趋势组件
const Trend = (props: { value: number }) => {
  const { value } = props;
  if (value > 0) {
    return h('span', { class: 'text-red-500' }, `↑${value}%`);
  } else if (value < 0) {
    return h('span', { class: 'text-green-500' }, `↓${Math.abs(value)}%`);
  }
  return h('span', { class: 'text-gray-500' }, '-');
};

// 统计数据
const statistics = reactive({
  newCustomers: 128,
  newCustomersGrowth: 12.5,
  newContracts: 45,
  newContractsGrowth: -5.2,
  monthSales: 1258600,
  monthSalesGrowth: 8.3,
  conversionRate: 35.6,
  conversionRateGrowth: 2.1,
});

// 销售排行榜列
const rankColumns = [
  {
    title: '排名',
    dataIndex: 'rank',
    key: 'rank',
    width: 80,
  },
  {
    title: '销售员',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '销售额',
    dataIndex: 'amount',
    key: 'amount',
    align: 'right',
  },
  {
    title: '完成率',
    dataIndex: 'completion',
    key: 'completion',
    align: 'right',
  },
];

// 销售排行数据
const salesRank = ref([
  {
    rank: 1,
    name: '张三',
    amount: '¥1,256,000',
    completion: '125%',
  },
  {
    rank: 2,
    name: '李四',
    amount: '¥986,000',
    completion: '98%',
  },
  {
    rank: 3,
    name: '王五',
    amount: '¥856,000',
    completion: '85%',
  },
  {
    rank: 4,
    name: '赵六',
    amount: '¥756,000',
    completion: '75%',
  },
  {
    rank: 5,
    name: '钱七',
    amount: '¥656,000',
    completion: '65%',
  },
]);

// 排名颜色
function getRankColor(rank: number) {
  const colors = ['#f5c542', '#8c8c8c', '#c45a65'];
  return colors[rank - 1] || '#595959';
}

// 图表实例
const salesTrendChart = ref<HTMLElement | null>(null);
const salesFunnelChart = ref<HTMLElement | null>(null);
const industryPieChart = ref<HTMLElement | null>(null);

// 初始化销售趋势图
function initSalesTrendChart() {
  if (!salesTrendChart.value) return;
  const chart = echarts.init(salesTrendChart.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['合同金额', '目标金额'],
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '合同金额',
        type: 'line',
        data: [820, 932, 901, 934, 1290, 1330],
      },
      {
        name: '目标金额',
        type: 'line',
        data: [820, 932, 901, 934, 1290, 1330].map(v => v * 1.2),
      },
    ],
  });
}

// 初始化销售漏斗图
function initSalesFunnelChart() {
  if (!salesFunnelChart.value) return;
  const chart = echarts.init(salesFunnelChart.value);
  chart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c}%',
    },
    series: [
      {
        name: '销售漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          label: {
            fontSize: 20,
          },
        },
        data: [
          { value: 100, name: '线索获取' },
          { value: 80, name: '初步接触' },
          { value: 60, name: '需求确认' },
          { value: 40, name: '方案制定' },
          { value: 20, name: '商务谈判' },
        ],
      },
    ],
  });
}

// 初始化客户行业分布图
function initIndustryPieChart() {
  if (!industryPieChart.value) return;
  const chart = echarts.init(industryPieChart.value);
  chart.setOption({
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '行业分布',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '互联网' },
          { value: 735, name: '制造业' },
          { value: 580, name: '金融' },
          { value: 484, name: '教育' },
          { value: 300, name: '其他' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });
}

// 监听窗口大小变化，重绘图表
function handleResize() {
  const charts = [
    salesTrendChart.value && echarts.getInstanceByDom(salesTrendChart.value),
    salesFunnelChart.value && echarts.getInstanceByDom(salesFunnelChart.value),
    industryPieChart.value && echarts.getInstanceByDom(industryPieChart.value),
  ].filter(Boolean);

  charts.forEach(chart => chart?.resize());
}

onMounted(() => {
  // 初始化图表
  initSalesTrendChart();
  initSalesFunnelChart();
  initIndustryPieChart();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script> 