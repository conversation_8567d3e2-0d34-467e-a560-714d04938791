<template>
  <div class="login-container min-h-screen flex items-center justify-center bg-gray-100">
    <div class="login-content w-full max-w-md">
      <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- 登录头部 -->
        <div class="p-6 bg-primary-700 text-white text-center">
          <h1 class="text-2xl font-bold">业财一体化系统</h1>
          <p class="mt-2 text-primary-200">企业数字化管理平台</p>
        </div>
        
        <!-- 登录表单 -->
        <div class="p-8">
          <h2 class="text-xl font-semibold text-gray-700 text-center mb-6">账号登录</h2>
          
          <a-form
            :model="formState"
            name="login"
            @finish="onFinish"
            @finishFailed="onFinishFailed"
            :rules="rules"
          >
            <!-- 用户名 -->
            <a-form-item name="username">
              <a-input
                v-model:value="formState.username"
                size="large"
                placeholder="请输入用户名"
              >
                <template #prefix>
                  <user-outlined class="text-gray-400" />
                </template>
              </a-input>
            </a-form-item>
            
            <!-- 密码 -->
            <a-form-item name="password">
              <a-input-password
                v-model:value="formState.password"
                size="large"
                placeholder="请输入密码"
              >
                <template #prefix>
                  <lock-outlined class="text-gray-400" />
                </template>
              </a-input-password>
            </a-form-item>
            
            <!-- 记住密码 -->
            <div class="flex justify-between mb-4">
              <a-checkbox v-model:checked="formState.remember">记住密码</a-checkbox>
              <a class="text-primary-600 hover:text-primary-500">忘记密码？</a>
            </div>
            
            <!-- 登录按钮 -->
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                class="w-full"
                :loading="loading"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
          
          <!-- 其他登录方式 -->
          <div class="mt-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">其他登录方式</span>
              </div>
            </div>
            
            <div class="mt-6 flex justify-center space-x-6">
              <a-button shape="circle" class="flex items-center justify-center">
                <template #icon><wechat-outlined style="color: #07C160" /></template>
              </a-button>
              <a-button shape="circle" class="flex items-center justify-center">
                <template #icon><dingtalk-outlined style="color: #1890ff" /></template>
              </a-button>
              <a-button shape="circle" class="flex items-center justify-center">
                <template #icon><alipay-outlined style="color: #1677FF" /></template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 页脚 -->
      <div class="mt-6 text-center text-gray-500 text-sm">
        <p>© 2023 业财一体化系统 - 版权所有</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  UserOutlined,
  LockOutlined,
  WechatOutlined,
  DingtalkOutlined,
  AlipayOutlined
} from '@ant-design/icons-vue';

// 路由
const router = useRouter();

// 加载状态
const loading = ref(false);

// 表单数据
const formState = reactive({
  username: '',
  password: '',
  remember: true,
});

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度必须在3-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度必须在6-20个字符之间', trigger: 'blur' }
  ],
};

// 登录成功
const onFinish = (values: any) => {
  loading.value = true;
  
  // 模拟登录请求
  setTimeout(() => {
    if (values.username === 'admin' && values.password === 'admin123') {
      message.success('登录成功');
      
      // 存储登录状态
      localStorage.setItem('token', 'mock-token');
      localStorage.setItem('user', JSON.stringify({
        id: 1,
        username: 'admin',
        name: '管理员',
        avatar: '',
        roles: ['admin']
      }));
      
      // 跳转到首页
      router.push('/dashboard');
    } else {
      message.error('用户名或密码错误');
    }
    
    loading.value = false;
  }, 1500);
};

// 登录失败
const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo);
  message.error('请检查输入信息');
};
</script>

<style scoped>
.login-container {
  background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
