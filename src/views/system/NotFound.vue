<template>
  <div class="not-found-container min-h-screen flex flex-col items-center justify-center bg-gray-100 p-4">
    <div class="text-center">
      <!-- 404图标 -->
      <div class="mb-8">
        <img src="@/assets/images/404.svg" alt="404" class="w-64 h-64 mx-auto" />
      </div>
      
      <!-- 错误信息 -->
      <h1 class="text-6xl font-bold text-gray-800 mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-600 mb-6">页面未找到</h2>
      <p class="text-gray-500 mb-8 max-w-md mx-auto">
        抱歉，您访问的页面不存在或已被移除。请检查URL是否正确，或返回首页继续浏览。
      </p>
      
      <!-- 操作按钮 -->
      <div class="flex flex-wrap justify-center gap-4">
        <a-button type="primary" size="large" @click="goBack">
          <template #icon><arrow-left-outlined /></template>
          返回上一页
        </a-button>
        <a-button size="large" @click="goHome">
          <template #icon><home-outlined /></template>
          返回首页
        </a-button>
      </div>
    </div>
    
    <!-- 页脚 -->
    <div class="mt-auto pt-8 text-center text-gray-400">
      <p>© 2023 业财一体化系统 - 版权所有</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ArrowLeftOutlined, HomeOutlined } from '@ant-design/icons-vue';

const router = useRouter();

// 返回上一页
const goBack = () => {
  router.back();
};

// 返回首页
const goHome = () => {
  router.push('/dashboard');
};
</script>

<style scoped>
.not-found-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
