<template>
  <div class="bg-white p-6 rounded-lg">
    <!-- 基本信息 -->
    <div class="mb-6">
      <h2 class="text-lg font-medium mb-4">商机基本信息</h2>
      <a-descriptions :column="3">
        <a-descriptions-item label="客户名称">{{ opportunity.customerName }}</a-descriptions-item>
        <a-descriptions-item label="商机描述">{{ opportunity.description }}</a-descriptions-item>
        <a-descriptions-item label="预计金额">¥{{ opportunity.expectedAmount?.toLocaleString() }}</a-descriptions-item>
        <a-descriptions-item label="跟进状态">
          <a-progress :percent="opportunity.status" size="small" />
        </a-descriptions-item>
        <a-descriptions-item label="预计签约时间">{{ formatDate(opportunity.expectedDate) }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 操作按钮 -->
    <div class="mb-4">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          添加需求
        </a-button>
        <a-button @click="handleExport">
          <template #icon><export-outlined /></template>
          导出需求文档
        </a-button>
      </a-space>
    </div>

    <!-- 需求列表 -->
    <a-table
      :columns="columns"
      :data-source="requirements"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 优先级 -->
      <template #priority="{ text }">
        <a-tag :color="getPriorityColor(text)">{{ text }}</a-tag>
      </template>

      <!-- 状态 -->
      <template #status="{ text }">
        <a-tag :color="getStatusColor(text)">{{ text }}</a-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a @click="handleView(record)">查看</a>
          <a @click="handleEdit(record)">编辑</a>
          <a-popconfirm
            title="确定要删除此需求吗？"
            @confirm="handleDelete(record)"
            ok-text="确定"
            cancel-text="取消"
          >
            <a class="text-red-500">删除</a>
          </a-popconfirm>
        </a-space>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="800px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="需求标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入需求标题" />
        </a-form-item>
        <a-form-item label="需求类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择需求类型">
            <a-select-option value="功能">功能需求</a-select-option>
            <a-select-option value="性能">性能需求</a-select-option>
            <a-select-option value="安全">安全需求</a-select-option>
            <a-select-option value="其他">其他需求</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" name="priority">
          <a-select v-model:value="formData.priority" placeholder="请选择优先级">
            <a-select-option value="高">高</a-select-option>
            <a-select-option value="中">中</a-select-option>
            <a-select-option value="低">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="需求描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入需求描述" :rows="4" />
        </a-form-item>
        <a-form-item label="验收标准" name="acceptance">
          <a-textarea v-model:value="formData.acceptance" placeholder="请输入验收标准" :rows="4" />
        </a-form-item>
        <a-form-item label="备注" name="remarks">
          <a-textarea v-model:value="formData.remarks" placeholder="请输入备注信息" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal
      v-model:visible="viewModalVisible"
      title="需求详情"
      @cancel="() => viewModalVisible = false"
      :footer="null"
      width="800px"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="需求标题">{{ viewData.title }}</a-descriptions-item>
        <a-descriptions-item label="需求类型">{{ viewData.type }}</a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="getPriorityColor(viewData.priority)">{{ viewData.priority }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(viewData.status)">{{ viewData.status }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="需求描述">{{ viewData.description }}</a-descriptions-item>
        <a-descriptions-item label="验收标准">{{ viewData.acceptance }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{ viewData.remarks }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDate(viewData.createTime) }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ formatDate(viewData.updateTime) }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import {
  PlusOutlined,
  ExportOutlined,
} from '@ant-design/icons-vue';

const route = useRoute();
const opportunityId = route.params.id;

// 商机信息（模拟数据）
const opportunity = reactive({
  customerName: '杭州软件有限公司',
  description: '企业管理系统开发项目',
  expectedAmount: 500000,
  status: 60,
  expectedDate: '2024-06-30',
});

// 表格列定义
const columns = [
  {
    title: '需求标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
  },
  {
    title: '需求类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 100,
    slots: { customRender: 'priority' },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];

// 需求列表（模拟数据）
const requirements = ref([
  {
    id: 1,
    title: '用户管理模块',
    type: '功能',
    priority: '高',
    status: '待评估',
    description: '实现用户的增删改查功能，包括角色权限管理',
    acceptance: '1. 可以添加、编辑、删除用户\n2. 可以分配角色和权限\n3. 支持批量导入导出',
    remarks: '需要考虑数据安全性',
    createTime: '2024-03-15',
    updateTime: '2024-03-15',
  },
  {
    id: 2,
    title: '系统性能优化',
    type: '性能',
    priority: '中',
    status: '已评估',
    description: '优化系统响应速度，提升用户体验',
    acceptance: '1. 页面加载时间不超过2秒\n2. 数据库查询响应时间不超过1秒',
    remarks: '需要进行压力测试',
    createTime: '2024-03-16',
    updateTime: '2024-03-16',
  },
]);

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 新增/编辑表单相关
const modalVisible = ref(false);
const modalTitle = ref('新增需求');
const formRef = ref();
const formData = reactive({
  title: '',
  type: undefined,
  priority: undefined,
  description: '',
  acceptance: '',
  remarks: '',
});

// 查看详情相关
const viewModalVisible = ref(false);
const viewData = reactive({
  title: '',
  type: '',
  priority: '',
  status: '',
  description: '',
  acceptance: '',
  remarks: '',
  createTime: '',
  updateTime: '',
});

// 加载数据
onMounted(() => {
  // TODO: 根据 opportunityId 加载商机和需求数据
  console.log('加载商机ID:', opportunityId);
});

// 表单校验规则
const rules = {
  title: [{ required: true, message: '请输入需求标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择需求类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  description: [{ required: true, message: '请输入需求描述', trigger: 'blur' }],
  acceptance: [{ required: true, message: '请输入验收标准', trigger: 'blur' }],
};

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 获取优先级标签颜色
function getPriorityColor(priority: string) {
  const colors = {
    '高': 'error',
    '中': 'warning',
    '低': 'success',
  };
  return colors[priority] || 'default';
}

// 获取状态标签颜色
function getStatusColor(status: string) {
  const colors = {
    '待评估': 'default',
    '已评估': 'processing',
    '开发中': 'warning',
    '已完成': 'success',
    '已取消': 'error',
  };
  return colors[status] || 'default';
}

// 处理表格变化
function handleTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // TODO: 加载数据
}

// 处理新增
function handleAdd() {
  modalTitle.value = '新增需求';
  modalVisible.value = true;
}

// 处理编辑
function handleEdit(record: any) {
  modalTitle.value = '编辑需求';
  Object.assign(formData, record);
  modalVisible.value = true;
}

// 处理查看
function handleView(record: any) {
  Object.assign(viewData, record);
  viewModalVisible.value = true;
}

// 处理删除
function handleDelete(record: any) {
  // TODO: 实现删除逻辑
  message.success('删除功能待实现');
}

// 处理导出
function handleExport() {
  // TODO: 实现导出逻辑
  message.success('导出功能待实现');
}

// 处理弹窗确认
async function handleModalOk() {
  try {
    await formRef.value.validate();
    // TODO: 实现保存逻辑
    message.success('保存成功');
    modalVisible.value = false;
  } catch (error) {
    // 表单验证失败
  }
}

// 处理弹窗取消
function handleModalCancel() {
  formRef.value?.resetFields();
  modalVisible.value = false;
}
</script> 