<template>
  <div class="opportunity-list">
    <!-- 搜索区域 -->
    <a-card class="mb-4">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="商机名称">
          <a-input
            v-model:value="searchForm.name"
            placeholder="请输入商机名称"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="客户名称">
          <a-input
            v-model:value="searchForm.customerName"
            placeholder="请输入客户名称"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="客户编号">
          <a-input
            v-model:value="searchForm.customerCode"
            placeholder="请输入客户编号"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="跟进状态">
          <a-select
            v-model:value="searchForm.stage"
            placeholder="请选择状态"
            style="width: 200px"
            allow-clear
          >
            <a-select-option v-for="item in stageOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon><redo-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮和表格 -->
    <a-card>
      <template #title>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><plus-outlined /></template>
            新增商机
          </a-button>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :expandable="{
          expandedRowRender: expandedRowRender,
          expandRowByClick: false
        }"
      >
        <template #bodyCell="{ column, record }">
          <!-- 预计金额 -->
          <template v-if="column.key === 'expectedAmount'">
            ¥{{ formatMoney(record.expectedAmount) }}
          </template>

          <!-- 跟进状态 -->
          <template v-if="column.key === 'stage'">
            <a-tag :color="getStageColor(record.stage)">
              {{ getStageName(record.stage) }}
            </a-tag>
          </template>

          <!-- 预计签约时间 -->
          <template v-if="column.key === 'expectedTime'">
            {{ formatDate(record.expectedTime) }}
          </template>

          <!-- 操作 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-popconfirm
                title="确定要删除该商机吗？"
                @confirm="handleDelete(record)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑商机弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirm-loading="modalLoading"
      width="800px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="商机名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入商机名称" />
        </a-form-item>
        <a-form-item label="商机描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入商机描述"
            :rows="2"
          />
        </a-form-item>
        <a-form-item label="客户名称" name="customerId">
          <a-select
            v-model:value="formData.customerId"
            placeholder="请选择客户"
            show-search
            :filter-option="filterCustomerOption"
          >
            <a-select-option v-for="item in customerOptions" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="客户编号" name="customerCode">
          <a-input v-model:value="formData.customerCode" placeholder="请输入客户编号" disabled />
        </a-form-item>
        <a-form-item label="预计金额" name="expectedAmount">
          <a-input-number
            v-model:value="formData.expectedAmount"
            placeholder="请输入预计金额"
            :min="0"
            :precision="2"
            :style="{ width: '100%' }"
          />
        </a-form-item>
        <a-form-item label="跟进状态" name="stage">
          <a-select
            v-model:value="formData.stage"
            placeholder="请选择状态"
            :style="{ width: '100%' }"
          >
            <a-select-option v-for="item in stageOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="预计签约时间" name="expectedTime">
          <a-date-picker
            v-model:value="formData.expectedTime"
            placeholder="请选择预计签约时间"
            :style="{ width: '100%' }"
          />
        </a-form-item>
        <a-form-item label="商机来源" name="source">
          <a-select
            v-model:value="formData.source"
            placeholder="请选择来源"
            :style="{ width: '100%' }"
          >
            <a-select-option value="website">官网</a-select-option>
            <a-select-option value="referral">转介绍</a-select-option>
            <a-select-option value="exhibition">展会</a-select-option>
            <a-select-option value="advertisement">广告</a-select-option>
            <a-select-option value="social">社交媒体</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="负责人" name="ownerId">
          <a-select
            v-model:value="formData.ownerId"
            placeholder="请选择负责人"
            :style="{ width: '100%' }"
          >
            <a-select-option v-for="item in ownerOptions" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, h } from 'vue';
import { useRouter } from 'vue-router';
import { message, TableColumnType, FormInstance } from 'ant-design-vue';
import {
  SearchOutlined,
  RedoOutlined,
  PlusOutlined,
  ExportOutlined,
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { watch } from 'vue';

const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '商机名称',
    dataIndex: 'name',
    key: 'name',
    width: 180,
  },
  {
    title: '商机描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 150,
  },
  {
    title: '客户编号',
    dataIndex: 'customerCode',
    key: 'customerCode',
    width: 120,
  },
  {
    title: '预计金额',
    dataIndex: 'expectedAmount',
    key: 'expectedAmount',
    width: 120,
    align: 'right',
  },
  {
    title: '预计签约时间',
    dataIndex: 'expectedTime',
    key: 'expectedTime',
    width: 120,
  },
  {
    title: '跟进状态',
    dataIndex: 'stage',
    key: 'stage',
    width: 120,
  },
  {
    title: '负责人',
    dataIndex: 'ownerName',
    key: 'ownerName',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
  },
];

// 跟进状态选项
const stageOptions = [
  { label: '20%', value: '20' },
  { label: '40%', value: '40' },
  { label: '60%', value: '60' },
  { label: '80%', value: '80' },
  { label: '100%', value: '100' },
];

// 客户选项（模拟数据）
const customerOptions = [
  { id: '1', name: '客户A', code: 'CUST001' },
  { id: '2', name: '客户B', code: 'CUST002' },
  { id: '3', name: '客户C', code: 'CUST003' },
];

// 负责人选项（模拟数据）
const ownerOptions = [
  { id: '1', name: '张三' },
  { id: '2', name: '李四' },
  { id: '3', name: '王五' },
];

// 搜索表单
const searchForm = reactive({
  name: '',
  customerName: '',
  customerCode: '',
  stage: undefined,
});

// 表格数据
const loading = ref(false);
const tableData = ref<any[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 弹窗相关
const modalVisible = ref(false);
const modalTitle = ref('新增商机');
const modalLoading = ref(false);
const formRef = ref<FormInstance>();
const formData = reactive({
  id: '',
  name: '',
  description: '',
  customerId: undefined,
  customerCode: '',
  expectedAmount: undefined,
  stage: undefined,
  expectedTime: undefined,
  source: undefined,
  ownerId: undefined,
  remark: '',
});

// 表单校验规则
const formRules = {
  name: [{ required: true, message: '请输入商机名称', trigger: 'blur' }],
  description: [{ required: false, message: '请输入商机描述', trigger: 'blur' }],
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  expectedAmount: [{ required: true, message: '请输入预计金额', trigger: 'change' }],
  stage: [{ required: true, message: '请选择跟进状态', trigger: 'change' }],
  expectedTime: [{ required: true, message: '请选择预计签约时间', trigger: 'change' }],
  ownerId: [{ required: true, message: '请选择负责人', trigger: 'change' }],
};

// 格式化金额
function formatMoney(amount: number) {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 编辑跟进记录
function handleEditTracking(record: any, index: number) {
  record.editable = true;
}

// 删除跟进记录
function handleDeleteTracking(record: any, index: number) {
  // 这里应该调用删除接口
  message.success('删除成功');
}

// 保存跟进记录
function handleSaveTracking(row: any) {
  row.editable = false;
  message.success('保存成功');
}

// 取消编辑跟进记录  
function handleCancelTracking(row: any, index: number) {
  if (!row.trackingObject && !row.trackingResult) {
    // 如果是新添加的空记录，取消时删除
    // 这里应该获取当前展开行的跟进记录数据
    // 简化处理：暂不实现
  } else {
    row.editable = false;
  }
}

// 跟进记录相关
interface TrackingColumn {
  title: string;
  dataIndex?: string;
  key: string;
  width: number;
  customRender?: (options: { text: string, record: any, index: number }) => any;
}

const trackingColumns: TrackingColumn[] = [
  { title: '跟进时间', dataIndex: 'trackingTime', key: 'trackingTime', width: 150 },
  { title: '跟进人', dataIndex: 'trackingPerson', key: 'trackingPerson', width: 100 },
  { title: '跟进对象', dataIndex: 'trackingObject', key: 'trackingObject', width: 150 },
  { title: '跟进结果', dataIndex: 'trackingResult', key: 'trackingResult', width: 200 },
  { title: '其它说明', dataIndex: 'trackingRemark', key: 'trackingRemark', width: 200 },
  { 
    title: '操作', 
    key: 'trackingAction', 
    width: 150,
    customRender: ({ record, index }: { record: any, index: number }) => {
      if (record.editable) {
        return h('div', [
          h('a', {
            style: { marginRight: '10px' },
            onClick: () => handleSaveTracking(record)
          }, '保存'),
          h('a', {
            style: { color: '#ff4d4f' },
            onClick: () => handleCancelTracking(record, index)
          }, '取消')
        ]);
      } else {
        return h('div', [
          h('a', {
            style: { marginRight: '10px' },
            onClick: () => handleEditTracking(record, index)
          }, '编辑'),
          h('a', {
            style: { color: '#ff4d4f' },
            onClick: () => handleDeleteTracking(record, index)
          }, '删除')
        ]);
      }
    }
  }
];

// 获取跟进记录数据（模拟）
function getTrackingData(opportunityId: string) {
  return Array.from({ length: Math.floor(Math.random() * 5) + 1 }).map((_, index) => ({
    id: `${opportunityId}-track-${index}`,
    opportunityId,
    trackingTime: formatDate(dayjs().subtract(index, 'day').toDate()),
    trackingPerson: ownerOptions[Math.floor(Math.random() * ownerOptions.length)].name,
    trackingObject: `联系人${index + 1}`,
    trackingResult: `跟进结果${index + 1}`,
    trackingRemark: `备注说明${index + 1}`,
    editable: false
  }));
}

// 子表格渲染函数
function expandedRowRender(record: any) {
  const trackingData = ref(getTrackingData(record.id));
  
  const handleAddTracking = () => {
    trackingData.value.unshift({
      id: `${record.id}-track-${Date.now()}`,
      opportunityId: record.id,
      trackingTime: formatDate(new Date()),
      trackingPerson: ownerOptions[0].name,
      trackingObject: '',
      trackingResult: '',
      trackingRemark: '',
      editable: true
    });
  };
  
  return h('div', [
    h('div', { style: { marginBottom: '10px' } }, [
      h('a-button', {
        type: 'primary',
        size: 'small',
        onClick: handleAddTracking
      }, '新增跟进记录')
    ]),
    h('a-table', {
      columns: trackingColumns.map(col => {
        if (col.dataIndex && ['trackingTime', 'trackingPerson', 'trackingObject', 'trackingResult', 'trackingRemark'].includes(col.dataIndex)) {
          return {
            ...col,
            customRender: ({ text, record, index }: { text: string, record: any, index: number }) => {
              if (record.editable) {
                if (col.dataIndex === 'trackingTime') {
                  return h('a-date-picker', {
                    size: 'small',
                    value: dayjs(record[col.dataIndex]),
                    style: { width: '100%' },
                    onChange: (date: any) => {
                      if (col.dataIndex) {
                        record[col.dataIndex] = formatDate(date);
                      }
                    }
                  });
                } else if (col.dataIndex === 'trackingPerson') {
                  return h('a-select', {
                    size: 'small',
                    value: record[col.dataIndex],
                    style: { width: '100%' },
                    onChange: (value: string) => {
                      if (col.dataIndex) {
                        record[col.dataIndex] = value;
                      }
                    }
                  }, ownerOptions.map(owner => 
                    h('a-select-option', { key: owner.id, value: owner.name }, owner.name)
                  ));
                } else {
                  return h('a-input', {
                    size: 'small',
                    value: text,
                    onChange: (e: any) => {
                      if (col.dataIndex) {
                        record[col.dataIndex] = e.target.value;
                      }
                    }
                  });
                }
              } else {
                return text;
              }
            }
          };
        }
        return col;
      }),
      dataSource: trackingData.value,
      pagination: false,
      rowKey: 'id',
      size: 'small',
      bordered: true
    })
  ]);
}

// 获取阶段名称
function getStageName(stage: string) {
  const option = stageOptions.find(item => item.value === stage);
  return option?.label || '-';
}

// 获取阶段颜色
function getStageColor(stage: string) {
  const colorMap: Record<string, string> = {
    '20': 'default',
    '40': 'blue',
    '60': 'cyan',
    '80': 'orange',
    '100': 'green',
  };
  return colorMap[stage] || 'default';
}

// 格式化日期
function formatDate(date: string | Date) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 客户选项过滤
function filterCustomerOption(input: string, option: any) {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

// 客户选择时自动填充客户编号
watch(() => formData.customerId, (newValue) => {
  if (newValue) {
    const customer = customerOptions.find(item => item.id === newValue);
    if (customer) {
      formData.customerCode = customer.code;
    }
  } else {
    formData.customerCode = '';
  }
});

// 查询列表
async function fetchList() {
  loading.value = true;
  try {
    // TODO: 调用接口获取数据
    const mockData = Array.from({ length: 10 }).map((_, index) => ({
      id: `${index + 1}`,
      name: `销售商机${index + 1}`,
      description: `这是一个潜在的销售商机，客户有意向购买我们的产品${index + 1}`,
      customerName: `客户${String.fromCharCode(65 + index)}`,
      customerCode: `C${String(100000 + index).padStart(6, '0')}`,
      expectedAmount: (index + 1) * 50000,
      stage: stageOptions[index % stageOptions.length].value,
      expectedTime: '2024-06-30',
      ownerName: ownerOptions[index % ownerOptions.length].name,
    }));
    tableData.value = mockData;
    pagination.total = 100;
  } catch (error) {
    message.error('获取销售商机列表失败');
  } finally {
    loading.value = false;
  }
}

// 搜索
function handleSearch() {
  pagination.current = 1;
  fetchList();
}

// 重置
function handleReset() {
  searchForm.name = '';
  searchForm.customerName = '';
  searchForm.customerCode = '';
  searchForm.stage = undefined;
  handleSearch();
}

// 表格变化
function handleTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchList();
}

// 新增商机
function handleAdd() {
  modalTitle.value = '新增商机';
  formData.id = '';
  formData.name = '';
  formData.description = '';
  formData.customerId = undefined;
  formData.customerCode = '';
  formData.expectedAmount = undefined;
  formData.stage = undefined;
  formData.expectedTime = undefined;
  formData.source = undefined;
  formData.ownerId = undefined;
  formData.remark = '';
  modalVisible.value = true;
}

// 编辑商机
function handleEdit(record: any) {
  modalTitle.value = '编辑商机';
  Object.assign(formData, record);
  modalVisible.value = true;
}

// 查看商机
function handleView(record: any) {
  router.push(`/crm/opportunities/${record.id}`);
}

// 删除商机
async function handleDelete(record: any) {
  try {
    // TODO: 调用删除接口
    message.success('删除成功');
    fetchList();
  } catch (error) {
    message.error('删除失败');
  }
}

// 导出数据
function handleExport() {
  // TODO: 实现导出功能
  message.info('正在导出数据...');
}

// 弹窗确认
async function handleModalOk() {
  try {
    await formRef.value?.validate();
    modalLoading.value = true;
    // TODO: 调用新增/编辑接口
    message.success(formData.id ? '编辑成功' : '新增成功');
    modalVisible.value = false;
    fetchList();
  } catch (error) {
    // 表单校验失败
  } finally {
    modalLoading.value = false;
  }
}

// 弹窗取消
function handleModalCancel() {
  formRef.value?.resetFields();
  modalVisible.value = false;
}

onMounted(() => {
  fetchList();
});
</script> 