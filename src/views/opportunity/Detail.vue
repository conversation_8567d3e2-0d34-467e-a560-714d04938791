<template>
  <div class="opportunity-detail">
    <!-- 页面头部 -->
    <div class="mb-4 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <a-button @click="router.back()">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
        <h2 class="text-xl font-bold m-0">{{ opportunityInfo.name }}</h2>
        <a-tag :color="getStageColor(opportunityInfo.stage)">
          {{ getStageName(opportunityInfo.stage) }}
        </a-tag>
      </div>
      <div class="flex gap-2">
        <a-button type="primary" @click="handleEdit">
          <template #icon><edit-outlined /></template>
          编辑机会
        </a-button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 基本信息 -->
      <a-card title="基本信息" class="lg:col-span-2">
        <a-descriptions :column="2">
          <a-descriptions-item label="机会名称">
            {{ opportunityInfo.name }}
          </a-descriptions-item>
          <a-descriptions-item label="客户名称">
            {{ opportunityInfo.customerName }}
          </a-descriptions-item>
          <a-descriptions-item label="预计金额">
            ¥{{ formatMoney(opportunityInfo.expectedAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="销售阶段">
            <a-tag :color="getStageColor(opportunityInfo.stage)">
              {{ getStageName(opportunityInfo.stage) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="成功率">
            <a-progress
              :percent="opportunityInfo.probability"
              size="small"
              :stroke-color="getProbabilityColor(opportunityInfo.probability)"
            />
          </a-descriptions-item>
          <a-descriptions-item label="预计成交时间">
            {{ formatDate(opportunityInfo.expectedTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="机会来源">
            {{ getSourceName(opportunityInfo.source) }}
          </a-descriptions-item>
          <a-descriptions-item label="负责人">
            {{ opportunityInfo.ownerName }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(opportunityInfo.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDate(opportunityInfo.updateTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ opportunityInfo.remark || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 统计信息 -->
      <a-card title="统计信息">
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <div class="text-gray-500">跟进次数</div>
            <div class="text-2xl font-bold mt-2">{{ statistics.followUpCount }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500">最近跟进</div>
            <div class="text-sm mt-2">{{ formatDate(statistics.lastFollowUpTime) }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500">竞争对手</div>
            <div class="text-2xl font-bold mt-2">{{ statistics.competitorCount }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500">已推进天数</div>
            <div class="text-2xl font-bold mt-2">{{ statistics.progressDays }}天</div>
          </div>
        </div>
      </a-card>

      <!-- 跟进记录 -->
      <a-card title="跟进记录" class="lg:col-span-2">
        <template #extra>
          <a-button type="link" @click="handleAddFollowUp">
            添加跟进
          </a-button>
        </template>
        <a-timeline>
          <a-timeline-item v-for="item in followUpRecords" :key="item.id">
            <template #dot>
              <a-avatar size="small">{{ item.creator[0] }}</a-avatar>
            </template>
            <div class="mb-2">{{ item.content }}</div>
            <div class="text-gray-400 text-sm">
              {{ item.creator }} - {{ formatDate(item.createTime) }}
              <a-tag class="ml-2" :color="getFollowUpMethodColor(item.method)">
                {{ getFollowUpMethodName(item.method) }}
              </a-tag>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>

      <!-- 竞争对手 -->
      <a-card title="竞争对手">
        <template #extra>
          <a-button type="link" @click="handleAddCompetitor">
            添加竞争对手
          </a-button>
        </template>
        <a-list size="small" :data-source="competitors">
          <template #renderItem="{ item }">
            <a-list-item>
              <div class="w-full">
                <div class="flex justify-between items-center">
                  <span class="font-medium">{{ item.name }}</span>
                  <a-tag :color="getCompetitorStatusColor(item.status)">
                    {{ getCompetitorStatusName(item.status) }}
                  </a-tag>
                </div>
                <div class="text-gray-500 text-sm mt-1">
                  优势：{{ item.advantage }}
                </div>
                <div class="text-gray-500 text-sm">
                  劣势：{{ item.disadvantage }}
                </div>
              </div>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>

    <!-- 添加跟进记录弹窗 -->
    <a-modal
      v-model:visible="followUpModalVisible"
      title="添加跟进记录"
      @ok="handleFollowUpModalOk"
      @cancel="handleFollowUpModalCancel"
      :confirm-loading="followUpModalLoading"
    >
      <a-form
        ref="followUpFormRef"
        :model="followUpForm"
        :rules="followUpFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="跟进内容" name="content">
          <a-textarea
            v-model:value="followUpForm.content"
            placeholder="请输入跟进内容"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="跟进方式" name="method">
          <a-select
            v-model:value="followUpForm.method"
            placeholder="请选择跟进方式"
            :style="{ width: '100%' }"
          >
            <a-select-option value="phone">电话</a-select-option>
            <a-select-option value="visit">拜访</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="meeting">会议</a-select-option>
            <a-select-option value="wechat">微信</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="下次跟进时间" name="nextTime">
          <a-date-picker
            v-model:value="followUpForm.nextTime"
            :style="{ width: '100%' }"
          />
        </a-form-item>
        <a-form-item label="更新阶段" name="stage">
          <a-select
            v-model:value="followUpForm.stage"
            placeholder="请选择销售阶段"
            :style="{ width: '100%' }"
          >
            <a-select-option value="">保持不变</a-select-option>
            <a-select-option v-for="item in stageOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="更新成功率" name="probability">
          <a-slider
            v-model:value="followUpForm.probability"
            :marks="{
              0: '0%',
              25: '25%',
              50: '50%',
              75: '75%',
              100: '100%'
            }"
            :step="5"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加竞争对手弹窗 -->
    <a-modal
      v-model:visible="competitorModalVisible"
      title="添加竞争对手"
      @ok="handleCompetitorModalOk"
      @cancel="handleCompetitorModalCancel"
      :confirm-loading="competitorModalLoading"
    >
      <a-form
        ref="competitorFormRef"
        :model="competitorForm"
        :rules="competitorFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="公司名称" name="name">
          <a-input v-model:value="competitorForm.name" placeholder="请输入公司名称" />
        </a-form-item>
        <a-form-item label="竞争状态" name="status">
          <a-select
            v-model:value="competitorForm.status"
            placeholder="请选择竞争状态"
            :style="{ width: '100%' }"
          >
            <a-select-option value="leading">领先</a-select-option>
            <a-select-option value="equal">平等</a-select-option>
            <a-select-option value="behind">落后</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="产品优势" name="advantage">
          <a-textarea
            v-model:value="competitorForm.advantage"
            placeholder="请输入产品优势"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="产品劣势" name="disadvantage">
          <a-textarea
            v-model:value="competitorForm.disadvantage"
            placeholder="请输入产品劣势"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  LeftOutlined,
  EditOutlined,
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();

// 销售机会信息
const opportunityInfo = ref({
  id: '',
  name: '示例销售机会',
  customerName: '示例客户',
  expectedAmount: 100000,
  stage: 'requirement',
  probability: 60,
  expectedTime: '2024-06-30',
  source: 'website',
  ownerName: '张三',
  createTime: '2024-01-01 12:00:00',
  updateTime: '2024-01-02 15:30:00',
  remark: '这是一个示例销售机会',
});

// 统计信息
const statistics = ref({
  followUpCount: 5,
  lastFollowUpTime: '2024-01-15',
  competitorCount: 2,
  progressDays: 15,
});

// 跟进记录
const followUpRecords = ref([
  {
    id: '1',
    content: '电话沟通需求细节',
    creator: '张三',
    createTime: '2024-01-15 14:30:00',
    method: 'phone',
  },
  {
    id: '2',
    content: '上门拜访客户',
    creator: '李四',
    createTime: '2024-01-10 10:20:00',
    method: 'visit',
  },
]);

// 竞争对手
const competitors = ref([
  {
    id: '1',
    name: '竞争对手A',
    status: 'equal',
    advantage: '价格优势',
    disadvantage: '功能较少',
  },
  {
    id: '2',
    name: '竞争对手B',
    status: 'behind',
    advantage: '知名度高',
    disadvantage: '服务响应慢',
  },
]);

// 销售阶段选项
const stageOptions = [
  { label: '初步接触', value: 'initial' },
  { label: '需求确认', value: 'requirement' },
  { label: '方案制定', value: 'proposal' },
  { label: '商务谈判', value: 'negotiation' },
  { label: '合同签订', value: 'contract' },
];

// 跟进记录弹窗
const followUpModalVisible = ref(false);
const followUpModalLoading = ref(false);
const followUpFormRef = ref();
const followUpForm = reactive({
  content: '',
  method: undefined,
  nextTime: undefined,
  stage: '',
  probability: undefined,
});

// 跟进记录表单校验规则
const followUpFormRules = {
  content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
  method: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
  nextTime: [{ required: true, message: '请选择下次跟进时间', trigger: 'change' }],
};

// 竞争对手弹窗
const competitorModalVisible = ref(false);
const competitorModalLoading = ref(false);
const competitorFormRef = ref();
const competitorForm = reactive({
  name: '',
  status: undefined,
  advantage: '',
  disadvantage: '',
});

// 竞争对手表单校验规则
const competitorFormRules = {
  name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择竞争状态', trigger: 'change' }],
  advantage: [{ required: true, message: '请输入产品优势', trigger: 'blur' }],
  disadvantage: [{ required: true, message: '请输入产品劣势', trigger: 'blur' }],
};

// 获取阶段名称
function getStageName(stage: string) {
  const option = stageOptions.find(item => item.value === stage);
  return option?.label || '-';
}

// 获取阶段颜色
function getStageColor(stage: string) {
  const colorMap: Record<string, string> = {
    initial: 'default',
    requirement: 'blue',
    proposal: 'cyan',
    negotiation: 'orange',
    contract: 'green',
  };
  return colorMap[stage] || 'default';
}

// 获取成功率颜色
function getProbabilityColor(probability: number) {
  if (probability >= 80) return '#52c41a';
  if (probability >= 60) return '#1890ff';
  if (probability >= 40) return '#faad14';
  if (probability >= 20) return '#ff7a45';
  return '#ff4d4f';
}

// 获取来源名称
function getSourceName(source: string) {
  const sourceMap: Record<string, string> = {
    website: '官网',
    referral: '转介绍',
    exhibition: '展会',
    advertisement: '广告',
    social: '社交媒体',
    other: '其他',
  };
  return sourceMap[source] || source;
}

// 获取跟进方式名称
function getFollowUpMethodName(method: string) {
  const methodMap: Record<string, string> = {
    phone: '电话',
    visit: '拜访',
    email: '邮件',
    meeting: '会议',
    wechat: '微信',
    other: '其他',
  };
  return methodMap[method] || method;
}

// 获取跟进方式颜色
function getFollowUpMethodColor(method: string) {
  const colorMap: Record<string, string> = {
    phone: 'blue',
    visit: 'green',
    email: 'purple',
    meeting: 'orange',
    wechat: 'cyan',
    other: 'default',
  };
  return colorMap[method] || 'default';
}

// 获取竞争对手状态名称
function getCompetitorStatusName(status: string) {
  const statusMap: Record<string, string> = {
    leading: '领先',
    equal: '平等',
    behind: '落后',
  };
  return statusMap[status] || status;
}

// 获取竞争对手状态颜色
function getCompetitorStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    leading: 'green',
    equal: 'blue',
    behind: 'red',
  };
  return colorMap[status] || 'default';
}

// 格式化日期
function formatDate(date: string) {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD');
}

// 格式化金额
function formatMoney(amount: number) {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 编辑机会
function handleEdit() {
  router.push(`/opportunities/${opportunityInfo.value.id}/edit`);
}

// 添加跟进记录
function handleAddFollowUp() {
  followUpForm.content = '';
  followUpForm.method = undefined;
  followUpForm.nextTime = undefined;
  followUpForm.stage = '';
  followUpForm.probability = undefined;
  followUpModalVisible.value = true;
}

// 提交跟进记录
async function handleFollowUpModalOk() {
  try {
    await followUpFormRef.value.validate();
    followUpModalLoading.value = true;
    // TODO: 调用添加跟进记录接口
    message.success('添加跟进记录成功');
    followUpModalVisible.value = false;
    // 重新获取跟进记录列表
  } catch (error) {
    // 表单校验失败
  } finally {
    followUpModalLoading.value = false;
  }
}

// 取消添加跟进记录
function handleFollowUpModalCancel() {
  followUpFormRef.value?.resetFields();
  followUpModalVisible.value = false;
}

// 添加竞争对手
function handleAddCompetitor() {
  competitorForm.name = '';
  competitorForm.status = undefined;
  competitorForm.advantage = '';
  competitorForm.disadvantage = '';
  competitorModalVisible.value = true;
}

// 提交竞争对手
async function handleCompetitorModalOk() {
  try {
    await competitorFormRef.value.validate();
    competitorModalLoading.value = true;
    // TODO: 调用添加竞争对手接口
    message.success('添加竞争对手成功');
    competitorModalVisible.value = false;
    // 重新获取竞争对手列表
  } catch (error) {
    // 表单校验失败
  } finally {
    competitorModalLoading.value = false;
  }
}

// 取消添加竞争对手
function handleCompetitorModalCancel() {
  competitorFormRef.value?.resetFields();
  competitorModalVisible.value = false;
}

// 获取机会详情
async function fetchOpportunityDetail() {
  const id = route.params.id;
  // TODO: 调用接口获取机会详情
}

onMounted(() => {
  fetchOpportunityDetail();
});
</script> 