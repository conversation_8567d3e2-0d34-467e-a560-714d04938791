<template>
  <div class="bg-white p-6 rounded-lg">
    <!-- 基本信息 -->
    <div class="mb-6">
      <h2 class="text-lg font-medium mb-4">商机基本信息</h2>
      <a-descriptions :column="3">
        <a-descriptions-item label="客户名称">{{ opportunity.customerName }}</a-descriptions-item>
        <a-descriptions-item label="商机描述">{{ opportunity.description }}</a-descriptions-item>
        <a-descriptions-item label="预计金额">¥{{ opportunity.expectedAmount?.toLocaleString() }}</a-descriptions-item>
        <a-descriptions-item label="跟进状态">
          <a-progress :percent="opportunity.status" size="small" />
        </a-descriptions-item>
        <a-descriptions-item label="预计签约时间">{{ formatDate(opportunity.expectedDate) }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 操作按钮 -->
    <div class="mb-4">
      <a-button type="primary" @click="handleAdd">
        <template #icon><plus-outlined /></template>
        添加跟进记录
      </a-button>
    </div>

    <!-- 跟进记录时间轴 -->
    <a-timeline>
      <a-timeline-item v-for="record in trackingRecords" :key="record.id" :color="getMethodColor(record.method)">
        <template #dot>
          <component :is="getMethodIcon(record.method)" />
        </template>
        <div class="mb-2">
          <span class="font-medium">{{ record.method }}</span>
          <span class="text-gray-400 ml-4">{{ formatDate(record.createTime) }}</span>
        </div>
        <div class="text-gray-600 mb-2">{{ record.content }}</div>
        <div class="text-gray-400">
          下次跟进时间：{{ formatDate(record.nextDate) }}
        </div>
      </a-timeline-item>
    </a-timeline>

    <!-- 添加跟进记录弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      title="添加跟进记录"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="跟进内容" name="content">
          <a-textarea v-model:value="formData.content" placeholder="请输入跟进内容" :rows="4" />
        </a-form-item>
        <a-form-item label="跟进方式" name="method">
          <a-select v-model:value="formData.method" placeholder="请选择跟进方式">
            <a-select-option value="电话">电话</a-select-option>
            <a-select-option value="邮件">邮件</a-select-option>
            <a-select-option value="拜访">拜访</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="下次跟进" name="nextDate">
          <a-date-picker
            v-model:value="formData.nextDate"
            placeholder="请选择下次跟进时间"
            style="width: 200px"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import {
  PlusOutlined,
  PhoneOutlined,
  MailOutlined,
  TeamOutlined,
  EllipsisOutlined,
} from '@ant-design/icons-vue';

const route = useRoute();
const opportunityId = route.params.id;

// 商机信息（模拟数据）
const opportunity = reactive({
  customerName: '杭州软件有限公司',
  description: '企业管理系统开发项目',
  expectedAmount: 500000,
  status: 60,
  expectedDate: '2024-06-30',
});

// 跟进记录（模拟数据）
const trackingRecords = ref([
  {
    id: 1,
    method: '电话',
    content: '与客户进行了初步沟通，了解了基本需求',
    createTime: '2024-03-15',
    nextDate: '2024-03-20',
  },
  {
    id: 2,
    method: '拜访',
    content: '进行了现场拜访，深入了解了客户的具体需求和痛点',
    createTime: '2024-03-20',
    nextDate: '2024-03-25',
  },
]);

// 弹窗相关
const modalVisible = ref(false);
const formRef = ref();
const formData = reactive({
  content: '',
  method: undefined,
  nextDate: undefined,
});

// 表单校验规则
const rules = {
  content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
  method: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
  nextDate: [{ required: true, message: '请选择下次跟进时间', trigger: 'change' }],
};

// 加载数据
onMounted(() => {
  // TODO: 根据 opportunityId 加载商机和跟进记录数据
  console.log('加载商机ID:', opportunityId);
});

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 获取跟进方式对应的颜色
function getMethodColor(method: string) {
  const colors = {
    '电话': 'blue',
    '邮件': 'purple',
    '拜访': 'green',
    '其他': 'gray',
  };
  return colors[method] || 'blue';
}

// 获取跟进方式对应的图标
function getMethodIcon(method: string) {
  const icons = {
    '电话': PhoneOutlined,
    '邮件': MailOutlined,
    '拜访': TeamOutlined,
    '其他': EllipsisOutlined,
  };
  return icons[method] || EllipsisOutlined;
}

// 处理添加
function handleAdd() {
  modalVisible.value = true;
}

// 处理弹窗确认
async function handleModalOk() {
  try {
    await formRef.value.validate();
    // TODO: 实现保存逻辑
    message.success('跟进记录已添加');
    modalVisible.value = false;
  } catch (error) {
    // 表单验证失败
  }
}

// 处理弹窗取消
function handleModalCancel() {
  formRef.value?.resetFields();
  modalVisible.value = false;
}
</script> 