<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-indigo-200 rounded-full opacity-20 animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
    </div>
    
    <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-xl relative z-10 animate-fadeInUp">
      <!-- Logo和标题 -->
      <div class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
        </div>
        <h2 class="text-3xl font-bold text-gray-900">
          欢迎登录
        </h2>
        <p class="mt-2 text-lg font-medium text-blue-600">
          业财一体化
        </p>
        <p class="mt-1 text-sm text-gray-600">
          企业数字化管理平台
        </p>
      </div>

      <!-- 登录表单 -->
      <a-form
        :model="formState"
        @finish="handleFinish"
        class="mt-8 space-y-6"
      >
        <div class="space-y-4">
          <!-- 用户名 -->
          <a-form-item
            name="username"
            :rules="[{ required: true, message: '请输入用户名' }]"
            class="mb-4"
          >
            <a-input
              v-model:value="formState.username"
              size="large"
              placeholder="请输入用户名"
              class="rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500"
            >
              <template #prefix>
                <user-outlined class="text-gray-400" />
              </template>
            </a-input>
          </a-form-item>

          <!-- 密码 -->
          <a-form-item
            name="password"
            :rules="[{ required: true, message: '请输入密码' }]"
            class="mb-4"
          >
            <a-input-password
              v-model:value="formState.password"
              size="large"
              placeholder="请输入密码"
              class="rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500"
            >
              <template #prefix>
                <lock-outlined class="text-gray-400" />
              </template>
            </a-input-password>
          </a-form-item>

          <!-- 验证码 -->
          <a-form-item
            name="captcha"
            :rules="[{ required: true, message: '请输入验证码' }]"
            class="mb-4"
          >
            <a-row :gutter="8">
              <a-col :span="16">
                <a-input
                  v-model:value="formState.captcha"
                  size="large"
                  placeholder="请输入验证码"
                  class="rounded-lg border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                >
                  <template #prefix>
                    <safety-outlined class="text-gray-400" />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="8">
                <div class="h-10 bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-300 transition-colors"
                     @click="refreshCaptcha">
                  <span class="text-lg font-bold text-gray-600">1234</span>
                </div>
              </a-col>
            </a-row>
          </a-form-item>
        </div>

        <!-- 记住我和忘记密码 -->
        <div class="flex items-center justify-between mb-6">
          <a-checkbox v-model:checked="formState.remember" class="text-gray-600">
            记住我
          </a-checkbox>
          <a class="text-sm text-blue-600 hover:text-blue-800 font-medium" @click="handleForgotPassword">
            忘记密码？
          </a>
        </div>

        <!-- 登录按钮 -->
        <div>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            class="relative w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 border-0 rounded-lg font-medium text-base hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            立即登录
          </a-button>
        </div>
      </a-form>

      <!-- 页脚信息 -->
      <div class="mt-8 text-center">
        <div class="text-sm text-gray-500 mb-2">
          其他登录方式
        </div>
        <div class="flex justify-center space-x-4 mb-6">
          <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-green-600 transition-colors social-login-btn" title="微信登录">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          </div>
          <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-600 transition-colors social-login-btn" title="钉钉登录">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
            </svg>
          </div>
          <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-orange-600 transition-colors social-login-btn" title="企业邮箱登录">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"/>
            </svg>
          </div>
        </div>
        
        <div class="border-t border-gray-200 pt-4">
          <p class="text-xs text-gray-500">
            © 2024 业财一体化平台 - 企业数字化管理解决方案
          </p>
          <p class="text-xs text-gray-400 mt-1">
            版本 v1.0.0 | 技术支持：IT部门
          </p>
        </div>
      </div>
    </div>

    <!-- 忘记密码弹窗 -->
    <a-modal
      v-model:visible="forgotPasswordVisible"
      title="重置密码"
      @ok="handleResetPassword"
      :confirmLoading="resetPasswordLoading"
    >
      <a-form :model="resetForm" layout="vertical">
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input v-model:value="resetForm.username" />
        </a-form-item>
        <a-form-item
          label="邮箱"
          name="email"
          :rules="[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]"
        >
          <a-input v-model:value="resetForm.email" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button key="back" @click="forgotPasswordVisible = false">
          取消
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="resetPasswordLoading"
          @click="handleResetPassword"
        >
          发送重置邮件
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  UserOutlined,
  LockOutlined,
  SafetyOutlined,
} from '@ant-design/icons-vue';

const router = useRouter();

// 登录表单数据
const formState = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: true,
});

// 重置密码表单数据
const resetForm = reactive({
  username: '',
  email: '',
});

// 状态变量
const loading = ref(false);
const captchaCode = ref('1234');
const forgotPasswordVisible = ref(false);
const resetPasswordLoading = ref(false);

// 刷新验证码
function refreshCaptcha() {
  // 生成新的4位验证码
  const codes = ['1234', '5678', '9012', 'ABCD', 'EFGH', 'XYZW', '2468', '1357'];
  const randomIndex = Math.floor(Math.random() * codes.length);
  captchaCode.value = codes[randomIndex];
  
  // 清空用户输入的验证码
  formState.captcha = '';
}

// 处理登录
async function handleFinish(values: any) {
  try {
    loading.value = true;
    
    // 验证用户名和密码
    if (values.username !== 'admin') {
      throw new Error('用户名不正确');
    }
    
    if (values.password !== 'abc_admin!23$') {
      throw new Error('密码不正确');
    }
    
    if (values.captcha !== '1234') {
      throw new Error('验证码不正确');
    }
    
    // 模拟登录接口调用延迟
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    message.success('登录成功，欢迎使用业财一体化平台！');
    
    // 存储登录状态
    localStorage.setItem('token', 'ycyth_' + Date.now());
    localStorage.setItem('user', JSON.stringify({
      id: 1,
      username: 'admin',
      name: '系统管理员',
      avatar: '',
      role: 'admin',
      permissions: ['*'],
      loginTime: new Date().toISOString()
    }));
    
    // 跳转到首页
    router.push('/');
  } catch (error: any) {
    message.error(error.message || '登录失败，请检查用户名和密码');
    // 刷新验证码
    refreshCaptcha();
    // 清空密码字段
    formState.password = '';
    formState.captcha = '';
  } finally {
    loading.value = false;
  }
}

// 打开忘记密码弹窗
function handleForgotPassword() {
  resetForm.username = '';
  resetForm.email = '';
  forgotPasswordVisible.value = true;
}

// 处理重置密码
async function handleResetPassword() {
  try {
    resetPasswordLoading.value = true;
    // TODO: 调用重置密码接口
    // await resetPassword(resetForm);
    message.success('重置密码邮件已发送，请查收');
    forgotPasswordVisible.value = false;
  } catch (error: any) {
    message.error(error.message || '发送重置邮件失败');
  } finally {
    resetPasswordLoading.value = false;
  }
}
</script>

<style scoped>
.site-form-item-icon {
  color: rgba(0, 0, 0, 0.25);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* 自定义输入框样式 */
:deep(.ant-input) {
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  transition: all 0.2s;
}

:deep(.ant-input:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.ant-input-password) {
  border-radius: 0.5rem;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  transition: all 0.2s;
}

:deep(.ant-input-affix-wrapper:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.ant-input-affix-wrapper-focused) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 登录按钮hover效果 */
:deep(.ant-btn-primary) {
  background: linear-gradient(to right, #2563eb, #4f46e5) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  font-weight: 500;
  transition: all 0.2s;
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(to right, #1d4ed8, #4338ca) !important;
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3) !important;
}

/* 第三方登录按钮动画 */
.social-login-btn {
  transition: all 0.3s;
}

.social-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 复选框样式 */
:deep(.ant-checkbox-wrapper) {
  color: #6b7280;
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

/* 表单项间距 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-radius: 0.5rem 0.5rem 0 0;
}

:deep(.ant-modal-content) {
  border-radius: 0.5rem;
  overflow: hidden;
}
</style> 