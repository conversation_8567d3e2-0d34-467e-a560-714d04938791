<template>
  <div class="file-versions-wrapper">
    <a-card :bordered="false">
      <template #title>
        <div class="flex items-center">
          <router-link to="/files/index" class="mr-2">
            <a-button><left-outlined /> 返回</a-button>
          </router-link>
          <span class="text-lg font-medium">文件版本历史</span>
          <span v-if="currentFile" class="ml-2 text-gray-500">({{ currentFile.name }})</span>
        </div>
      </template>
      
      <!-- Embed the FileVersionsModal component directly -->
      <div class="versions-container">
        <a-spin :spinning="loading" tip="加载文件信息...">
          <template v-if="fileId">
            <FileVersionsModal
              v-model="modalVisible"
              :file-id="fileId"
              @update:modelValue="handleClose"
            />
          </template>
          <a-empty v-else description="未找到文件ID" />
        </a-spin>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { LeftOutlined } from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import FileVersionsModal from './components/FileVersionsModal.vue';

const route = useRoute();
const router = useRouter();
const fileStore = useFileStore();
const loading = ref(true);
const modalVisible = ref(true);

// Get file ID from route params
const fileId = computed(() => route.params.id as string);

// Get current file info
const currentFile = computed(() => fileStore.fileById(fileId.value));

// Load file data on mount
onMounted(async () => {
  if (fileId.value) {
    try {
      await fileStore.fetchFileById(fileId.value);
      loading.value = false;
    } catch (error) {
      console.error('Failed to load file:', error);
      message.error('文件加载失败');
      loading.value = false;
    }
  } else {
    message.error('无效的文件ID');
    loading.value = false;
  }
});

// Handle modal close
const handleClose = (isVisible: boolean) => {
  if (!isVisible) {
    router.push('/files/index');
  }
};
</script>

<style scoped>
.file-versions-wrapper {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.versions-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}
</style> 