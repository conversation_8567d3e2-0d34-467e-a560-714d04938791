<template>
  <div class="file-upload-wrapper">
    <a-card :bordered="false">
      <template #title>
        <div class="flex items-center">
          <router-link to="/files/index" class="mr-2">
            <a-button><left-outlined /> 返回</a-button>
          </router-link>
          <span class="text-lg font-medium">上传文件</span>
        </div>
      </template>
      
      <!-- Embed the FileUploadModal component directly -->
      <div class="upload-container">
        <FileUploadModal
          v-model="visible"
          @update:modelValue="handleClose"
          @success="handleUploadSuccess"
        />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { LeftOutlined } from '@ant-design/icons-vue';
import FileUploadModal from './components/FileUploadModal.vue';

const router = useRouter();
const visible = ref(true);

// Handle modal close
const handleClose = (isVisible: boolean) => {
  if (!isVisible) {
    router.push('/files/index');
  }
};

// Handle upload success
const handleUploadSuccess = () => {
  message.success('文件上传成功');
  router.push('/files/index');
};
</script>

<style scoped>
.file-upload-wrapper {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}
</style> 