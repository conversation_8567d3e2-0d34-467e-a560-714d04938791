<template>
  <div class="file-management-container animate-fadeIn">
    <!-- 页面标题区域 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">文件管理中心</h2>
          <p class="text-gray-500 mt-1">管理企业法律文件、证书和知识产权</p>
        </div>
        <div class="space-x-2">
          <a-button type="primary" @click="showUploadModal">
            <template #icon><upload-outlined /></template>
            上传文件
          </a-button>
          <a-button @click="showPermissionSettings">
            <template #icon><setting-outlined /></template>
            权限设置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="mb-6 statistics-card-wrapper">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="statistics-card">
            <template #title>
              <span class="text-lg">文件总数</span>
            </template>
            <div class="text-center">
              <p class="text-3xl font-bold text-primary">
                {{ statistics?.totalCount || 0 }}
              </p>
              <p class="text-gray-500">
                总容量: {{ formatFileSize(statistics?.totalSize || 0) }}
              </p>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="statistics-card">
            <template #title>
              <span class="text-lg">即将到期文件</span>
            </template>
            <div class="text-center">
              <p class="text-3xl font-bold text-warning">
                {{ statistics?.expiringCount || 0 }}
              </p>
              <p class="text-gray-500">
                需要及时续期
              </p>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="statistics-card">
            <template #title>
              <span class="text-lg">工商档案</span>
            </template>
            <div class="text-center">
              <p class="text-3xl font-bold text-success">
                {{ statistics?.categoryCount?.business || 0 }}
              </p>
              <p class="text-gray-500">
                最近更新: {{ lastUpdatedTime(FileCategory.BUSINESS) }}
              </p>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="statistics-card">
            <template #title>
              <span class="text-lg">知识产权</span>
            </template>
            <div class="text-center">
              <p class="text-3xl font-bold text-info">
                {{ statistics?.categoryCount?.intellectual || 0 }}
              </p>
              <p class="text-gray-500">
                最近更新: {{ lastUpdatedTime(FileCategory.INTELLECTUAL) }}
              </p>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索区域 -->
    <a-card :bordered="false" class="mb-6">
      <a-form layout="vertical" :model="searchForm">
        <a-row :gutter="16">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="关键词搜索">
              <a-input
                v-model:value="searchForm.keyword"
                placeholder="输入文件名或编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="文件类型">
              <a-select
                v-model:value="searchForm.fileType"
                placeholder="选择文件类型"
                allow-clear
              >
                <a-select-option :value="FileType.PDF">PDF文档</a-select-option>
                <a-select-option :value="FileType.WORD">Word文档</a-select-option>
                <a-select-option :value="FileType.EXCEL">Excel表格</a-select-option>
                <a-select-option :value="FileType.IMAGE">图片</a-select-option>
                <a-select-option :value="FileType.CODE">代码文件</a-select-option>
                <a-select-option :value="FileType.ZIP">压缩文件</a-select-option>
                <a-select-option :value="FileType.OTHER">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="上传日期">
              <a-range-picker
                v-model:value="searchForm.dateRange"
                style="width: 100%"
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="文件状态">
              <a-select
                v-model:value="searchForm.status"
                placeholder="选择文件状态"
                allow-clear
              >
                <a-select-option :value="FileStatus.ACTIVE">有效</a-select-option>
                <a-select-option :value="FileStatus.EXPIRING_SOON">即将到期</a-select-option>
                <a-select-option :value="FileStatus.EXPIRED">已过期</a-select-option>
                <a-select-option :value="FileStatus.ARCHIVED">已归档</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="24" :lg="24" class="flex justify-end">
            <a-space>
              <a-button @click="resetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- Tab分类区域 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
        <!-- 工商档案Tab -->
        <a-tab-pane key="business" tab="工商档案">
          <BusinessFilesTab />
        </a-tab-pane>
        
        <!-- 知识产权Tab -->
        <a-tab-pane key="intellectual" tab="知识产权">
          <IntellectualPropertyTab />
        </a-tab-pane>
        
        <!-- 资质证书Tab -->
        <a-tab-pane key="certificate" tab="资质证书">
          <CertificatesTab />
        </a-tab-pane>
        
        <!-- 其他法律文件Tab -->
        <a-tab-pane key="legal" tab="其他法律文件">
          <LegalDocumentsTab />
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 文件上传弹窗 -->
    <FileUploadModal 
      v-model="uploadModalVisible"
      :category="activeTabKey as FileCategory"
      @success="handleUploadSuccess"
    />

    <!-- 版本历史弹窗 -->
    <FileVersionsModal
      v-model="versionsModalVisible"
      :file-id="selectedFileId"
    />

    <!-- 权限设置弹窗 -->
    <FilePermissionsModal
      v-model="permissionsModalVisible"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { 
  UploadOutlined, 
  SettingOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useFileStore } from '@/stores/file';
import { 
  FileType, FileCategory, FileStatus,
  type FileSearchFilter 
} from '@/types/file';
import BusinessFilesTab from './components/BusinessFilesTab.vue';
import IntellectualPropertyTab from './components/IntellectualPropertyTab.vue';
import CertificatesTab from './components/CertificatesTab.vue';
import LegalDocumentsTab from './components/LegalDocumentsTab.vue';
import FileUploadModal from './components/FileUploadModal.vue';
import FileVersionsModal from './components/FileVersionsModal.vue';
import FilePermissionsModal from './components/FilePermissionsModal.vue';

const router = useRouter();
const fileStore = useFileStore();

// 当前激活的Tab
const activeTabKey = ref<string>(FileCategory.BUSINESS);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  fileType: undefined as FileType | undefined,
  dateRange: [] as any,
  status: undefined as FileStatus | undefined
});

// 弹窗控制
const uploadModalVisible = ref(false);
const versionsModalVisible = ref(false);
const permissionsModalVisible = ref(false);
const selectedFileId = ref('');

// 计算属性
const statistics = computed(() => fileStore.statistics);

// 初始化
onMounted(async () => {
  await Promise.all([
    fileStore.fetchFiles(),
    fileStore.fetchFileStatistics()
  ]);
});

// 显示上传弹窗
const showUploadModal = () => {
  uploadModalVisible.value = true;
};

// 显示权限设置
const showPermissionSettings = () => {
  permissionsModalVisible.value = true;
};

// 显示版本历史
const showVersionHistory = (fileId: string) => {
  selectedFileId.value = fileId;
  versionsModalVisible.value = true;
};

// 处理上传成功
const handleUploadSuccess = () => {
  message.success('文件上传成功');
  fileStore.fetchFiles();
  fileStore.fetchFileStatistics();
};

// 处理Tab切换
const handleTabChange = (key: string) => {
  activeTabKey.value = key;
};

// 处理日期选择变更
const handleDateChange = (dates: any, dateStrings: [string, string]) => {
  if (dates) {
    searchForm.dateRange = dates;
  } else {
    searchForm.dateRange = [];
  }
};

// 搜索
const handleSearch = async () => {
  const filter: Partial<FileSearchFilter> = {
    keyword: searchForm.keyword,
    fileType: searchForm.fileType,
    status: searchForm.status
  };
  
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    filter.startDate = dayjs(searchForm.dateRange[0]).format('YYYY-MM-DD');
    filter.endDate = dayjs(searchForm.dateRange[1]).format('YYYY-MM-DD');
  }
  
  await fileStore.searchFiles(filter);
};

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = '';
  searchForm.fileType = undefined;
  searchForm.dateRange = [];
  searchForm.status = undefined;
  fileStore.resetFilter();
  fileStore.fetchFiles();
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} KB`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} MB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(2)} GB`;
  }
};

// 获取最后更新时间
const lastUpdatedTime = (category: FileCategory): string => {
  const categoryFiles = fileStore.files.filter(file => file.category === category);
  if (categoryFiles.length === 0) return '无';
  
  // 按更新时间降序排序，取第一个
  const sorted = [...categoryFiles].sort((a, b) => {
    const timeA = a.updatedTime || a.uploadTime;
    const timeB = b.updatedTime || b.uploadTime;
    return new Date(timeB).getTime() - new Date(timeA).getTime();
  });
  
  const latestTime = sorted[0].updatedTime || sorted[0].uploadTime;
  return dayjs(latestTime).format('YYYY-MM-DD');
};
</script>

<style scoped>
.file-management-container {
  animation: fadeIn 0.5s ease-in-out;
}

.statistics-card-wrapper :deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  height: 100%;
  margin-bottom: 16px;
}

.statistics-card :deep(.ant-card-head) {
  border-bottom: none;
  padding-bottom: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .statistics-card-wrapper {
    margin-bottom: 0;
  }
}
</style> 