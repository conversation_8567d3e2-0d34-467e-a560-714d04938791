<template>
  <div class="legal-documents-tab">
    <!-- 统计和工具栏区域 -->
    <div class="flex flex-wrap justify-between items-center mb-4">
      <div>
        <span class="text-lg font-medium">法律文件 ({{ legalFiles.length }})</span>
        <span class="ml-2 text-gray-500">合同模板、章程、协议等法律文件</span>
      </div>
      <div>
        <a-space>
          <a-select
            v-model:value="filterLegalType"
            placeholder="按文件类型筛选"
            style="width: 180px"
            allow-clear
            @change="handleLegalTypeChange"
          >
            <a-select-option v-for="type in legalTypes" :key="type" :value="type">
              {{ type }}
            </a-select-option>
          </a-select>
          <a-select
            v-model:value="filterIsTemplate"
            placeholder="是否为模板"
            style="width: 180px"
            allow-clear
            @change="handleIsTemplateChange"
          >
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
          <a-button @click="exportToExcel">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表格区域 -->
    <a-table
      :dataSource="filteredLegalFiles"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :rowKey="(record: LegalFile) => record.id"
      @change="handleTableChange"
      bordered
    >
      <!-- 表格列内容 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <a @click="showFileDetails(record.id)">{{ record.name }}</a>
        </template>
        
        <template v-else-if="column.dataIndex === 'legalType'">
          <a-tag color="blue">{{ record.legalType }}</a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'isTemplate'">
          <a-tag :color="record.isTemplate ? 'green' : 'purple'">
            {{ record.isTemplate ? '是' : '否' }}
          </a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusName(record.status) }}
          </a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'parties'">
          <template v-if="record.parties && record.parties.length">
            <a-tooltip :title="record.parties.join(', ')">
              <div class="truncate max-w-xs">{{ record.parties.join(', ') }}</div>
            </a-tooltip>
          </template>
          <template v-else>-</template>
        </template>
        
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="previewFile(record.id)">
              <template #icon><eye-outlined /></template>
              预览
            </a-button>
            <a-button type="link" size="small" @click="downloadFile(record.id)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                <template #icon><more-outlined /></template>
                更多
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="showVersions(record.id)">
                    <history-outlined />
                    版本历史
                  </a-menu-item>
                  <a-menu-item @click="editFile(record.id)">
                    <edit-outlined />
                    编辑信息
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="showDeleteConfirm(record.id)">
                    <delete-outlined />
                    <span class="text-danger">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情抽屉将在需要时添加 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { 
  EyeOutlined, 
  DownloadOutlined, 
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import { 
  FileType, FileCategory, FileStatus,
  type LegalFile, type PaginationInfo
} from '@/types/file';

const router = useRouter();
const fileStore = useFileStore();

// 表格列定义
const columns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '文件编号',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '法律文件类型',
    dataIndex: 'legalType',
    key: 'legalType',
    width: 150
  },
  {
    title: '是否为模板',
    dataIndex: 'isTemplate',
    key: 'isTemplate',
    width: 100
  },
  {
    title: '生效日期',
    dataIndex: 'effectiveDate',
    key: 'effectiveDate',
    width: 120,
    sorter: true
  },
  {
    title: '相关方',
    dataIndex: 'parties',
    key: 'parties',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 200
  }
];

// 法律文件类型选项（从已有文件中提取）
const legalTypes = computed(() => {
  const types = new Set<string>();
  fileStore.legalFiles.forEach(file => {
    if (file.legalType) {
      types.add(file.legalType);
    }
  });
  return Array.from(types);
});

// 筛选状态
const filterLegalType = ref<string | undefined>(undefined);
const filterIsTemplate = ref<boolean | undefined>(undefined);

// 分页配置
const pagination = reactive<PaginationInfo>({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total: number) => `共 ${total} 条`
});

// 计算属性
const loading = computed(() => fileStore.loading);
const legalFiles = computed(() => fileStore.legalFiles);

// 筛选后的文件列表
const filteredLegalFiles = computed(() => {
  let files = [...legalFiles.value];
  
  // 按法律文件类型筛选
  if (filterLegalType.value) {
    files = files.filter(file => file.legalType === filterLegalType.value);
  }
  
  // 按是否为模板筛选
  if (filterIsTemplate.value !== undefined) {
    files = files.filter(file => file.isTemplate === filterIsTemplate.value);
  }
  
  return files;
});

// 初始化
onMounted(async () => {
  if (legalFiles.value.length === 0) {
    await fileStore.fetchFiles();
  }
  
  // 更新分页总数
  pagination.total = filteredLegalFiles.value.length;
});

// 处理法律文件类型变更
const handleLegalTypeChange = (value: string | undefined) => {
  filterLegalType.value = value;
  pagination.current = 1;
};

// 处理是否为模板变更
const handleIsTemplateChange = (value: boolean | undefined) => {
  filterIsTemplate.value = value;
  pagination.current = 1;
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

// 显示文件详情
const showFileDetails = (id: string) => {
  message.info(`显示文件详情: ${id}`);
  // 实际应用中应该打开详情抽屉
};

// 预览文件
const previewFile = (id: string) => {
  router.push(`/files/preview/${id}`);
};

// 下载文件
const downloadFile = (id: string) => {
  message.success('文件开始下载');
  // 实际应用中应调用下载API
};

// 显示版本历史
const showVersions = async (id: string) => {
  router.push(`/files/versions/${id}`);
};

// 编辑文件
const editFile = (id: string) => {
  message.info(`编辑文件: ${id}`);
  // 实际应用中应该打开编辑弹窗
};

// 确认删除
const showDeleteConfirm = (id: string) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除此文件吗？此操作不可逆。',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await fileStore.deleteFile(id);
      if (success) {
        message.success('文件已删除');
      }
    }
  });
};

// 导出为Excel
const exportToExcel = () => {
  message.success('正在导出文件列表...');
  // 实际应用中应调用导出API
};

// 工具函数：获取状态名称
const getStatusName = (status: FileStatus): string => {
  const names: Record<FileStatus, string> = {
    [FileStatus.ACTIVE]: '有效',
    [FileStatus.EXPIRED]: '已过期',
    [FileStatus.EXPIRING_SOON]: '即将过期',
    [FileStatus.ARCHIVED]: '已归档',
    [FileStatus.DELETED]: '已删除'
  };
  
  return names[status] || '未知状态';
};

// 工具函数：获取状态颜色
const getStatusColor = (status: FileStatus): string => {
  const colors: Record<FileStatus, string> = {
    [FileStatus.ACTIVE]: 'success',
    [FileStatus.EXPIRED]: 'error',
    [FileStatus.EXPIRING_SOON]: 'warning',
    [FileStatus.ARCHIVED]: 'default',
    [FileStatus.DELETED]: 'default'
  };
  
  return colors[status] || 'default';
};
</script>

<style scoped>
@media (max-width: 768px) {
  .file-table-wrapper {
    margin-top: 16px;
  }
}
</style> 