<template>
  <a-modal
    :visible="modelValue"
    @update:visible="$emit('update:modelValue', $event)"
    title="上传文件"
    width="700px"
    :maskClosable="false"
    :confirmLoading="uploading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="upload-modal-content">
      <!-- 文件上传区域 -->
      <a-upload-dragger
        v-model:fileList="fileList"
        :multiple="false"
        :beforeUpload="beforeUpload"
        :showUploadList="true"
        @drop="handleDrop"
        @remove="handleRemove"
      >
        <p class="ant-upload-drag-icon">
          <inbox-outlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持单个或批量上传。支持的文件格式：PDF, Word, Excel, ZIP, 图片等
        </p>
      </a-upload-dragger>

      <!-- 元数据表单 -->
      <a-divider>文件信息</a-divider>

      <a-form
        :model="formState"
        layout="vertical"
        ref="formRef"
      >
        <a-form-item
          label="文件名称"
          name="name"
          :rules="[{ required: true, message: '请输入文件名称' }]"
        >
          <a-input v-model:value="formState.name" placeholder="请输入文件名称" />
        </a-form-item>

        <a-form-item
          label="文件分类"
          name="category"
          :rules="[{ required: true, message: '请选择文件分类' }]"
        >
          <a-select v-model:value="formState.category" :disabled="!!props.category">
            <a-select-option :value="FileCategory.BUSINESS">工商档案</a-select-option>
            <a-select-option :value="FileCategory.INTELLECTUAL">知识产权</a-select-option>
            <a-select-option :value="FileCategory.CERTIFICATE">资质证书</a-select-option>
            <a-select-option :value="FileCategory.LEGAL">其他法律文件</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 根据分类显示不同的表单项 -->
        <template v-if="formState.category === FileCategory.BUSINESS">
          <a-form-item
            label="档案类型"
            name="metadata.businessType"
            :rules="[{ required: true, message: '请选择档案类型' }]"
          >
            <a-select v-model:value="formState.metadata.businessType">
              <a-select-option :value="BusinessFileType.BUSINESS_LICENSE">营业执照</a-select-option>
              <a-select-option :value="BusinessFileType.TAX_REGISTRATION">税务登记证</a-select-option>
              <a-select-option :value="BusinessFileType.ORGANIZATION_CODE">组织机构代码证</a-select-option>
              <a-select-option :value="BusinessFileType.BANK_ACCOUNT">银行开户许可证</a-select-option>
              <a-select-option :value="BusinessFileType.OTHER">其他</a-select-option>
            </a-select>
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="登记日期" name="metadata.registrationDate">
                <a-date-picker v-model:value="formState.metadata.registrationDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="有效期至" name="metadata.expiryDate">
                <a-date-picker v-model:value="formState.metadata.expiryDate" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="登记主体" name="metadata.subject">
            <a-input v-model:value="formState.metadata.subject" placeholder="请输入登记主体，如子公司名称" />
          </a-form-item>

          <a-form-item label="登记机关" name="metadata.registrationAuthority">
            <a-input v-model:value="formState.metadata.registrationAuthority" placeholder="请输入登记机关" />
          </a-form-item>
        </template>

        <template v-else-if="formState.category === FileCategory.INTELLECTUAL">
          <a-form-item
            label="知识产权类型"
            name="metadata.ipType"
            :rules="[{ required: true, message: '请选择知识产权类型' }]"
          >
            <a-select v-model:value="formState.metadata.ipType">
              <a-select-option :value="IntellectualPropertyType.PATENT">专利</a-select-option>
              <a-select-option :value="IntellectualPropertyType.TRADEMARK">商标</a-select-option>
              <a-select-option :value="IntellectualPropertyType.COPYRIGHT">版权</a-select-option>
              <a-select-option :value="IntellectualPropertyType.SOFTWARE">软件著作权</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="申请号/注册号" name="metadata.applicationNumber">
            <a-input v-model:value="formState.metadata.applicationNumber" placeholder="请输入申请号或注册号" />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="申请日期" name="metadata.applicationDate">
                <a-date-picker v-model:value="formState.metadata.applicationDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="授权日期" name="metadata.grantDate">
                <a-date-picker v-model:value="formState.metadata.grantDate" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item
            label="知识产权状态"
            name="metadata.ipStatus"
            :rules="[{ required: true, message: '请选择知识产权状态' }]"
          >
            <a-select v-model:value="formState.metadata.ipStatus">
              <a-select-option :value="IPStatus.PENDING">申请中</a-select-option>
              <a-select-option :value="IPStatus.GRANTED">已授权</a-select-option>
              <a-select-option :value="IPStatus.REJECTED">已驳回</a-select-option>
              <a-select-option :value="IPStatus.EXPIRED">已过期</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="发明人/设计人" name="metadata.inventors">
            <a-select
              v-model:value="formState.metadata.inventors"
              mode="tags"
              placeholder="输入发明人或设计人名称，可多选"
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </template>

        <template v-else-if="formState.category === FileCategory.CERTIFICATE">
          <a-form-item
            label="证书类型"
            name="metadata.certificateType"
            :rules="[{ required: true, message: '请输入证书类型' }]"
          >
            <a-input v-model:value="formState.metadata.certificateType" placeholder="例如：高新技术企业认证、ISO9001认证" />
          </a-form-item>

          <a-form-item
            label="证书编号"
            name="metadata.certificateNumber"
            :rules="[{ required: true, message: '请输入证书编号' }]"
          >
            <a-input v-model:value="formState.metadata.certificateNumber" placeholder="请输入证书编号" />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="颁发日期" name="metadata.issueDate">
                <a-date-picker v-model:value="formState.metadata.issueDate" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item 
                label="有效期至" 
                name="metadata.expiryDate"
                :rules="[{ required: true, message: '请选择有效期' }]"
              >
                <a-date-picker v-model:value="formState.metadata.expiryDate" style="width: 100%" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="颁发机构" name="metadata.issuer">
            <a-input v-model:value="formState.metadata.issuer" placeholder="请输入证书颁发机构" />
          </a-form-item>

          <a-form-item label="认证范围" name="metadata.scope">
            <a-textarea v-model:value="formState.metadata.scope" placeholder="请输入认证范围" :rows="2" />
          </a-form-item>
        </template>

        <template v-else-if="formState.category === FileCategory.LEGAL">
          <a-form-item
            label="法律文件类型"
            name="metadata.legalType"
            :rules="[{ required: true, message: '请输入法律文件类型' }]"
          >
            <a-input v-model:value="formState.metadata.legalType" placeholder="例如：合同模板、公司章程" />
          </a-form-item>

          <a-form-item label="生效日期" name="metadata.effectiveDate">
            <a-date-picker v-model:value="formState.metadata.effectiveDate" style="width: 100%" />
          </a-form-item>

          <a-form-item label="相关方" name="metadata.parties">
            <a-select
              v-model:value="formState.metadata.parties"
              mode="tags"
              placeholder="输入相关方名称，可多选"
              style="width: 100%"
            ></a-select>
          </a-form-item>

          <a-form-item label="是否为模板" name="metadata.isTemplate">
            <a-switch v-model:checked="formState.metadata.isTemplate" />
          </a-form-item>
        </template>

        <a-divider>其他信息</a-divider>

        <a-form-item label="关联项目" name="projectId">
          <a-select v-model:value="formState.projectId" placeholder="请选择关联项目" allow-clear>
            <a-select-option value="P1001">项目1001</a-select-option>
            <a-select-option value="P1002">项目1002</a-select-option>
            <a-select-option value="P1003">项目1003</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="标签" name="tags">
          <a-select
            v-model:value="formState.tags"
            mode="tags"
            placeholder="请输入标签，按回车确认"
            style="width: 100%"
          ></a-select>
        </a-form-item>

        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            placeholder="请输入文件描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import type { UploadFile } from 'ant-design-vue';
import { useFileStore } from '@/stores/file';
import { 
  FileType, FileCategory, FileStatus, 
  BusinessFileType, IntellectualPropertyType, IPStatus,
  type File, type FileUploadParams
} from '@/types/file';

// 组件接收的属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  category: {
    type: String as () => FileCategory,
    default: undefined
  }
});

// 组件事件
const emit = defineEmits(['update:modelValue', 'success']);

// 引入Store
const fileStore = useFileStore();

// 文件列表
const fileList = ref<UploadFile[]>([]);
// 上传状态
const uploading = ref(false);
// 表单ref
const formRef = ref();

// 初始化表单状态
const initFormState = () => {
  return {
    name: '',
    category: props.category || FileCategory.BUSINESS,
    projectId: undefined,
    tags: [],
    description: '',
    metadata: {
      // 工商档案
      businessType: BusinessFileType.BUSINESS_LICENSE,
      registrationDate: undefined,
      expiryDate: undefined,
      subject: '',
      registrationAuthority: '',
      
      // 知识产权
      ipType: IntellectualPropertyType.PATENT,
      applicationNumber: '',
      applicationDate: undefined,
      grantDate: undefined,
      ipStatus: IPStatus.PENDING,
      inventors: [],
      
      // 资质证书
      certificateType: '',
      certificateNumber: '',
      issueDate: undefined,
      issuer: '',
      scope: '',
      
      // 法律文件
      legalType: '',
      effectiveDate: undefined,
      parties: [],
      isTemplate: false
    }
  };
};

// 表单状态
const formState = reactive(initFormState());

// 监听文件变化，自动设置文件名
watch(fileList, (files) => {
  if (files.length > 0 && !formState.name) {
    const filename = files[0].name;
    if (filename) {
      // 移除文件扩展名
      const dotIndex = filename.lastIndexOf('.');
      formState.name = dotIndex > 0 ? filename.substring(0, dotIndex) : filename;
    }
  }
});

// 监听分类变化，保持与props同步
watch(() => props.category, (category) => {
  if (category) {
    formState.category = category;
  }
});

// 获取文件类型
const getFileType = (filename: string): FileType => {
  const ext = filename.toLowerCase().split('.').pop();
  
  if (!ext) return FileType.OTHER;
  
  switch (ext) {
    case 'pdf':
      return FileType.PDF;
    case 'doc':
    case 'docx':
      return FileType.WORD;
    case 'xls':
    case 'xlsx':
      return FileType.EXCEL;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return FileType.IMAGE;
    case 'js':
    case 'ts':
    case 'html':
    case 'css':
    case 'java':
    case 'py':
      return FileType.CODE;
    case 'zip':
    case 'rar':
    case '7z':
      return FileType.ZIP;
    default:
      return FileType.OTHER;
  }
};

// 上传前处理
const beforeUpload = (file: File) => {
  const isLt100M = file.size / 1024 / 1024 < 100;
  
  if (!isLt100M) {
    message.error('文件大小不能超过100MB!');
  }
  
  // 手动控制上传，阻止默认上传行为
  return false;
};

// 处理拖拽
const handleDrop = (e: any) => {
  console.log('Dropped files', e.dataTransfer.files);
  // 实际处理会在beforeUpload中进行
};

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
  
  // 如果没有文件了，清空文件名
  if (newFileList.length === 0) {
    formState.name = '';
  }
};

// 准备上传数据
const prepareUploadData = (): File | null => {
  if (fileList.value.length === 0) {
    message.error('请选择要上传的文件');
    return null;
  }
  
  const file = fileList.value[0];
  if (!file.originFileObj) {
    message.error('文件对象错误');
    return null;
  }
  
  // 构造基本文件信息
  const fileData: File = {
    id: '', // 由服务器生成
    name: formState.name,
    code: '', // 由服务器生成
    type: getFileType(file.name || ''),
    category: formState.category,
    size: file.size || 0,
    uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    path: '', // 由服务器生成
    projectId: formState.projectId,
    tags: formState.tags,
    description: formState.description,
    createdBy: '当前用户', // 实际应用中应使用当前登录用户
    status: FileStatus.ACTIVE
  };
  
  // 根据不同的分类添加特定字段
  if (formState.category === FileCategory.BUSINESS) {
    Object.assign(fileData, {
      businessType: formState.metadata.businessType,
      registrationDate: formState.metadata.registrationDate ? dayjs(formState.metadata.registrationDate).format('YYYY-MM-DD') : undefined,
      expiryDate: formState.metadata.expiryDate ? dayjs(formState.metadata.expiryDate).format('YYYY-MM-DD') : undefined,
      subject: formState.metadata.subject,
      registrationAuthority: formState.metadata.registrationAuthority
    });
  } else if (formState.category === FileCategory.INTELLECTUAL) {
    Object.assign(fileData, {
      ipType: formState.metadata.ipType,
      applicationNumber: formState.metadata.applicationNumber,
      applicationDate: formState.metadata.applicationDate ? dayjs(formState.metadata.applicationDate).format('YYYY-MM-DD') : undefined,
      grantDate: formState.metadata.grantDate ? dayjs(formState.metadata.grantDate).format('YYYY-MM-DD') : undefined,
      expiryDate: dayjs().add(20, 'year').format('YYYY-MM-DD'), // 默认20年
      ipStatus: formState.metadata.ipStatus,
      inventors: formState.metadata.inventors
    });
  } else if (formState.category === FileCategory.CERTIFICATE) {
    Object.assign(fileData, {
      certificateType: formState.metadata.certificateType,
      certificateNumber: formState.metadata.certificateNumber,
      issueDate: formState.metadata.issueDate ? dayjs(formState.metadata.issueDate).format('YYYY-MM-DD') : undefined,
      expiryDate: formState.metadata.expiryDate ? dayjs(formState.metadata.expiryDate).format('YYYY-MM-DD') : undefined,
      issuer: formState.metadata.issuer,
      scope: formState.metadata.scope
    });
  } else if (formState.category === FileCategory.LEGAL) {
    Object.assign(fileData, {
      legalType: formState.metadata.legalType,
      effectiveDate: formState.metadata.effectiveDate ? dayjs(formState.metadata.effectiveDate).format('YYYY-MM-DD') : undefined,
      parties: formState.metadata.parties,
      isTemplate: formState.metadata.isTemplate
    });
  }
  
  return fileData as File;
};

// 确认上传
const handleOk = async () => {
  try {
    const valid = await formRef.value.validate();
    
    // 准备上传数据
    const uploadData = prepareUploadData();
    if (!uploadData) return;
    
    uploading.value = true;
    
    // 调用Store上传文件
    const result = await fileStore.uploadFile(uploadData);
    
    if (result) {
      message.success('文件上传成功');
      emit('success');
      emit('update:modelValue', false);
      
      // 重置表单和文件列表
      resetForm();
    }
  } catch (error) {
    console.error('上传失败', error);
    message.error('表单验证失败');
  } finally {
    uploading.value = false;
  }
};

// 取消上传
const handleCancel = () => {
  resetForm();
  emit('update:modelValue', false);
};

// 重置表单
const resetForm = () => {
  Object.assign(formState, initFormState());
  fileList.value = [];
  
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 每次打开弹窗时，重置表单
watch(() => props.modelValue, (visible) => {
  if (visible) {
    resetForm();
  }
});
</script>

<style scoped>
.upload-modal-content {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 4px;
}

.upload-modal-content::-webkit-scrollbar {
  width: 6px;
}

.upload-modal-content::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.upload-modal-content::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}
</style> 