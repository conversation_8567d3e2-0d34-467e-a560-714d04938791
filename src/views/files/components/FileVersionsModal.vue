<template>
  <a-modal
    :visible="modelValue"
    @update:visible="$emit('update:modelValue', $event)"
    title="版本历史"
    width="700px"
    :footer="null"
    :maskClosable="true"
  >
    <div class="file-versions-modal">
      <div class="mb-4">
        <a-alert 
          v-if="currentFile"
          type="info"
          :message="`正在查看 ${currentFile.name}（${currentFile.code}）的版本历史`"
          show-icon
        />
      </div>
      
      <a-spin :spinning="loading">
        <a-timeline v-if="fileVersions.length > 0">
          <a-timeline-item 
            v-for="version in fileVersions" 
            :key="version.id"
            :color="version.versionNumber === 'v1.0' ? 'green' : 'blue'"
          >
            <div class="version-item" :class="{ 'version-latest': version === fileVersions[0] }">
              <div class="flex justify-between items-center">
                <div>
                  <span class="version-number">{{ version.versionNumber }}</span>
                  <a-tag 
                    v-if="version === fileVersions[0]" 
                    class="ml-2"
                    color="green"
                  >
                    当前版本
                  </a-tag>
                </div>
                <span class="text-gray-500">{{ version.changeTime }}</span>
              </div>
              <div class="mt-2 mb-2 version-note">{{ version.changeNote }}</div>
              <div class="flex justify-between items-center text-gray-500">
                <span>大小: {{ formatFileSize(version.size) }}</span>
                <span>上传人: {{ version.createdBy }}</span>
              </div>
              <div class="mt-2 version-actions">
                <a-space>
                  <a-button size="small" @click="previewVersion(version.id)">
                    <template #icon><eye-outlined /></template>
                    预览
                  </a-button>
                  <a-button size="small" @click="downloadVersion(version.id)">
                    <template #icon><download-outlined /></template>
                    下载
                  </a-button>
                  <a-button 
                    v-if="version !== fileVersions[0]"
                    size="small" 
                    @click="setAsCurrent(version.id)"
                  >
                    <template #icon><reload-outlined /></template>
                    设为当前版本
                  </a-button>
                  <a-button 
                    v-if="version !== fileVersions[0]"
                    size="small" 
                    type="primary"
                    @click="compareVersions(version.id, fileVersions[0].id)"
                  >
                    <template #icon><diff-outlined /></template>
                    与当前版本比较
                  </a-button>
                </a-space>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else description="暂无版本历史记录" />
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import { 
  EyeOutlined, 
  DownloadOutlined, 
  DiffOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { useFileStore } from '@/stores/file';
import type { FileVersion } from '@/types/file';

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  fileId: {
    type: String,
    default: ''
  }
});

// 组件事件
const emit = defineEmits(['update:modelValue']);

// 路由
const router = useRouter();

// Store
const fileStore = useFileStore();

// 状态
const loading = ref(false);
const fileVersions = ref<FileVersion[]>([]);

// 计算属性
const currentFile = computed(() => fileStore.fileById(props.fileId));

// 监听visible或fileId的变化
watch(
  () => [props.modelValue, props.fileId], 
  async ([visible, fileId]) => {
    if (visible && fileId) {
      await fetchVersions(fileId as string);
    }
  },
  { immediate: true }
);

// 获取文件版本历史
const fetchVersions = async (fileId: string) => {
  if (!fileId) return;
  
  loading.value = true;
  
  try {
    fileVersions.value = await fileStore.fetchFileVersions(fileId);
  } catch (error) {
    console.error('获取版本历史失败', error);
    message.error('获取版本历史失败');
    fileVersions.value = [];
  } finally {
    loading.value = false;
  }
};

// 预览版本
const previewVersion = (versionId: string) => {
  // 实际应用中应该跳转到文件预览页
  message.info(`预览版本: ${versionId}`);
};

// 下载版本
const downloadVersion = (versionId: string) => {
  message.success('文件版本开始下载');
  // 实际应用中应调用下载API
};

// 设置为当前版本
const setAsCurrent = (versionId: string) => {
  message.info(`设置版本 ${versionId} 为当前版本`);
  // 实际应用中需要调用API更新当前版本
};

// 比较版本差异
const compareVersions = (versionId1: string, versionId2: string) => {
  message.info(`比较版本 ${versionId1} 和 ${versionId2} 的差异`);
  // 实际应用中应导航到版本对比页面
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} KB`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} MB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(2)} GB`;
  }
};
</script>

<style scoped>
.file-versions-modal {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 4px;
}

.version-item {
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s;
  border-left: 3px solid #1890ff;
}

.version-latest {
  background-color: #f0f7ff;
  border-left-color: #52c41a;
}

.version-number {
  font-weight: bold;
  font-size: 16px;
}

.version-note {
  line-height: 1.5;
}

.version-actions {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e8e8e8;
}
</style> 