<template>
  <div class="certificates-tab">
    <!-- 统计和工具栏区域 -->
    <div class="flex flex-wrap justify-between items-center mb-4">
      <div>
        <span class="text-lg font-medium">资质证书 ({{ certificateFiles.length }})</span>
        <span class="ml-2 text-gray-500">企业资质认证、荣誉证书等证明文件</span>
      </div>
      <div>
        <a-space>
          <a-select
            v-model:value="filterCertType"
            placeholder="按证书类型筛选"
            style="width: 180px"
            allow-clear
            @change="handleCertTypeChange"
          >
            <a-select-option v-for="type in certificateTypes" :key="type" :value="type">
              {{ type }}
            </a-select-option>
          </a-select>
          <a-select
            v-model:value="filterStatus"
            placeholder="按状态筛选"
            style="width: 180px"
            allow-clear
            @change="handleStatusChange"
          >
            <a-select-option :value="FileStatus.ACTIVE">有效</a-select-option>
            <a-select-option :value="FileStatus.EXPIRING_SOON">即将到期</a-select-option>
            <a-select-option :value="FileStatus.EXPIRED">已过期</a-select-option>
          </a-select>
          <a-button @click="exportToExcel">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表格区域 -->
    <a-table
      :dataSource="filteredCertFiles"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :rowKey="(record: CertificateFile) => record.id"
      @change="handleTableChange"
      bordered
    >
      <!-- 表格列内容 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <a @click="showFileDetails(record.id)">{{ record.name }}</a>
        </template>
        
        <template v-else-if="column.dataIndex === 'certificateType'">
          <a-tag color="blue">{{ record.certificateType }}</a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusName(record.status) }}
          </a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="previewFile(record.id)">
              <template #icon><eye-outlined /></template>
              预览
            </a-button>
            <a-button type="link" size="small" @click="downloadFile(record.id)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                <template #icon><more-outlined /></template>
                更多
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="showVersions(record.id)">
                    <history-outlined />
                    版本历史
                  </a-menu-item>
                  <a-menu-item @click="editFile(record.id)">
                    <edit-outlined />
                    编辑信息
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="showDeleteConfirm(record.id)">
                    <delete-outlined />
                    <span class="text-danger">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情抽屉将在需要时添加 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { 
  EyeOutlined, 
  DownloadOutlined, 
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import { 
  FileType, FileCategory, FileStatus,
  type CertificateFile, type PaginationInfo
} from '@/types/file';

const router = useRouter();
const fileStore = useFileStore();

// 表格列定义
const columns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '文件编号',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '证书类型',
    dataIndex: 'certificateType',
    key: 'certificateType',
    width: 150
  },
  {
    title: '证书编号',
    dataIndex: 'certificateNumber',
    key: 'certificateNumber',
    width: 150
  },
  {
    title: '颁发日期',
    dataIndex: 'issueDate',
    key: 'issueDate',
    width: 120,
    sorter: true
  },
  {
    title: '到期日期',
    dataIndex: 'expiryDate',
    key: 'expiryDate',
    width: 120,
    sorter: true
  },
  {
    title: '颁发机构',
    dataIndex: 'issuer',
    key: 'issuer',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 200
  }
];

// 证书类型选项（从已有证书中提取）
const certificateTypes = computed(() => {
  const types = new Set<string>();
  fileStore.certificateFiles.forEach(file => {
    if (file.certificateType) {
      types.add(file.certificateType);
    }
  });
  return Array.from(types);
});

// 筛选状态
const filterCertType = ref<string | undefined>(undefined);
const filterStatus = ref<FileStatus | undefined>(undefined);

// 分页配置
const pagination = reactive<PaginationInfo>({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total: number) => `共 ${total} 条`
});

// 计算属性
const loading = computed(() => fileStore.loading);
const certificateFiles = computed(() => fileStore.certificateFiles);

// 筛选后的文件列表
const filteredCertFiles = computed(() => {
  let files = [...certificateFiles.value];
  
  // 按证书类型筛选
  if (filterCertType.value) {
    files = files.filter(file => file.certificateType === filterCertType.value);
  }
  
  // 按状态筛选
  if (filterStatus.value) {
    files = files.filter(file => file.status === filterStatus.value);
  }
  
  return files;
});

// 初始化
onMounted(async () => {
  if (certificateFiles.value.length === 0) {
    await fileStore.fetchFiles();
  }
  
  // 更新分页总数
  pagination.total = filteredCertFiles.value.length;
});

// 处理证书类型变更
const handleCertTypeChange = (value: string | undefined) => {
  filterCertType.value = value;
  pagination.current = 1;
};

// 处理状态变更
const handleStatusChange = (value: FileStatus | undefined) => {
  filterStatus.value = value;
  pagination.current = 1;
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

// 显示文件详情
const showFileDetails = (id: string) => {
  message.info(`显示文件详情: ${id}`);
  // 实际应用中应该打开详情抽屉
};

// 预览文件
const previewFile = (id: string) => {
  router.push(`/files/preview/${id}`);
};

// 下载文件
const downloadFile = (id: string) => {
  message.success('文件开始下载');
  // 实际应用中应调用下载API
};

// 显示版本历史
const showVersions = async (id: string) => {
  router.push(`/files/versions/${id}`);
};

// 编辑文件
const editFile = (id: string) => {
  message.info(`编辑文件: ${id}`);
  // 实际应用中应该打开编辑弹窗
};

// 确认删除
const showDeleteConfirm = (id: string) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除此文件吗？此操作不可逆。',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await fileStore.deleteFile(id);
      if (success) {
        message.success('文件已删除');
      }
    }
  });
};

// 导出为Excel
const exportToExcel = () => {
  message.success('正在导出文件列表...');
  // 实际应用中应调用导出API
};

// 工具函数：获取状态名称
const getStatusName = (status: FileStatus): string => {
  const names: Record<FileStatus, string> = {
    [FileStatus.ACTIVE]: '有效',
    [FileStatus.EXPIRED]: '已过期',
    [FileStatus.EXPIRING_SOON]: '即将过期',
    [FileStatus.ARCHIVED]: '已归档',
    [FileStatus.DELETED]: '已删除'
  };
  
  return names[status] || '未知状态';
};

// 工具函数：获取状态颜色
const getStatusColor = (status: FileStatus): string => {
  const colors: Record<FileStatus, string> = {
    [FileStatus.ACTIVE]: 'success',
    [FileStatus.EXPIRED]: 'error',
    [FileStatus.EXPIRING_SOON]: 'warning',
    [FileStatus.ARCHIVED]: 'default',
    [FileStatus.DELETED]: 'default'
  };
  
  return colors[status] || 'default';
};
</script>

<style scoped>
@media (max-width: 768px) {
  .file-table-wrapper {
    margin-top: 16px;
  }
}
</style>