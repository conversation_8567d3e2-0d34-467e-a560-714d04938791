<template>
  <a-modal
    :visible="modelValue"
    @update:visible="$emit('update:modelValue', $event)"
    title="文件权限设置"
    width="800px"
    :footer="null"
    :maskClosable="true"
  >
    <div class="file-permissions-modal">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 权限设置Tab -->
        <a-tab-pane key="permissions" tab="权限管理">
          <div class="mb-4 flex justify-between items-center">
            <div>
              <span class="text-lg font-medium">权限设置</span>
              <span class="text-gray-500 ml-2">管理文件的访问和操作权限</span>
            </div>
            <a-button type="primary" @click="showAddPermissionModal">
              <template #icon><plus-outlined /></template>
              添加权限
            </a-button>
          </div>
          
          <a-table
            :dataSource="filePermissions"
            :columns="permissionColumns"
            :loading="loading"
            :pagination="{ pageSize: 5, showTotal: (total: number) => `共 ${total} 条` }"
            :rowKey="(record: Permission) => record.id"
            bordered
          >
            <!-- 目标类型列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'targetType'">
                <a-tag :color="getTargetTypeColor(record.targetType)">
                  {{ getTargetTypeName(record.targetType) }}
                </a-tag>
              </template>
              
              <!-- 目标列 -->
              <template v-else-if="column.dataIndex === 'targetId'">
                {{ getTargetName(record.targetType, record.targetId) }}
              </template>
              
              <!-- 权限类型列 -->
              <template v-else-if="column.dataIndex === 'permissionType'">
                <a-tag :color="getPermissionTypeColor(record.permissionType)">
                  {{ getPermissionTypeName(record.permissionType) }}
                </a-tag>
              </template>
              
              <!-- 临时授权列 -->
              <template v-else-if="column.dataIndex === 'expireTime'">
                <template v-if="record.expireTime">
                  <a-tag :color="isExpired(record.expireTime) ? 'error' : 'green'">
                    {{ record.expireTime }} 
                    {{ isExpired(record.expireTime) ? '(已过期)' : '' }}
                  </a-tag>
                </template>
                <template v-else>
                  <span class="text-gray-500">永久有效</span>
                </template>
              </template>
              
              <!-- 操作列 -->
              <template v-else-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="editPermission(record)">
                    <template #icon><edit-outlined /></template>
                    编辑
                  </a-button>
                  <a-button type="link" size="small" danger @click="showDeleteConfirm(record.id)">
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        
        <!-- 操作日志Tab -->
        <a-tab-pane key="logs" tab="操作日志">
          <div class="mb-4 flex justify-between items-center">
            <div>
              <span class="text-lg font-medium">操作日志</span>
              <span class="text-gray-500 ml-2">记录文件的所有操作历史</span>
            </div>
            <a-button @click="exportLogs">
              <template #icon><download-outlined /></template>
              导出日志
            </a-button>
          </div>
          
          <a-table
            :dataSource="operationLogs"
            :columns="logColumns"
            :loading="logsLoading"
            :pagination="{ pageSize: 10, showTotal: (total: number) => `共 ${total} 条` }"
            :rowKey="(record: FileOperationLog) => record.id"
            bordered
          >
            <!-- 操作类型列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'operation'">
                <a-tag :color="getOperationColor(record.operation)">
                  {{ getOperationName(record.operation) }}
                </a-tag>
              </template>
              
              <!-- 操作时间列 -->
              <template v-else-if="column.dataIndex === 'operationTime'">
                {{ formatDateTime(record.operationTime) }}
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
      
      <!-- 添加权限弹窗 -->
      <a-modal
        v-model:visible="addPermissionVisible"
        title="添加权限"
        @ok="handleAddPermission"
        @cancel="addPermissionVisible = false"
        :confirmLoading="addingPermission"
      >
        <a-form :model="permissionForm" layout="vertical">
          <a-form-item label="文件" name="fileId">
            <a-select
              v-model:value="permissionForm.fileId"
              placeholder="请选择文件"
              :options="fileOptions"
              show-search
              :filter-option="filterFileOption"
            ></a-select>
          </a-form-item>
          
          <a-form-item label="目标类型" name="targetType">
            <a-select v-model:value="permissionForm.targetType" placeholder="请选择目标类型">
              <a-select-option value="user">用户</a-select-option>
              <a-select-option value="role">角色</a-select-option>
              <a-select-option value="department">部门</a-select-option>
              <a-select-option value="project">项目</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="目标" name="targetId">
            <a-select 
              v-model:value="permissionForm.targetId"
              placeholder="请选择目标"
              :options="targetOptions"
              show-search
              :filter-option="(input: string, option: any) => 
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            ></a-select>
          </a-form-item>
          
          <a-form-item label="权限类型" name="permissionType">
            <a-radio-group v-model:value="permissionForm.permissionType">
              <a-radio :value="PermissionType.READ">只读</a-radio>
              <a-radio :value="PermissionType.EDIT">编辑</a-radio>
              <a-radio :value="PermissionType.DOWNLOAD">下载</a-radio>
              <a-radio :value="PermissionType.FULL">完全控制</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="临时授权">
            <a-switch v-model:checked="permissionForm.isTemporary" />
          </a-form-item>
          
          <a-form-item 
            v-if="permissionForm.isTemporary" 
            label="过期时间" 
            name="expireTime"
          >
            <a-date-picker 
              v-model:value="permissionForm.expireTime"
              :disabledDate="disabledDate"
              show-time
              style="width: 100%"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import { 
  PermissionType,
  type Permission, 
  type FileOperationLog
} from '@/types/file';

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
});

// 组件事件
const emit = defineEmits(['update:modelValue']);

// Store
const fileStore = useFileStore();

// 状态
const activeTab = ref('permissions');
const loading = ref(false);
const logsLoading = ref(false);
const filePermissions = ref<Permission[]>([]);
const operationLogs = ref<FileOperationLog[]>([]);

// 添加权限弹窗
const addPermissionVisible = ref(false);
const addingPermission = ref(false);
const permissionForm = reactive({
  fileId: undefined as string | undefined,
  targetType: 'user' as 'user' | 'role' | 'department' | 'project',
  targetId: undefined as string | undefined,
  permissionType: PermissionType.READ,
  isTemporary: false,
  expireTime: undefined as any
});

// 权限表格列定义
const permissionColumns = [
  {
    title: '文件',
    dataIndex: ['file', 'name'],
    key: 'fileName',
    width: '20%'
  },
  {
    title: '目标类型',
    dataIndex: 'targetType',
    key: 'targetType',
    width: '10%'
  },
  {
    title: '目标',
    dataIndex: 'targetId',
    key: 'targetId',
    width: '15%'
  },
  {
    title: '权限类型',
    dataIndex: 'permissionType',
    key: 'permissionType',
    width: '15%'
  },
  {
    title: '过期时间',
    dataIndex: 'expireTime',
    key: 'expireTime',
    width: '15%'
  },
  {
    title: '授权人',
    dataIndex: 'createdBy',
    key: 'createdBy',
    width: '10%'
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: '15%'
  }
];

// 日志表格列定义
const logColumns = [
  {
    title: '文件名',
    dataIndex: ['file', 'name'],
    key: 'fileName',
    width: '20%'
  },
  {
    title: '操作类型',
    dataIndex: 'operation',
    key: 'operation',
    width: '15%'
  },
  {
    title: '操作时间',
    dataIndex: 'operationTime',
    key: 'operationTime',
    width: '15%'
  },
  {
    title: '操作人',
    dataIndex: 'operatedBy',
    key: 'operatedBy',
    width: '10%'
  },
  {
    title: '详情',
    dataIndex: 'details',
    key: 'details',
    width: '25%'
  },
  {
    title: 'IP地址',
    dataIndex: 'ipAddress',
    key: 'ipAddress',
    width: '15%'
  }
];

// 文件选项
const fileOptions = computed(() => {
  return fileStore.files.map(file => ({
    label: `${file.name} (${file.code})`,
    value: file.id
  }));
});

// 目标选项
const targetOptions = computed(() => {
  switch (permissionForm.targetType) {
    case 'user':
      return [
        { label: '张三', value: 'U1001' },
        { label: '李四', value: 'U1002' },
        { label: '王五', value: 'U1003' }
      ];
    case 'role':
      return [
        { label: '管理员', value: 'admin' },
        { label: '普通用户', value: 'user' },
        { label: '访客', value: 'guest' }
      ];
    case 'department':
      return [
        { label: '技术部', value: 'tech' },
        { label: '财务部', value: 'finance' },
        { label: '人事部', value: 'hr' }
      ];
    case 'project':
      return [
        { label: '项目1', value: 'P1001' },
        { label: '项目2', value: 'P1002' },
        { label: '项目3', value: 'P1003' }
      ];
    default:
      return [];
  }
});

// 监听visible变化
watch(() => props.modelValue, async (visible) => {
  if (visible) {
    await loadData();
  }
});

// 加载权限和日志数据
const loadData = async () => {
  loading.value = true;
  logsLoading.value = true;
  
  try {
    // 假设这里是加载所有权限（实际应用可能需要分页）
    filePermissions.value = await Promise.all(
      fileStore.files.slice(0, 5).map(file => fileStore.fetchFilePermissions(file.id))
    ).then(results => results.flat());
    
    // 加载操作日志
    operationLogs.value = await Promise.all(
      fileStore.files.slice(0, 3).map(file => fileStore.fetchFileOperationLogs(file.id))
    ).then(results => results.flat());
  } catch (error) {
    console.error('加载权限数据失败', error);
    message.error('加载权限数据失败');
  } finally {
    loading.value = false;
    logsLoading.value = false;
  }
};

// 文件选择器过滤
const filterFileOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 显示添加权限弹窗
const showAddPermissionModal = () => {
  // 重置表单
  Object.assign(permissionForm, {
    fileId: undefined,
    targetType: 'user',
    targetId: undefined,
    permissionType: PermissionType.READ,
    isTemporary: false,
    expireTime: undefined
  });
  
  addPermissionVisible.value = true;
};

// 添加权限
const handleAddPermission = async () => {
  // 表单验证
  if (!permissionForm.fileId) {
    message.error('请选择文件');
    return;
  }
  
  if (!permissionForm.targetId) {
    message.error('请选择目标');
    return;
  }
  
  if (permissionForm.isTemporary && !permissionForm.expireTime) {
    message.error('请选择过期时间');
    return;
  }
  
  addingPermission.value = true;
  
  try {
    const permission = {
      targetType: permissionForm.targetType,
      targetId: permissionForm.targetId,
      permissionType: permissionForm.permissionType,
      expireTime: permissionForm.isTemporary 
        ? dayjs(permissionForm.expireTime).format('YYYY-MM-DD HH:mm:ss') 
        : undefined
    };
    
    const result = await fileStore.setFilePermission(
      permissionForm.fileId,
      permission
    );
    
    if (result) {
      message.success('添加权限成功');
      addPermissionVisible.value = false;
      await loadData(); // 重新加载权限列表
    }
  } catch (error) {
    console.error('添加权限失败', error);
    message.error('添加权限失败');
  } finally {
    addingPermission.value = false;
  }
};

// 编辑权限
const editPermission = (permission: Permission) => {
  // 在实际应用中应该打开编辑弹窗
  message.info(`编辑权限: ${permission.id}`);
};

// 确认删除
const showDeleteConfirm = (id: string) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除此权限设置吗？',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await fileStore.deleteFilePermission(id);
      if (success) {
        message.success('权限已删除');
        await loadData(); // 重新加载权限列表
      }
    }
  });
};

// 导出日志
const exportLogs = () => {
  message.success('正在导出操作日志...');
  // 实际应用中应调用导出API
};

// 禁用过去的日期
const disabledDate = (current: any) => {
  return current && current < dayjs().startOf('day');
};

// 工具函数：检查是否过期
const isExpired = (expireTime: string): boolean => {
  return dayjs(expireTime).isBefore(dayjs());
};

// 工具函数：格式化日期时间
const formatDateTime = (dateTime: string): string => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

// 工具函数：获取目标类型名称
const getTargetTypeName = (type: string): string => {
  const names: Record<string, string> = {
    'user': '用户',
    'role': '角色',
    'department': '部门',
    'project': '项目'
  };
  
  return names[type] || type;
};

// 工具函数：获取目标类型颜色
const getTargetTypeColor = (type: string): string => {
  const colors: Record<string, string> = {
    'user': 'blue',
    'role': 'purple',
    'department': 'orange',
    'project': 'green'
  };
  
  return colors[type] || 'default';
};

// 工具函数：获取目标名称
const getTargetName = (type: string, id: string): string => {
  // 实际应用中应该从用户库、角色库等获取真实名称
  const userNames: Record<string, string> = {
    'U1001': '张三',
    'U1002': '李四',
    'U1003': '王五'
  };
  
  const roleNames: Record<string, string> = {
    'admin': '管理员',
    'user': '普通用户',
    'guest': '访客'
  };
  
  const departmentNames: Record<string, string> = {
    'tech': '技术部',
    'finance': '财务部',
    'hr': '人事部'
  };
  
  const projectNames: Record<string, string> = {
    'P1001': '项目1',
    'P1002': '项目2',
    'P1003': '项目3'
  };
  
  switch (type) {
    case 'user':
      return userNames[id] || id;
    case 'role':
      return roleNames[id] || id;
    case 'department':
      return departmentNames[id] || id;
    case 'project':
      return projectNames[id] || id;
    default:
      return id;
  }
};

// 工具函数：获取权限类型名称
const getPermissionTypeName = (type: PermissionType): string => {
  const names: Record<PermissionType, string> = {
    [PermissionType.READ]: '只读',
    [PermissionType.EDIT]: '编辑',
    [PermissionType.DOWNLOAD]: '下载',
    [PermissionType.FULL]: '完全控制'
  };
  
  return names[type] || '未知权限';
};

// 工具函数：获取权限类型颜色
const getPermissionTypeColor = (type: PermissionType): string => {
  const colors: Record<PermissionType, string> = {
    [PermissionType.READ]: 'blue',
    [PermissionType.EDIT]: 'green',
    [PermissionType.DOWNLOAD]: 'orange',
    [PermissionType.FULL]: 'red'
  };
  
  return colors[type] || 'default';
};

// 工具函数：获取操作名称
const getOperationName = (operation: string): string => {
  const names: Record<string, string> = {
    'upload': '上传',
    'download': '下载',
    'view': '查看',
    'edit': '编辑',
    'permission_change': '权限变更',
    'delete': '删除'
  };
  
  return names[operation] || operation;
};

// 工具函数：获取操作颜色
const getOperationColor = (operation: string): string => {
  const colors: Record<string, string> = {
    'upload': 'green',
    'download': 'blue',
    'view': 'processing',
    'edit': 'orange',
    'permission_change': 'purple',
    'delete': 'red'
  };
  
  return colors[operation] || 'default';
};
</script>

<style scoped>
.file-permissions-modal {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 4px;
}

.file-permissions-modal::-webkit-scrollbar {
  width: 6px;
}

.file-permissions-modal::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.file-permissions-modal::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}
</style> 