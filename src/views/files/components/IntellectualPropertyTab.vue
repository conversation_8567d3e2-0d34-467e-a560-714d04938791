<template>
  <div class="intellectual-property-tab">
    <!-- 统计和工具栏区域 -->
    <div class="flex flex-wrap justify-between items-center mb-4">
      <div>
        <span class="text-lg font-medium">知识产权 ({{ intellectualPropertyFiles.length }})</span>
        <span class="ml-2 text-gray-500">专利、商标、版权及软件著作权等知识产权档案</span>
      </div>
      <div>
        <a-space>
          <a-select
            v-model:value="filterIPType"
            placeholder="按知识产权类型筛选"
            style="width: 180px"
            allow-clear
            @change="handleIPTypeChange"
          >
            <a-select-option :value="IntellectualPropertyType.PATENT">专利</a-select-option>
            <a-select-option :value="IntellectualPropertyType.TRADEMARK">商标</a-select-option>
            <a-select-option :value="IntellectualPropertyType.COPYRIGHT">版权</a-select-option>
            <a-select-option :value="IntellectualPropertyType.SOFTWARE">软件著作权</a-select-option>
          </a-select>
          <a-select
            v-model:value="filterIPStatus"
            placeholder="按状态筛选"
            style="width: 180px"
            allow-clear
            @change="handleIPStatusChange"
          >
            <a-select-option :value="IPStatus.PENDING">申请中</a-select-option>
            <a-select-option :value="IPStatus.GRANTED">已授权</a-select-option>
            <a-select-option :value="IPStatus.REJECTED">已驳回</a-select-option>
            <a-select-option :value="IPStatus.EXPIRED">已过期</a-select-option>
          </a-select>
          <a-button @click="exportToExcel">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表格区域 -->
    <a-table
      :dataSource="filteredIPFiles"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :rowKey="(record: IntellectualPropertyFile) => record.id"
      @change="handleTableChange"
      bordered
    >
      <!-- 表格列内容 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <a @click="showFileDetails(record.id)">{{ record.name }}</a>
        </template>
        
        <template v-else-if="column.dataIndex === 'ipType'">
          <a-tag :color="getIPTypeColor(record.ipType)">
            {{ getIPTypeName(record.ipType) }}
          </a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'ipStatus'">
          <a-tag :color="getIPStatusColor(record.ipStatus)">
            {{ getIPStatusName(record.ipStatus) }}
          </a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'type'">
          <a-tag :color="getFileTypeColor(record.type)">
            {{ getFileTypeName(record.type) }}
          </a-tag>
        </template>
        
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="previewFile(record.id)">
              <template #icon><eye-outlined /></template>
              预览
            </a-button>
            <a-button type="link" size="small" @click="downloadFile(record.id)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                <template #icon><more-outlined /></template>
                更多
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="showVersions(record.id)">
                    <history-outlined />
                    版本历史
                  </a-menu-item>
                  <a-menu-item @click="editFile(record.id)">
                    <edit-outlined />
                    编辑信息
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="showDeleteConfirm(record.id)">
                    <delete-outlined />
                    <span class="text-danger">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情抽屉将在需要时添加 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { 
  EyeOutlined, 
  DownloadOutlined, 
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import { 
  FileType, FileCategory, IPStatus, IntellectualPropertyType,
  type IntellectualPropertyFile, type PaginationInfo
} from '@/types/file';

const router = useRouter();
const fileStore = useFileStore();

// 表格列定义
const columns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '文件编号',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '知识产权类型',
    dataIndex: 'ipType',
    key: 'ipType',
    width: 120
  },
  {
    title: '申请号/注册号',
    dataIndex: 'applicationNumber',
    key: 'applicationNumber',
    width: 150
  },
  {
    title: '申请日期',
    dataIndex: 'applicationDate',
    key: 'applicationDate',
    width: 120,
    sorter: true
  },
  {
    title: '授权日期',
    dataIndex: 'grantDate',
    key: 'grantDate',
    width: 120,
    sorter: true
  },
  {
    title: '到期日期',
    dataIndex: 'expiryDate',
    key: 'expiryDate',
    width: 120,
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'ipStatus',
    key: 'ipStatus',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 200
  }
];

// 筛选状态
const filterIPType = ref<IntellectualPropertyType | undefined>(undefined);
const filterIPStatus = ref<IPStatus | undefined>(undefined);

// 分页配置
const pagination = reactive<PaginationInfo>({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total: number) => `共 ${total} 条`
});

// 计算属性
const loading = computed(() => fileStore.loading);
const intellectualPropertyFiles = computed(() => fileStore.intellectualPropertyFiles);

// 筛选后的文件列表
const filteredIPFiles = computed(() => {
  let files = [...intellectualPropertyFiles.value];
  
  // 按知识产权类型筛选
  if (filterIPType.value) {
    files = files.filter(file => file.ipType === filterIPType.value);
  }
  
  // 按状态筛选
  if (filterIPStatus.value) {
    files = files.filter(file => file.ipStatus === filterIPStatus.value);
  }
  
  return files;
});

// 初始化
onMounted(async () => {
  if (intellectualPropertyFiles.value.length === 0) {
    await fileStore.fetchFiles();
  }
  
  // 更新分页总数
  pagination.total = filteredIPFiles.value.length;
});

// 处理知识产权类型变更
const handleIPTypeChange = (value: IntellectualPropertyType | undefined) => {
  filterIPType.value = value;
  pagination.current = 1;
};

// 处理状态变更
const handleIPStatusChange = (value: IPStatus | undefined) => {
  filterIPStatus.value = value;
  pagination.current = 1;
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

// 显示文件详情
const showFileDetails = (id: string) => {
  message.info(`显示文件详情: ${id}`);
  // 实际应用中应该打开详情抽屉
};

// 预览文件
const previewFile = (id: string) => {
  router.push(`/files/preview/${id}`);
};

// 下载文件
const downloadFile = (id: string) => {
  message.success('文件开始下载');
  // 实际应用中应调用下载API
};

// 显示版本历史
const showVersions = async (id: string) => {
  router.push(`/files/versions/${id}`);
};

// 编辑文件
const editFile = (id: string) => {
  message.info(`编辑文件: ${id}`);
  // 实际应用中应该打开编辑弹窗
};

// 确认删除
const showDeleteConfirm = (id: string) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除此文件吗？此操作不可逆。',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await fileStore.deleteFile(id);
      if (success) {
        message.success('文件已删除');
      }
    }
  });
};

// 导出为Excel
const exportToExcel = () => {
  message.success('正在导出文件列表...');
  // 实际应用中应调用导出API
};

// 工具函数：获取知识产权类型名称
const getIPTypeName = (type: IntellectualPropertyType): string => {
  const names: Record<IntellectualPropertyType, string> = {
    [IntellectualPropertyType.PATENT]: '专利',
    [IntellectualPropertyType.TRADEMARK]: '商标',
    [IntellectualPropertyType.COPYRIGHT]: '版权',
    [IntellectualPropertyType.SOFTWARE]: '软件著作权'
  };
  
  return names[type] || '未知类型';
};

// 工具函数：获取知识产权类型颜色
const getIPTypeColor = (type: IntellectualPropertyType): string => {
  const colors: Record<IntellectualPropertyType, string> = {
    [IntellectualPropertyType.PATENT]: 'blue',
    [IntellectualPropertyType.TRADEMARK]: 'purple',
    [IntellectualPropertyType.COPYRIGHT]: 'cyan',
    [IntellectualPropertyType.SOFTWARE]: 'green'
  };
  
  return colors[type] || 'default';
};

// 工具函数：获取状态名称
const getIPStatusName = (status: IPStatus): string => {
  const names: Record<IPStatus, string> = {
    [IPStatus.PENDING]: '申请中',
    [IPStatus.GRANTED]: '已授权',
    [IPStatus.REJECTED]: '已驳回',
    [IPStatus.EXPIRED]: '已过期'
  };
  
  return names[status] || '未知状态';
};

// 工具函数：获取状态颜色
const getIPStatusColor = (status: IPStatus): string => {
  const colors: Record<IPStatus, string> = {
    [IPStatus.PENDING]: 'processing',
    [IPStatus.GRANTED]: 'success',
    [IPStatus.REJECTED]: 'error',
    [IPStatus.EXPIRED]: 'warning'
  };
  
  return colors[status] || 'default';
};

// 工具函数：获取文件类型名称
const getFileTypeName = (type: FileType): string => {
  const names: Record<FileType, string> = {
    [FileType.PDF]: 'PDF',
    [FileType.WORD]: 'Word',
    [FileType.EXCEL]: 'Excel',
    [FileType.IMAGE]: '图片',
    [FileType.CODE]: '代码',
    [FileType.ZIP]: '压缩包',
    [FileType.OTHER]: '其他'
  };
  
  return names[type] || '未知类型';
};

// 工具函数：获取文件类型颜色
const getFileTypeColor = (type: FileType): string => {
  const colors: Record<FileType, string> = {
    [FileType.PDF]: 'red',
    [FileType.WORD]: 'blue',
    [FileType.EXCEL]: 'green',
    [FileType.IMAGE]: 'purple',
    [FileType.CODE]: 'cyan',
    [FileType.ZIP]: 'orange',
    [FileType.OTHER]: 'default'
  };
  
  return colors[type] || 'default';
};
</script>

<style scoped>
@media (max-width: 768px) {
  .file-table-wrapper {
    margin-top: 16px;
  }
}
</style> 