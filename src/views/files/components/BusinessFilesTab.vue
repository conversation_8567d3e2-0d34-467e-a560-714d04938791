<template>
  <div class="business-files-tab">
    <!-- 统计和工具栏区域 -->
    <div class="flex flex-wrap justify-between items-center mb-4">
      <div>
        <span class="text-lg font-medium">工商档案 ({{ businessFiles.length }})</span>
        <span class="ml-2 text-gray-500">企业的营业执照、税务登记等工商相关证件</span>
      </div>
      <div>
        <a-space>
          <a-select
            v-model:value="filterBusinessType"
            placeholder="按档案类型筛选"
            style="width: 180px"
            allow-clear
            @change="handleBusinessTypeChange"
          >
            <a-select-option :value="BusinessFileType.BUSINESS_LICENSE">营业执照</a-select-option>
            <a-select-option :value="BusinessFileType.TAX_REGISTRATION">税务登记证</a-select-option>
            <a-select-option :value="BusinessFileType.ORGANIZATION_CODE">组织机构代码证</a-select-option>
            <a-select-option :value="BusinessFileType.BANK_ACCOUNT">银行开户许可证</a-select-option>
            <a-select-option :value="BusinessFileType.OTHER">其他</a-select-option>
          </a-select>
          <a-select
            v-model:value="filterSubject"
            placeholder="按登记主体筛选"
            style="width: 180px"
            allow-clear
            @change="handleSubjectChange"
          >
            <a-select-option v-for="subject in subjects" :key="subject" :value="subject">
              {{ subject }}
            </a-select-option>
          </a-select>
          <a-button @click="exportToExcel">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表格区域 -->
    <a-table
      :dataSource="filteredBusinessFiles"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :rowKey="(record: BusinessFile) => record.id"
      @change="handleTableChange"
      bordered
    >
      <!-- 文件名称列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <a @click="showFileDetails(record.id)">{{ record.name }}</a>
        </template>
        
        <!-- 档案类型列 -->
        <template v-else-if="column.dataIndex === 'businessType'">
          <a-tag :color="getBusinessTypeColor(record.businessType)">
            {{ getBusinessTypeName(record.businessType) }}
          </a-tag>
        </template>
        
        <!-- 文件类型列 -->
        <template v-else-if="column.dataIndex === 'type'">
          <a-tag :color="getFileTypeColor(record.type)">
            {{ getFileTypeName(record.type) }}
          </a-tag>
        </template>
        
        <!-- 状态列 -->
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusName(record.status) }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="previewFile(record.id)">
              <template #icon><eye-outlined /></template>
              预览
            </a-button>
            <a-button type="link" size="small" @click="downloadFile(record.id)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                <template #icon><more-outlined /></template>
                更多
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="showVersions(record.id)">
                    <history-outlined />
                    版本历史
                  </a-menu-item>
                  <a-menu-item @click="editFile(record.id)">
                    <edit-outlined />
                    编辑信息
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="showDeleteConfirm(record.id)">
                    <delete-outlined />
                    <span class="text-danger">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情抽屉 -->
    <a-drawer
      v-model:visible="detailsVisible"
      title="档案详情"
      width="600"
      :footer-style="{ textAlign: 'right' }"
      @close="closeDetails"
    >
      <template v-if="selectedFile">
        <a-descriptions bordered :column="1">
          <a-descriptions-item label="文件名称">{{ selectedFile.name }}</a-descriptions-item>
          <a-descriptions-item label="文件编号">{{ selectedFile.code }}</a-descriptions-item>
          <a-descriptions-item label="档案类型">
            {{ getBusinessTypeName(selectedFile.businessType) }}
          </a-descriptions-item>
          <a-descriptions-item label="登记日期">
            {{ selectedFile.registrationDate || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="有效期至">
            {{ selectedFile.expiryDate || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="登记主体">
            {{ selectedFile.subject || '本公司' }}
          </a-descriptions-item>
          <a-descriptions-item label="登记机关">
            {{ selectedFile.registrationAuthority || '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="上传时间">{{ selectedFile.uploadTime }}</a-descriptions-item>
          <a-descriptions-item label="上传人">{{ selectedFile.createdBy }}</a-descriptions-item>
          <a-descriptions-item label="文件大小">
            {{ formatFileSize(selectedFile.size) }}
          </a-descriptions-item>
          <a-descriptions-item label="关联项目">
            {{ selectedFile.projectId ? `项目${selectedFile.projectId}` : '无关联项目' }}
          </a-descriptions-item>
          <a-descriptions-item label="标签">
            <template v-if="selectedFile.tags && selectedFile.tags.length > 0">
              <a-tag v-for="tag in selectedFile.tags" :key="tag">{{ tag }}</a-tag>
            </template>
            <template v-else>无标签</template>
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedFile.description" label="描述">
            {{ selectedFile.description }}
          </a-descriptions-item>
        </a-descriptions>
        
        <div class="flex justify-end mt-4">
          <a-space>
            <a-button @click="closeDetails">关闭</a-button>
            <a-button type="primary" @click="previewFile(selectedFile.id)">
              <template #icon><eye-outlined /></template>
              预览
            </a-button>
            <a-button @click="downloadFile(selectedFile.id)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
          </a-space>
        </div>
      </template>
      <a-empty v-else description="未找到文件详情" />
    </a-drawer>

    <!-- 编辑抽屉 -->
    <a-drawer
      v-model:visible="editVisible"
      title="编辑档案信息"
      width="500"
      :footer-style="{ textAlign: 'right' }"
      @close="closeEdit"
    >
      <a-form
        v-if="editForm"
        :model="editForm"
        layout="vertical"
      >
        <a-form-item label="文件名称" name="name" :rules="[{ required: true, message: '请输入文件名称' }]">
          <a-input v-model:value="editForm.name" placeholder="请输入文件名称" />
        </a-form-item>
        
        <a-form-item label="档案类型" name="businessType" :rules="[{ required: true, message: '请选择档案类型' }]">
          <a-select v-model:value="editForm.businessType" placeholder="请选择档案类型">
            <a-select-option :value="BusinessFileType.BUSINESS_LICENSE">营业执照</a-select-option>
            <a-select-option :value="BusinessFileType.TAX_REGISTRATION">税务登记证</a-select-option>
            <a-select-option :value="BusinessFileType.ORGANIZATION_CODE">组织机构代码证</a-select-option>
            <a-select-option :value="BusinessFileType.BANK_ACCOUNT">银行开户许可证</a-select-option>
            <a-select-option :value="BusinessFileType.OTHER">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="登记日期" name="registrationDate">
          <a-date-picker 
            v-model:value="editForm.registrationDate"
            style="width: 100%" 
            format="YYYY-MM-DD" 
          />
        </a-form-item>
        
        <a-form-item label="有效期至" name="expiryDate">
          <a-date-picker 
            v-model:value="editForm.expiryDate"
            style="width: 100%" 
            format="YYYY-MM-DD" 
          />
        </a-form-item>
        
        <a-form-item label="登记主体" name="subject">
          <a-input v-model:value="editForm.subject" placeholder="请输入登记主体，如子公司名称" />
        </a-form-item>
        
        <a-form-item label="登记机关" name="registrationAuthority">
          <a-input v-model:value="editForm.registrationAuthority" placeholder="请输入登记机关" />
        </a-form-item>
        
        <a-form-item label="关联项目" name="projectId">
          <a-select v-model:value="editForm.projectId" placeholder="请选择关联项目" allow-clear>
            <a-select-option value="P1001">项目1001</a-select-option>
            <a-select-option value="P1002">项目1002</a-select-option>
            <a-select-option value="P1003">项目1003</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="标签" name="tags">
          <a-select
            v-model:value="editForm.tags"
            mode="tags"
            placeholder="请输入标签"
            style="width: 100%"
          ></a-select>
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="editForm.description"
            placeholder="请输入文件描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <a-space>
          <a-button @click="closeEdit">取消</a-button>
          <a-button type="primary" @click="saveEdit" :loading="saving">保存</a-button>
        </a-space>
      </template>
    </a-drawer>

    <!-- 版本历史抽屉 -->
    <a-drawer
      v-model:visible="versionsVisible"
      title="版本历史"
      width="600"
      @close="closeVersions"
    >
      <a-timeline v-if="fileVersions.length > 0">
        <a-timeline-item 
          v-for="version in fileVersions" 
          :key="version.id"
          :color="version.versionNumber === 'v1.0' ? 'green' : 'blue'"
        >
          <div class="version-item">
            <div class="flex justify-between items-center">
              <span class="font-bold">{{ version.versionNumber }}</span>
              <span class="text-gray-500">{{ version.changeTime }}</span>
            </div>
            <div class="mt-2 mb-2">{{ version.changeNote }}</div>
            <div class="flex justify-between items-center text-gray-500">
              <span>大小: {{ formatFileSize(version.size) }}</span>
              <span>上传人: {{ version.createdBy }}</span>
            </div>
            <div class="mt-2">
              <a-space>
                <a-button size="small" @click="previewVersion(version.id)">
                  <template #icon><eye-outlined /></template>
                  预览
                </a-button>
                <a-button size="small" @click="downloadVersion(version.id)">
                  <template #icon><download-outlined /></template>
                  下载
                </a-button>
                <a-button 
                  v-if="version.versionNumber !== 'v1.0'"
                  size="small" 
                  type="primary"
                  @click="compareVersions(version.id)"
                >
                  <template #icon><diff-outlined /></template>
                  对比差异
                </a-button>
              </a-space>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
      <a-empty v-else description="暂无版本历史记录" />
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  EyeOutlined, 
  DownloadOutlined, 
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  HistoryOutlined,
  DiffOutlined
} from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import { 
  FileType, FileCategory, FileStatus, BusinessFileType,
  type BusinessFile, type FileVersion, type PaginationInfo
} from '@/types/file';

const router = useRouter();
const fileStore = useFileStore();

// 表格列定义
const columns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true
  },
  {
    title: '文件编号',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '档案类型',
    dataIndex: 'businessType',
    key: 'businessType',
    width: 120
  },
  {
    title: '登记日期',
    dataIndex: 'registrationDate',
    key: 'registrationDate',
    width: 120,
    sorter: true
  },
  {
    title: '有效期至',
    dataIndex: 'expiryDate',
    key: 'expiryDate',
    width: 120,
    sorter: true
  },
  {
    title: '登记主体',
    dataIndex: 'subject',
    key: 'subject',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 200
  }
];

// 筛选状态
const filterBusinessType = ref<BusinessFileType | undefined>(undefined);
const filterSubject = ref<string | undefined>(undefined);

// 抽屉状态
const detailsVisible = ref(false);
const editVisible = ref(false);
const versionsVisible = ref(false);
const selectedFileId = ref('');
const editForm = ref<any>(null);
const saving = ref(false);

// 文件版本列表
const fileVersions = ref<FileVersion[]>([]);

// 分页配置
const pagination = reactive<PaginationInfo>({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条`
});

// 计算属性
const loading = computed(() => fileStore.loading);
const businessFiles = computed(() => fileStore.businessFiles);

// 获取所有登记主体选项
const subjects = computed(() => {
  const subjectSet = new Set<string>();
  businessFiles.value.forEach(file => {
    if (file.subject) {
      subjectSet.add(file.subject);
    }
  });
  return Array.from(subjectSet);
});

// 筛选后的文件列表
const filteredBusinessFiles = computed(() => {
  let files = [...businessFiles.value];
  
  // 按档案类型筛选
  if (filterBusinessType.value) {
    files = files.filter(file => file.businessType === filterBusinessType.value);
  }
  
  // 按登记主体筛选
  if (filterSubject.value) {
    files = files.filter(file => file.subject === filterSubject.value);
  }
  
  return files;
});

// 当前选中的文件
const selectedFile = computed(() => {
  return businessFiles.value.find(file => file.id === selectedFileId.value);
});

// 初始化
onMounted(async () => {
  if (businessFiles.value.length === 0) {
    await fileStore.fetchFiles();
  }
  
  // 更新分页总数
  pagination.total = filteredBusinessFiles.value.length;
});

// 处理档案类型变更
const handleBusinessTypeChange = (value: BusinessFileType | undefined) => {
  filterBusinessType.value = value;
  pagination.current = 1;
};

// 处理登记主体变更
const handleSubjectChange = (value: string | undefined) => {
  filterSubject.value = value;
  pagination.current = 1;
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
};

// 显示文件详情
const showFileDetails = async (id: string) => {
  selectedFileId.value = id;
  detailsVisible.value = true;
};

// 关闭详情抽屉
const closeDetails = () => {
  detailsVisible.value = false;
  selectedFileId.value = '';
};

// 预览文件
const previewFile = (id: string) => {
  router.push(`/files/preview/${id}`);
};

// 下载文件
const downloadFile = (id: string) => {
  message.success('文件开始下载');
  // 实际应用中应调用下载API
};

// 显示版本历史
const showVersions = async (id: string) => {
  selectedFileId.value = id;
  versionsVisible.value = true;
  fileVersions.value = await fileStore.fetchFileVersions(id);
};

// 关闭版本历史抽屉
const closeVersions = () => {
  versionsVisible.value = false;
  fileVersions.value = [];
};

// 预览版本
const previewVersion = (versionId: string) => {
  message.info(`预览版本: ${versionId}`);
  // 实际应用中应导航到预览页面
};

// 下载版本
const downloadVersion = (versionId: string) => {
  message.success('文件版本开始下载');
  // 实际应用中应调用下载API
};

// 比较版本差异
const compareVersions = (versionId: string) => {
  message.info(`比较版本差异: ${versionId}`);
  // 实际应用中应导航到版本对比页面
};

// 编辑文件
const editFile = (id: string) => {
  selectedFileId.value = id;
  const file = businessFiles.value.find(f => f.id === id);
  
  if (file) {
    editForm.value = {
      name: file.name,
      businessType: file.businessType,
      registrationDate: file.registrationDate ? dayjs(file.registrationDate) : undefined,
      expiryDate: file.expiryDate ? dayjs(file.expiryDate) : undefined,
      subject: file.subject,
      registrationAuthority: file.registrationAuthority,
      projectId: file.projectId,
      tags: file.tags || [],
      description: file.description || ''
    };
    
    editVisible.value = true;
  } else {
    message.error('未找到文件信息');
  }
};

// 关闭编辑抽屉
const closeEdit = () => {
  editVisible.value = false;
  editForm.value = null;
};

// 保存编辑
const saveEdit = async () => {
  if (!editForm.value) return;
  
  saving.value = true;
  
  try {
    const updatedData: Partial<BusinessFile> = {
      name: editForm.value.name,
      businessType: editForm.value.businessType,
      registrationDate: editForm.value.registrationDate ? dayjs(editForm.value.registrationDate).format('YYYY-MM-DD') : undefined,
      expiryDate: editForm.value.expiryDate ? dayjs(editForm.value.expiryDate).format('YYYY-MM-DD') : undefined,
      subject: editForm.value.subject,
      registrationAuthority: editForm.value.registrationAuthority,
      projectId: editForm.value.projectId,
      tags: editForm.value.tags,
      description: editForm.value.description
    };
    
    const updated = await fileStore.updateFile(selectedFileId.value, updatedData);
    
    if (updated) {
      message.success('更新成功');
      closeEdit();
    }
  } catch (error) {
    console.error('更新文件失败', error);
    message.error('更新失败');
  } finally {
    saving.value = false;
  }
};

// 确认删除
const showDeleteConfirm = (id: string) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除此文件吗？此操作不可逆。',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await fileStore.deleteFile(id);
      if (success) {
        message.success('文件已删除');
      }
    }
  });
};

// 导出为Excel
const exportToExcel = () => {
  message.success('正在导出文件列表...');
  // 实际应用中应调用导出API
};

// 工具函数：格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} KB`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} MB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(2)} GB`;
  }
};

// 工具函数：获取档案类型名称
const getBusinessTypeName = (type: BusinessFileType): string => {
  const names: Record<BusinessFileType, string> = {
    [BusinessFileType.BUSINESS_LICENSE]: '营业执照',
    [BusinessFileType.TAX_REGISTRATION]: '税务登记证',
    [BusinessFileType.ORGANIZATION_CODE]: '组织机构代码证',
    [BusinessFileType.BANK_ACCOUNT]: '银行开户许可证',
    [BusinessFileType.OTHER]: '其他'
  };
  
  return names[type] || '未知类型';
};

// 工具函数：获取档案类型颜色
const getBusinessTypeColor = (type: BusinessFileType): string => {
  const colors: Record<BusinessFileType, string> = {
    [BusinessFileType.BUSINESS_LICENSE]: 'blue',
    [BusinessFileType.TAX_REGISTRATION]: 'green',
    [BusinessFileType.ORGANIZATION_CODE]: 'orange',
    [BusinessFileType.BANK_ACCOUNT]: 'purple',
    [BusinessFileType.OTHER]: 'default'
  };
  
  return colors[type] || 'default';
};

// 工具函数：获取文件类型名称
const getFileTypeName = (type: FileType): string => {
  const names: Record<FileType, string> = {
    [FileType.PDF]: 'PDF',
    [FileType.WORD]: 'Word',
    [FileType.EXCEL]: 'Excel',
    [FileType.IMAGE]: '图片',
    [FileType.CODE]: '代码',
    [FileType.ZIP]: '压缩包',
    [FileType.OTHER]: '其他'
  };
  
  return names[type] || '未知类型';
};

// 工具函数：获取文件类型颜色
const getFileTypeColor = (type: FileType): string => {
  const colors: Record<FileType, string> = {
    [FileType.PDF]: 'red',
    [FileType.WORD]: 'blue',
    [FileType.EXCEL]: 'green',
    [FileType.IMAGE]: 'purple',
    [FileType.CODE]: 'cyan',
    [FileType.ZIP]: 'orange',
    [FileType.OTHER]: 'default'
  };
  
  return colors[type] || 'default';
};

// 工具函数：获取状态名称
const getStatusName = (status: FileStatus): string => {
  const names: Record<FileStatus, string> = {
    [FileStatus.ACTIVE]: '有效',
    [FileStatus.EXPIRED]: '已过期',
    [FileStatus.EXPIRING_SOON]: '即将过期',
    [FileStatus.ARCHIVED]: '已归档',
    [FileStatus.DELETED]: '已删除'
  };
  
  return names[status] || '未知状态';
};

// 工具函数：获取状态颜色
const getStatusColor = (status: FileStatus): string => {
  const colors: Record<FileStatus, string> = {
    [FileStatus.ACTIVE]: 'success',
    [FileStatus.EXPIRED]: 'error',
    [FileStatus.EXPIRING_SOON]: 'warning',
    [FileStatus.ARCHIVED]: 'default',
    [FileStatus.DELETED]: 'default'
  };
  
  return colors[status] || 'default';
};
</script>

<style scoped>
.version-item {
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .file-table-wrapper {
    margin-top: 16px;
  }
}
</style> 