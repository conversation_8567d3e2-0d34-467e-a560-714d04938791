<template>
  <div class="file-preview-container">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="file-info">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/files/index">
              <home-outlined /> 文件中心
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>文件预览</a-breadcrumb-item>
          <a-breadcrumb-item>{{ file?.name || '加载中...' }}</a-breadcrumb-item>
        </a-breadcrumb>
        
        <div class="file-title">
          <span class="file-name">{{ file?.name }}</span>
          <a-tag v-if="file?.type" class="ml-2" :color="getFileTypeColor(file.type)">
            {{ getFileTypeName(file.type) }}
          </a-tag>
        </div>
      </div>
      
      <div class="action-buttons">
        <a-space>
          <a-button @click="downloadFile">
            <template #icon><download-outlined /></template>
            下载
          </a-button>
          <a-button @click="showVersionHistory">
            <template #icon><history-outlined /></template>
            版本历史
          </a-button>
          <a-dropdown :trigger="['click']">
            <a-button>
              <template #icon><more-outlined /></template>
              更多
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="printFile">
                  <printer-outlined /> 打印
                </a-menu-item>
                <a-menu-item @click="showPermissionSettings">
                  <setting-outlined /> 权限设置
                </a-menu-item>
                <a-menu-item @click="navigateToEdit">
                  <edit-outlined /> 编辑信息
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="goBack">
                  <left-outlined /> 返回列表
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>
    
    <!-- 预览区域 -->
    <div class="preview-content">
      <a-spin :spinning="loading" tip="加载文件中...">
        <!-- PDF预览 -->
        <template v-if="file?.type === FileType.PDF">
          <div class="pdf-container">
            <div class="pdf-toolbar">
              <div class="pdf-pagination">
                <a-button 
                  :disabled="currentPage <= 1" 
                  @click="prevPage"
                >
                  <template #icon><left-outlined /></template>
                </a-button>
                <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
                <a-button 
                  :disabled="currentPage >= totalPages" 
                  @click="nextPage"
                >
                  <template #icon><right-outlined /></template>
                </a-button>
              </div>
              
              <div class="pdf-zoom">
                <a-button @click="zoomOut" :disabled="zoom <= 0.5">
                  <template #icon><minus-outlined /></template>
                </a-button>
                <span class="zoom-info">{{ Math.round(zoom * 100) }}%</span>
                <a-button @click="zoomIn" :disabled="zoom >= 2">
                  <template #icon><plus-outlined /></template>
                </a-button>
              </div>
              
              <div class="pdf-search">
                <a-input-search
                  v-model:value="searchText"
                  placeholder="搜索文档"
                  enter-button
                  @search="searchInDocument"
                />
              </div>
            </div>
            
            <div class="pdf-viewer">
              <!-- 假设PDF预览组件 -->
              <div class="pdf-canvas-container">
                <div 
                  class="pdf-canvas" 
                  :style="{ transform: `scale(${zoom})` }"
                ></div>
              </div>
            </div>
          </div>
        </template>
        
        <!-- 图片预览 -->
        <template v-else-if="file?.type === FileType.IMAGE">
          <div class="image-container">
            <div class="image-toolbar">
              <a-button @click="zoomOut" :disabled="zoom <= 0.5">
                <template #icon><minus-outlined /></template>
              </a-button>
              <span class="zoom-info">{{ Math.round(zoom * 100) }}%</span>
              <a-button @click="zoomIn" :disabled="zoom >= 3">
                <template #icon><plus-outlined /></template>
              </a-button>
              <a-button @click="rotateImage">
                <template #icon><rotate-right-outlined /></template>
              </a-button>
            </div>
            
            <div class="image-viewer">
              <img 
                :src="imageSrc"
                alt="预览图片"
                :style="{ 
                  transform: `scale(${zoom}) rotate(${rotation}deg)`,
                  maxWidth: '100%',
                  maxHeight: '100%'
                }"
                @load="imageLoaded"
                @error="imageError"
              />
            </div>
          </div>
        </template>
        
        <!-- Word文档预览 -->
        <template v-else-if="file?.type === FileType.WORD">
          <div class="document-container">
            <div class="document-content">
              <!-- 实际应用中应使用专业的Word文档预览组件 -->
              <div class="document-preview">
                <div class="word-page">
                  <h1>{{ file?.name }}</h1>
                  <p>Word文档预览内容...</p>
                  <p>实际应用中应使用专业的文档预览组件，如Office Viewer</p>
                </div>
              </div>
            </div>
          </div>
        </template>
        
        <!-- Excel预览 -->
        <template v-else-if="file?.type === FileType.EXCEL">
          <div class="excel-container">
            <!-- 实际应用中应使用专业的Excel预览组件 -->
            <a-table
              :columns="excelColumns"
              :dataSource="excelData"
              bordered
              size="small"
              :pagination="false"
              :scroll="{ x: '100%', y: 500 }"
            />
          </div>
        </template>
        
        <!-- 代码预览 -->
        <template v-else-if="file?.type === FileType.CODE">
          <div class="code-container">
            <div class="code-toolbar">
              <a-select v-model:value="codeTheme" style="width: 150px">
                <a-select-option value="light">浅色主题</a-select-option>
                <a-select-option value="dark">深色主题</a-select-option>
              </a-select>
              <a-select v-model:value="codeFontSize" style="width: 100px">
                <a-select-option :value="12">12px</a-select-option>
                <a-select-option :value="14">14px</a-select-option>
                <a-select-option :value="16">16px</a-select-option>
                <a-select-option :value="18">18px</a-select-option>
              </a-select>
            </div>
            
            <div 
              class="code-editor" 
              :class="{ 'theme-dark': codeTheme === 'dark' }"
              :style="{ fontSize: `${codeFontSize}px` }"
            >
              <pre><code>// 示例代码
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';

// 文件预览组件
export default {
  setup() {
    const file = ref(null);
    const loading = ref(true);
    
    onMounted(() => {
      // 加载文件内容
      loadFile();
    });
    
    const loadFile = async () => {
      // API调用获取文件
      loading.value = false;
    };
    
    return {
      file,
      loading
    };
  }
};</code></pre>
            </div>
          </div>
        </template>
        
        <!-- 其他文件类型预览（通用） -->
        <template v-else>
          <div class="generic-container">
            <a-result
              status="info"
              title="无法预览此类型的文件"
              sub-title="请尝试下载文件后在本地查看"
            >
              <template #extra>
                <a-button type="primary" @click="downloadFile">
                  下载文件
                </a-button>
                <a-button @click="goBack" style="margin-left: 8px">
                  返回列表
                </a-button>
              </template>
            </a-result>
          </div>
        </template>
      </a-spin>
    </div>
    
    <!-- 版本历史弹窗 -->
    <FileVersionsModal
      v-model:visible="versionsModalVisible"
      :file-id="fileId"
    />
    
    <!-- 权限设置弹窗 -->
    <FilePermissionsModal
      v-model:visible="permissionsModalVisible"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
  HomeOutlined,
  DownloadOutlined,
  HistoryOutlined,
  MoreOutlined,
  PrinterOutlined,
  SettingOutlined,
  EditOutlined,
  LeftOutlined,
  RightOutlined,
  MinusOutlined,
  PlusOutlined,
  RotateRightOutlined
} from '@ant-design/icons-vue';
import { useFileStore } from '@/stores/file';
import { FileType } from '@/types/file';
import FileVersionsModal from './components/FileVersionsModal.vue';
import FilePermissionsModal from './components/FilePermissionsModal.vue';

// 路由相关
const route = useRoute();
const router = useRouter();
const fileId = computed(() => route.params.id as string);

// Store
const fileStore = useFileStore();

// 状态
const loading = ref(true);
const file = ref<any>(null);
const versionsModalVisible = ref(false);
const permissionsModalVisible = ref(false);

// PDF预览相关
const currentPage = ref(1);
const totalPages = ref(1);
const zoom = ref(1);
const searchText = ref('');

// 图片预览相关
const imageSrc = ref('');
const rotation = ref(0);

// 代码预览相关
const codeTheme = ref('light');
const codeFontSize = ref(14);

// Excel预览数据
const excelColumns = [
  { title: 'A', dataIndex: 'a', key: 'a' },
  { title: 'B', dataIndex: 'b', key: 'b' },
  { title: 'C', dataIndex: 'c', key: 'c' },
  { title: 'D', dataIndex: 'd', key: 'd' },
  { title: 'E', dataIndex: 'e', key: 'e' },
  { title: 'F', dataIndex: 'f', key: 'f' },
  { title: 'G', dataIndex: 'g', key: 'g' }
];

const excelData = Array.from({ length: 20 }).map((_, index) => ({
  key: index,
  a: `A${index + 1}`,
  b: `B${index + 1}`,
  c: `C${index + 1}`,
  d: `D${index + 1}`,
  e: `E${index + 1}`,
  f: `F${index + 1}`,
  g: `G${index + 1}`
}));

// 初始化
onMounted(async () => {
  if (fileId.value) {
    await loadFile(fileId.value);
  } else {
    message.error('文件ID无效');
    router.replace('/files/index');
  }
});

// 加载文件
const loadFile = async (id: string) => {
  loading.value = true;
  
  try {
    const fileData = await fileStore.fetchFileById(id);
    
    if (fileData) {
      file.value = fileData;
      
      // 为不同类型的文件准备预览
      if (file.value.type === FileType.PDF) {
        setupPdfPreview();
      } else if (file.value.type === FileType.IMAGE) {
        setupImagePreview();
      }
    } else {
      message.error('文件不存在');
      router.replace('/files/index');
    }
  } catch (error) {
    console.error('加载文件失败', error);
    message.error('加载文件失败');
  } finally {
    loading.value = false;
  }
};

// 设置PDF预览
const setupPdfPreview = () => {
  // 在实际应用中应加载PDF.js等PDF预览库
  // 这里仅作为模拟
  totalPages.value = 10; // 假设PDF有10页
};

// 设置图片预览
const setupImagePreview = () => {
  // 实际应用中应该使用真实的文件路径
  imageSrc.value = `https://picsum.photos/800/600?random=${Math.random()}`;
};

// 图片加载成功
const imageLoaded = () => {
  message.success('图片加载成功');
};

// 图片加载失败
const imageError = () => {
  message.error('图片加载失败');
};

// PDF翻页相关
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 缩放相关
const zoomIn = () => {
  zoom.value = Math.min(zoom.value + 0.1, 3);
};

const zoomOut = () => {
  zoom.value = Math.max(zoom.value - 0.1, 0.5);
};

// 旋转图片
const rotateImage = () => {
  rotation.value = (rotation.value + 90) % 360;
};

// PDF搜索
const searchInDocument = (value: string) => {
  if (!value) {
    message.warning('请输入搜索关键词');
    return;
  }
  
  message.info(`搜索关键词: ${value}`);
  // 实际应用中应调用PDF.js的搜索功能
};

// 下载文件
const downloadFile = () => {
  message.success('文件开始下载');
  // 实际应用中应调用下载API
};

// 显示版本历史
const showVersionHistory = () => {
  versionsModalVisible.value = true;
};

// 显示权限设置
const showPermissionSettings = () => {
  permissionsModalVisible.value = true;
};

// 打印文件
const printFile = () => {
  message.info('准备打印文件');
  // 实际应用中应调用打印API
};

// 导航到编辑页
const navigateToEdit = () => {
  // 实际应用中应导航到编辑页面
  message.info('导航到编辑页面');
};

// 返回列表
const goBack = () => {
  router.push('/files/index');
};

// 工具函数：获取文件类型名称
const getFileTypeName = (type: FileType): string => {
  const names: Record<FileType, string> = {
    [FileType.PDF]: 'PDF',
    [FileType.WORD]: 'Word',
    [FileType.EXCEL]: 'Excel',
    [FileType.IMAGE]: '图片',
    [FileType.CODE]: '代码',
    [FileType.ZIP]: '压缩包',
    [FileType.OTHER]: '其他'
  };
  
  return names[type] || '未知类型';
};

// 工具函数：获取文件类型颜色
const getFileTypeColor = (type: FileType): string => {
  const colors: Record<FileType, string> = {
    [FileType.PDF]: 'red',
    [FileType.WORD]: 'blue',
    [FileType.EXCEL]: 'green',
    [FileType.IMAGE]: 'purple',
    [FileType.CODE]: 'cyan',
    [FileType.ZIP]: 'orange',
    [FileType.OTHER]: 'default'
  };
  
  return colors[type] || 'default';
};
</script>

<style scoped>
.file-preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.file-info {
  display: flex;
  flex-direction: column;
}

.file-title {
  margin-top: 8px;
}

.file-name {
  font-size: 18px;
  font-weight: bold;
}

.preview-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* PDF预览样式 */
.pdf-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.pdf-pagination,
.pdf-zoom {
  display: flex;
  align-items: center;
}

.page-info,
.zoom-info {
  margin: 0 8px;
  width: 80px;
  text-align: center;
}

.pdf-search {
  width: 250px;
}

.pdf-viewer {
  flex: 1;
  overflow: auto;
  padding: 24px;
  display: flex;
  justify-content: center;
}

.pdf-canvas-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  overflow: auto;
}

.pdf-canvas {
  width: 100%;
  height: 1200px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  transform-origin: center center;
  /* 在实际应用中，这里会被Canvas元素替换 */
  /* 模拟PDF页面效果 */
  background: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 图片预览样式 */
.image-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.image-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  gap: 8px;
}

.image-viewer {
  flex: 1;
  overflow: auto;
  padding: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
}

.image-viewer img {
  transition: transform 0.3s;
  max-width: 100%;
}

/* 文档预览样式 */
.document-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.document-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
}

.word-page {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 1100px;
}

/* Excel预览样式 */
.excel-container {
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 16px;
}

/* 代码预览样式 */
.code-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.code-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  gap: 8px;
}

.code-editor {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: #f8f8f8;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
}

.code-editor pre {
  margin: 0;
  padding: 16px;
  border-radius: 4px;
  background-color: #f8f8f8;
  overflow: auto;
}

.code-editor.theme-dark pre {
  background-color: #282c34;
  color: #abb2bf;
}

/* 通用容器样式 */
.generic-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
</style> 