<template>
  <div class="file-permissions-wrapper">
    <a-card :bordered="false">
      <template #title>
        <div class="flex items-center">
          <router-link to="/files/index" class="mr-2">
            <a-button><left-outlined /> 返回</a-button>
          </router-link>
          <span class="text-lg font-medium">文件权限管理</span>
        </div>
      </template>
      
      <!-- Embed the FilePermissionsModal component directly -->
      <div class="permissions-container">
        <FilePermissionsModal
          v-model="visible"
          @update:modelValue="handleClose"
        />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { LeftOutlined } from '@ant-design/icons-vue';
import FilePermissionsModal from './components/FilePermissionsModal.vue';

const router = useRouter();
const visible = ref(true);

// Handle modal close
const handleClose = (isVisible: boolean) => {
  if (!isVisible) {
    router.push('/files/index');
  }
};
</script>

<style scoped>
.file-permissions-wrapper {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.permissions-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}
</style> 