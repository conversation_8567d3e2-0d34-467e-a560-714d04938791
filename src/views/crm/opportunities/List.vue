<template>
  <div class="bg-white p-6 rounded-lg">
    <!-- 搜索区域 -->
    <div class="mb-4">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="客户名称">
          <a-input v-model:value="searchForm.customerName" placeholder="请输入客户名称" allow-clear />
        </a-form-item>
        <a-form-item label="商机描述">
          <a-input v-model:value="searchForm.description" placeholder="请输入商机描述" allow-clear />
        </a-form-item>
        <a-form-item label="跟进状态">
          <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 200px" allow-clear>
            <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon><redo-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="mb-4">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增机会
        </a-button>
      </a-space>
    </div>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 预计金额 -->
      <template #expectedAmount="{ text }">
        ¥{{ text.toLocaleString() }}
      </template>

      <!-- 跟进状态 -->
      <template #status="{ text }">
        <a-progress :percent="text" size="small" />
      </template>

      <!-- 预计签约时间 -->
      <template #expectedDate="{ text }">
        {{ formatDate(text) }}
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a @click="handleView(record)">查看</a>
          <a @click="handleEdit(record)">编辑</a>
          <a @click="handleTracking(record)">跟进</a>
          <a-popconfirm
            title="确定要删除此销售机会吗？"
            @confirm="handleDelete(record)"
            ok-text="确定"
            cancel-text="取消"
          >
            <a class="text-red-500">删除</a>
          </a-popconfirm>
        </a-space>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="800px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="客户名称" name="customerName">
          <a-select
            v-model:value="formData.customerId"
            placeholder="请选择客户"
            :options="customerOptions"
            show-search
            :filter-option="filterOption"
          />
        </a-form-item>
        <a-form-item label="商机描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入商机描述" :rows="4" />
        </a-form-item>
        <a-form-item label="预计金额" name="expectedAmount">
          <a-input-number
            v-model:value="formData.expectedAmount"
            placeholder="请输入预计金额"
            :min="0"
            :step="1000"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="预计签约时间" name="expectedDate">
          <a-date-picker
            v-model:value="formData.expectedDate"
            placeholder="请选择预计签约时间"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="跟进状态" name="status">
          <a-select v-model:value="formData.status" placeholder="请选择状态">
            <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 跟进记录弹窗 -->
    <a-modal
      v-model:visible="trackingModalVisible"
      title="添加跟进记录"
      @ok="handleTrackingModalOk"
      @cancel="handleTrackingModalCancel"
    >
      <a-form
        ref="trackingFormRef"
        :model="trackingForm"
        :rules="trackingRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="跟进内容" name="content">
          <a-textarea v-model:value="trackingForm.content" placeholder="请输入跟进内容" :rows="4" />
        </a-form-item>
        <a-form-item label="跟进方式" name="method">
          <a-select v-model:value="trackingForm.method" placeholder="请选择跟进方式">
            <a-select-option value="电话">电话</a-select-option>
            <a-select-option value="邮件">邮件</a-select-option>
            <a-select-option value="拜访">拜访</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="下次跟进" name="nextDate">
          <a-date-picker
            v-model:value="trackingForm.nextDate"
            placeholder="请选择下次跟进时间"
            style="width: 200px"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  SearchOutlined,
  RedoOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';

// 状态选项
const statusOptions = [
  { label: '20%', value: 20 },
  { label: '40%', value: 40 },
  { label: '60%', value: 60 },
  { label: '80%', value: 80 },
  { label: '100%', value: 100 },
];

// 客户选项（模拟数据）
const customerOptions = [
  { label: '杭州软件有限公司', value: 1 },
  { label: '上海科技有限公司', value: 2 },
];

// 搜索表单
const searchForm = reactive({
  customerName: '',
  description: '',
  status: undefined,
});

// 表格列定义
const columns = [
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 200,
  },
  {
    title: '商机描述',
    dataIndex: 'description',
    key: 'description',
    width: 300,
  },
  {
    title: '预计金额',
    dataIndex: 'expectedAmount',
    key: 'expectedAmount',
    width: 150,
    slots: { customRender: 'expectedAmount' },
  },
  {
    title: '跟进状态',
    dataIndex: 'status',
    key: 'status',
    width: 150,
    slots: { customRender: 'status' },
  },
  {
    title: '预计签约时间',
    dataIndex: 'expectedDate',
    key: 'expectedDate',
    width: 150,
    slots: { customRender: 'expectedDate' },
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];

// 模拟数据
const dataSource = ref([
  {
    id: 1,
    customerName: '杭州软件有限公司',
    customerId: 1,
    description: '企业管理系统开发项目',
    expectedAmount: 500000,
    status: 60,
    expectedDate: '2024-06-30',
  },
  // 更多数据...
]);

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 表单相关
const modalVisible = ref(false);
const modalTitle = ref('新增销售机会');
const formRef = ref();
const formData = reactive({
  customerId: undefined,
  description: '',
  expectedAmount: undefined,
  expectedDate: undefined,
  status: undefined,
});

// 表单校验规则
const rules = {
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  description: [{ required: true, message: '请输入商机描述', trigger: 'blur' }],
  expectedAmount: [{ required: true, message: '请输入预计金额', trigger: 'change' }],
  expectedDate: [{ required: true, message: '请选择预计签约时间', trigger: 'change' }],
  status: [{ required: true, message: '请选择跟进状态', trigger: 'change' }],
};

// 跟进记录相关
const trackingModalVisible = ref(false);
const trackingFormRef = ref();
const trackingForm = reactive({
  content: '',
  method: undefined,
  nextDate: undefined,
});

// 跟进记录表单校验规则
const trackingRules = {
  content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
  method: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
  nextDate: [{ required: true, message: '请选择下次跟进时间', trigger: 'change' }],
};

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 客户选择器过滤
function filterOption(input: string, option: any) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

// 处理搜索
function handleSearch() {
  // TODO: 实现搜索逻辑
  message.success('搜索功能待实现');
}

// 处理重置
function handleReset() {
  searchForm.customerName = '';
  searchForm.description = '';
  searchForm.status = undefined;
}

// 处理表格变化
function handleTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // TODO: 加载数据
}

// 处理新增
function handleAdd() {
  modalTitle.value = '新增销售机会';
  modalVisible.value = true;
}

// 处理编辑
function handleEdit(record: any) {
  modalTitle.value = '编辑销售机会';
  Object.assign(formData, record);
  modalVisible.value = true;
}

// 处理查看
function handleView(record: any) {
  // TODO: 实现查看详情逻辑
  message.success('查看详情功能待实现');
}

// 处理跟进
function handleTracking(record: any) {
  trackingModalVisible.value = true;
}

// 处理删除
function handleDelete(record: any) {
  // TODO: 实现删除逻辑
  message.success('删除功能待实现');
}

// 处理弹窗确认
async function handleModalOk() {
  try {
    await formRef.value.validate();
    // TODO: 实现保存逻辑
    message.success('保存成功');
    modalVisible.value = false;
  } catch (error) {
    // 表单验证失败
  }
}

// 处理弹窗取消
function handleModalCancel() {
  formRef.value?.resetFields();
  modalVisible.value = false;
}

// 处理跟进记录弹窗确认
async function handleTrackingModalOk() {
  try {
    await trackingFormRef.value.validate();
    // TODO: 实现保存逻辑
    message.success('跟进记录已添加');
    trackingModalVisible.value = false;
  } catch (error) {
    // 表单验证失败
  }
}

// 处理跟进记录弹窗取消
function handleTrackingModalCancel() {
  trackingFormRef.value?.resetFields();
  trackingModalVisible.value = false;
}
</script> 