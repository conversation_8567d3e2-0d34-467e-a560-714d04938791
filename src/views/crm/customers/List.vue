<template>
  <div class="bg-white p-6 rounded-lg">
    <!-- 搜索区域 -->
    <div class="mb-4">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="客户名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入客户名称" allow-clear />
        </a-form-item>
        <a-form-item label="纳税人识别号">
          <a-input v-model:value="searchForm.taxId" placeholder="请输入纳税人识别号" allow-clear />
        </a-form-item>
        <a-form-item label="行业">
          <a-select v-model:value="searchForm.industry" placeholder="请选择行业" style="width: 200px" allow-clear>
            <a-select-option value="互联网">互联网</a-select-option>
            <a-select-option value="医疗">医疗</a-select-option>
            <a-select-option value="制造业">制造业</a-select-option>
            <a-select-option value="金融">金融</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon><redo-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="mb-4">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增客户
        </a-button>
        <a-button @click="handleExport">
          <template #icon><export-outlined /></template>
          导出
        </a-button>
      </a-space>
    </div>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      row-key="id"
    >
      <!-- 客户等级 -->
      <template #creditLevel="{ text }">
        <a-tag :color="getCreditLevelColor(text)">{{ text }}</a-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a @click="handleView(record)">查看</a>
          <a @click="handleEdit(record)">编辑</a>
          <a-popconfirm
            title="确定要删除此客户吗？"
            @confirm="handleDelete(record)"
            ok-text="确定"
            cancel-text="取消"
          >
            <a class="text-red-500">删除</a>
          </a-popconfirm>
        </a-space>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="800px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="客户名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入客户名称" />
        </a-form-item>
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="formData.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        <a-form-item label="行业" name="industry">
          <a-select v-model:value="formData.industry" placeholder="请选择行业">
            <a-select-option value="互联网">互联网</a-select-option>
            <a-select-option value="医疗">医疗</a-select-option>
            <a-select-option value="制造业">制造业</a-select-option>
            <a-select-option value="金融">金融</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="formData.contact" placeholder="请输入联系人" />
        </a-form-item>
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="信用等级" name="creditLevel">
          <a-select v-model:value="formData.creditLevel" placeholder="请选择信用等级">
            <a-select-option value="A">A</a-select-option>
            <a-select-option value="B">B</a-select-option>
            <a-select-option value="C">C</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remarks">
          <a-textarea v-model:value="formData.remarks" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  RedoOutlined,
  PlusOutlined,
  ExportOutlined,
} from '@ant-design/icons-vue';

// 搜索表单
const searchForm = reactive({
  name: '',
  taxId: '',
  industry: undefined,
});

// 表格列定义
const columns = [
  {
    title: '客户名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '纳税人识别号',
    dataIndex: 'taxId',
    key: 'taxId',
    width: 200,
  },
  {
    title: '行业',
    dataIndex: 'industry',
    key: 'industry',
    width: 120,
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    key: 'contact',
    width: 120,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 150,
  },
  {
    title: '信用等级',
    dataIndex: 'creditLevel',
    key: 'creditLevel',
    width: 100,
    slots: { customRender: 'creditLevel' },
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];

// 模拟数据
const dataSource = ref([
  {
    id: 1,
    name: '杭州软件有限公司',
    taxId: '91330100MA2B0D1X5R',
    industry: '互联网',
    contact: '张三',
    phone: '13812341234',
    creditLevel: 'A',
  },
  // 更多数据...
]);

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 表单相关
const modalVisible = ref(false);
const modalTitle = ref('新增客户');
const formRef = ref();
const formData = reactive({
  name: '',
  taxId: '',
  industry: undefined,
  contact: '',
  phone: '',
  creditLevel: undefined,
  remarks: '',
});

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  taxId: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
  industry: [{ required: true, message: '请选择行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  creditLevel: [{ required: true, message: '请选择信用等级', trigger: 'change' }],
};

// 获取信用等级标签颜色
function getCreditLevelColor(level: string) {
  const colors = {
    A: 'success',
    B: 'warning',
    C: 'error',
  };
  return colors[level] || 'default';
}

// 处理搜索
function handleSearch() {
  // TODO: 实现搜索逻辑
  message.success('搜索功能待实现');
}

// 处理重置
function handleReset() {
  searchForm.name = '';
  searchForm.taxId = '';
  searchForm.industry = undefined;
}

// 处理表格变化
function handleTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // TODO: 加载数据
}

// 处理新增
function handleAdd() {
  modalTitle.value = '新增客户';
  modalVisible.value = true;
}

// 处理编辑
function handleEdit(record: any) {
  modalTitle.value = '编辑客户';
  Object.assign(formData, record);
  modalVisible.value = true;
}

// 处理查看
function handleView(record: any) {
  // TODO: 实现查看详情逻辑
  message.success('查看详情功能待实现');
}

// 处理删除
function handleDelete(record: any) {
  // TODO: 实现删除逻辑
  message.success('删除功能待实现');
}

// 处理导出
function handleExport() {
  // TODO: 实现导出逻辑
  message.success('导出功能待实现');
}

// 处理弹窗确认
async function handleModalOk() {
  try {
    await formRef.value.validate();
    // TODO: 实现保存逻辑
    message.success('保存成功');
    modalVisible.value = false;
  } catch (error) {
    // 表单验证失败
  }
}

// 处理弹窗取消
function handleModalCancel() {
  formRef.value?.resetFields();
  modalVisible.value = false;
}
</script> 