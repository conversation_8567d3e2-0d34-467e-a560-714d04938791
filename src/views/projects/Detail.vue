<template>
  <div class="project-detail">
    <!-- 页面头部 -->
    <div class="mb-4 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <a-button @click="router.back()">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
        <h2 class="text-xl font-bold m-0">{{ projectInfo.name }}</h2>
        <a-tag :color="getStatusColor(projectInfo.status)">
          {{ getStatusName(projectInfo.status) }}
        </a-tag>
      </div>
    </div>

        <!-- 基本信息 -->
    <a-card class="mb-4">
      <template #title>基本信息</template>
      <template #extra>
        <a-button type="link" @click="handleEditProject" size="small">
          <template #icon><edit-outlined /></template>
          编辑
        </a-button>
      </template>
      <a-descriptions :column="{ xs: 1, sm: 2, md: 3, lg: 4 }" bordered>
        <a-descriptions-item label="项目编号">
          {{ projectInfo.code || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ projectInfo.name }}
        </a-descriptions-item>
        <a-descriptions-item label="项目类型">
          <a-tag>{{ getProjectTypeName(projectInfo.type) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="项目状态">
          <a-tag :color="getStatusColor(projectInfo.status)">
            {{ getStatusName(projectInfo.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="客户名称">
          <a @click="handleViewCustomer" class="text-blue-600 hover:text-blue-800">{{ projectInfo.customerName }}</a>
        </a-descriptions-item>
        <a-descriptions-item label="销售负责人">
          {{ projectInfo.managerName }}
        </a-descriptions-item>
        <a-descriptions-item label="预计毛利率">
          {{ projectInfo.expectedProfitRate ? `${projectInfo.expectedProfitRate}%` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="项目预算">
          {{ formatMoney(projectInfo.budget) }}
        </a-descriptions-item>
        <a-descriptions-item label="开始时间">
          {{ formatDate(projectInfo.startDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="预计结束">
          {{ formatDate(projectInfo.endDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDate(projectInfo.createTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ formatDate(projectInfo.updateTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="项目描述" :span="4">
          {{ projectInfo.description || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
      <!-- 客户信息 -->
      <a-card title="客户信息">
        <template #extra>
          <a-button type="link" size="small" @click="handleViewCustomer">
            <template #icon><eye-outlined /></template>
            查看详情
          </a-button>
        </template>
        <a-descriptions :column="1">
          <a-descriptions-item label="客户编号">
            {{ projectInfo.customerId || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户名称">
            <a @click="handleViewCustomer" class="text-blue-600 hover:text-blue-800">
              {{ projectInfo.customerName || '-' }}
            </a>
          </a-descriptions-item>
          <a-descriptions-item label="联系人">
            {{ projectInfo.customerContact || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ projectInfo.customerPhone || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 项目进度 -->
      <a-card title="项目进度">
        <div class="space-y-4">
          <div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-gray-600">整体进度</span>
              <span class="font-semibold">{{ projectInfo.progress }}%</span>
            </div>
            <a-progress :percent="projectInfo.progress" :stroke-color="getProgressColor(projectInfo.progress)" />
          </div>
          
          <a-divider class="my-3" />
          
          <div class="grid grid-cols-2 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-blue-600">{{ projectInfo.budget ? Math.floor(projectInfo.budget / 10000) : 0 }}</div>
              <div class="text-sm text-gray-500">预算（万元）</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-green-600">{{ getDaysRemaining() }}</div>
              <div class="text-sm text-gray-500">剩余天数</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 关联合同 -->
    <a-card title="关联合同" class="mb-4">
      <template #extra>
        <a-button type="link" size="small" @click="handleAddContract">
          <template #icon><plus-outlined /></template>
          关联合同
        </a-button>
      </template>
      <a-table
        :columns="contractColumns"
        :data-source="contractList"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a @click="handleViewContract(record)" class="text-blue-600 hover:text-blue-800">
              {{ record.name }}
            </a>
          </template>
          <template v-if="column.key === 'amount'">
            {{ formatMoney(record.amount) }}
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="getContractStatusColor(record.status)">
              {{ getContractStatusName(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'signDate'">
            {{ formatDate(record.signDate) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleViewContract(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleUnlinkContract(record)" danger>
                取消关联
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
      <div v-if="contractList.length === 0" class="text-center text-gray-500 py-8">
        暂无关联合同
      </div>
    </a-card>

    <!-- 项目附件 -->
    <a-card title="项目附件">
      <template #extra>
        <a-button type="link" size="small" @click="handleUploadAttachment">
          <template #icon><upload-outlined /></template>
          上传附件
        </a-button>
      </template>
      <a-table
        :columns="attachmentColumns"
        :data-source="attachmentList"
        :pagination="{ pageSize: 5 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getAttachmentTypeColor(record.type)">
              {{ getAttachmentTypeName(record.type) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleDownload(record)">
                下载
              </a-button>
              <a-button type="link" size="small" @click="handleDeleteAttachment(record)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 编辑项目抽屉 -->
    <a-drawer
      v-model:visible="editDrawerVisible"
      title="编辑项目信息"
      width="700px"
      placement="right"
      @close="editDrawerVisible = false"
    >
      <div class="text-center text-gray-500 mt-20">
        编辑项目功能待实现
      </div>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  LeftOutlined,
  EditOutlined,
  UploadOutlined,
  PlusOutlined,
  EyeOutlined,
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();

// 项目信息
const projectInfo = ref<any>({
  id: '',
  code: '',
  name: '',
  type: '',
  status: '',
  customerId: '',
  customerName: '',
  customerContact: '',
  customerPhone: '',
  managerName: '',
  expectedProfitRate: 0,
  budget: 0,
  progress: 0,
  startDate: '',
  endDate: '',
  createTime: '',
  updateTime: '',
  description: '',
});

// 编辑抽屉
const editDrawerVisible = ref(false);

// 合同列表
const contractList = ref<any[]>([]);

// 合同表格列配置
const contractColumns = [
  { title: '合同名称', dataIndex: 'name', key: 'name' },
  { title: '合同金额', dataIndex: 'amount', key: 'amount', width: 150 },
  { title: '合同状态', dataIndex: 'status', key: 'status', width: 120 },
  { title: '签署日期', dataIndex: 'signDate', key: 'signDate', width: 120 },
  { title: '操作', key: 'action', width: 140 },
];

// 附件列表
const attachmentList = ref<any[]>([]);

// 附件表格列配置
const attachmentColumns = [
  { title: '文件名', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type', width: 100 },
  { title: '大小', dataIndex: 'size', key: 'size', width: 100 },
  { title: '上传时间', dataIndex: 'uploadTime', key: 'uploadTime', width: 150 },
  { title: '操作', key: 'action', width: 120 },
];

// 获取项目类型名称
function getProjectTypeName(type: string) {
  const typeMap: Record<string, string> = {
    'system_integration': '系统集成',
    'software_development': '软件开发',
    'product_own': '产品（自有）',
    'product_external': '产品（外采）',
    'service_own': '服务（自有）',
    'service_external': '服务（外采）',
    'integration': '集成',
    'other': '其他（行政、租房、人事等）',
  };
  return typeMap[type] || type;
}

// 获取状态名称
function getStatusName(status: string) {
  const statusMap: Record<string, string> = {
    'pending': '准备中',
    'active': '进行中',
    'paused': '暂停',
    'completed': '已完成',
    'cancelled': '已取消',
  };
  return statusMap[status] || status;
}

// 获取状态颜色
function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    'pending': 'default',
    'active': 'processing',
    'paused': 'warning',
    'completed': 'success',
    'cancelled': 'error',
  };
  return colorMap[status] || 'default';
}

// 获取附件类型名称
function getAttachmentTypeName(type: string) {
  const typeMap: Record<string, string> = {
    'spreadsheet': '试算表',
    'document': '文档',
    'image': '图片',
    'other': '其他',
  };
  return typeMap[type] || type;
}

// 获取附件类型颜色
function getAttachmentTypeColor(type: string) {
  const colorMap: Record<string, string> = {
    'spreadsheet': 'green',
    'document': 'blue',
    'image': 'orange',
    'other': 'default',
  };
  return colorMap[type] || 'default';
}

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 格式化金额
function formatMoney(amount: number) {
  return amount ? `¥${amount.toLocaleString()}` : '-';
}

// 编辑项目
function handleEditProject() {
  editDrawerVisible.value = true;
}

// 查看客户
function handleViewCustomer() {
  router.push(`/customer/detail/${projectInfo.value.customerId}`);
}

// 上传附件
function handleUploadAttachment() {
  message.success('上传附件功能待实现');
}

// 下载附件
function handleDownload(record: any) {
  message.success(`下载文件: ${record.name}`);
}

// 删除附件
function handleDeleteAttachment(record: any) {
  message.success(`删除文件: ${record.name}`);
}

// 获取合同状态名称
function getContractStatusName(status: string) {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'pending': '待签署',
    'signed': '已签署',
    'executing': '执行中',
    'completed': '已完成',
    'terminated': '已终止',
  };
  return statusMap[status] || status;
}

// 获取合同状态颜色
function getContractStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    'draft': 'default',
    'pending': 'processing',
    'signed': 'success',
    'executing': 'blue',
    'completed': 'green',
    'terminated': 'red',
  };
  return colorMap[status] || 'default';
}

// 关联合同
function handleAddContract() {
  message.success('关联合同功能待实现');
}

// 查看合同
function handleViewContract(record: any) {
  message.success(`跳转到合同详情: ${record.name}`);
  // router.push(`/contracts/detail/${record.id}`);
}

// 取消关联合同
function handleUnlinkContract(record: any) {
  contractList.value = contractList.value.filter(item => item.id !== record.id);
  message.success('取消关联成功');
}

// 获取进度条颜色
function getProgressColor(progress: number) {
  if (progress < 30) return '#f5222d'; // 红色
  if (progress < 60) return '#fa8c16'; // 橙色
  if (progress < 80) return '#fadb14'; // 黄色
  return '#52c41a'; // 绿色
}

// 计算剩余天数
function getDaysRemaining() {
  if (!projectInfo.value.endDate) return '-';
  const endDate = dayjs(projectInfo.value.endDate);
  const today = dayjs();
  const diff = endDate.diff(today, 'day');
  return diff > 0 ? diff : 0;
}

// 加载项目详情数据
async function loadProjectDetail(id: string) {
  try {
    // 模拟数据
    projectInfo.value = {
      id: 'PI10001',
      code: 'P001-C250624001',
      name: '智能办公平台开发项目',
      type: 'software_development',
      status: 'active',
      customerId: 'C250624001',
      customerName: '北京科技有限公司',
      customerContact: '张经理',
      customerPhone: '13800138001',
      managerName: '张三',
      expectedProfitRate: 25,
      budget: 500000,
      progress: 65,
      startDate: '2024-01-15',
      endDate: '2024-06-30',
      createTime: '2024-01-10',
      updateTime: '2024-06-18',
      description: '为北京科技有限公司开发智能办公平台，包括OA系统、CRM系统、人事管理等模块。',
    };
    
    contractList.value = [
      {
        id: 'CT001',
        name: '智能办公平台开发合同',
        amount: 500000,
        status: 'signed',
        signDate: '2024-01-20',
      },
      {
        id: 'CT002',
        name: '系统运维服务合同',
        amount: 100000,
        status: 'executing',
        signDate: '2024-02-15',
      },
    ];
    
    attachmentList.value = [
      {
        id: '1',
        name: '项目需求文档.docx',
        type: 'document',
        size: '2.5MB',
        uploadTime: '2024-01-15 10:30:00',
      },
      {
        id: '2',
        name: '系统架构图.png',
        type: 'image',
        size: '1.2MB',
        uploadTime: '2024-01-20 14:20:00',
      },
      {
        id: '3',
        name: '项目预算表.xlsx',
        type: 'spreadsheet',
        size: '856KB',
        uploadTime: '2024-01-25 09:15:00',
      },
    ];
  } catch (error) {
    console.error('加载项目详情失败', error);
    message.error('加载项目详情失败');
  }
}

// 初始化
onMounted(() => {
  const id = route.params.id;
  if (id) {
    loadProjectDetail(id as string);
  }
});
</script>

<style scoped>
.project-detail {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
