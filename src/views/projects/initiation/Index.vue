<template>
  <div class="project-initiation-container animate-fadeIn">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">项目管理</h2>
          <p class="text-gray-500 mt-1">管理项目的生命周期</p>
        </div>
        <a-button type="primary" @click="handleCreateProject">
          <template #icon><plus-outlined /></template>
          新建项目
        </a-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <a-card class="mb-6" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="项目名称/编号">
          <a-input v-model:value="searchForm.keyword" placeholder="请输入项目名称或编号" allowClear />
        </a-form-item>
        
        <a-form-item label="销售负责人">
          <a-select
            v-model:value="searchForm.manager"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="pending">准备中/待启动</a-select-option>
            <a-select-option value="active">进行中</a-select-option>
            <a-select-option value="paused">暂停</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="cancelled">已取消/已终止</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <template #icon><reload-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 项目立项列表 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="projectList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <!-- 项目名称 -->
          <template v-if="column.dataIndex === 'name'">
            <a @click="handleViewProject(record)" class="font-medium text-blue-600 hover:text-blue-800">{{ record.name }}</a>
          </template>
          
          <!-- 客户名称 -->
          <template v-if="column.dataIndex === 'customerName'">
            <a @click="viewCustomer(record)" class="text-blue-600 hover:text-blue-800">{{ record.customerName }}</a>
          </template>
          
          <!-- 项目类型 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag>{{ getProjectTypeName(record.type) }}</a-tag>
          </template>
          
          <!-- 状态 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          
          <!-- 预计毛利率 -->
          <template v-if="column.dataIndex === 'expectedProfitRate'">
            {{ record.expectedProfitRate ? `${record.expectedProfitRate}%` : '-' }}
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="editProject(record)"
              >
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="viewRelatedContracts(record)"
              >
                查看关联合同
              </a-button>
              <a-button 
                v-if="record.status !== 'completed' && record.status !== 'cancelled'"
                type="link" 
                size="small" 
                @click="handleCompleteProject(record)"
              >
                完成
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 项目立项表单弹窗 -->
    <a-drawer
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑项目' : '新建项目'"
      width="1000px"
      placement="right"
      @close="handleModalCancel"
    >
      <div class="flex gap-6">
        <!-- 左侧：项目信息 -->
        <div class="flex-1">
          <a-divider orientation="left">项目信息</a-divider>
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="项目编号" v-if="isEdit">
              <span>{{ formState.id }}</span>
            </a-form-item>
            
            <a-form-item label="项目名称" name="name">
              <a-input v-model:value="formState.name" placeholder="请输入项目名称" />
            </a-form-item>
            
            <a-form-item label="项目起止时间" name="projectTimeRange">
              <a-range-picker 
                v-model:value="formState.projectTimeRange"
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
            
            <a-form-item label="客户" name="customerId">
              <div class="flex gap-2">
                <a-select
                  v-model:value="formState.customerId"
                  placeholder="请选择客户"
                  @change="handleCustomerChange"
                  show-search
                  :filter-option="filterOption"
                  style="flex: 1"
                >
                  <a-select-option v-for="item in customerOptions" :key="item.id" :value="item.id">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <a-button type="primary" @click="handleAddCustomer">新增客户</a-button>
              </div>
            </a-form-item>
            
            <a-form-item>
              <a-checkbox v-model:checked="isEndUserSameAsCustomer" @change="handleEndUserCheckChange">
                客户与最终用户一致
              </a-checkbox>
            </a-form-item>
            
            <a-form-item label="项目类型" name="type">
              <a-select v-model:value="formState.type" placeholder="请选择项目类型">
                <a-select-option value="system_integration">系统集成</a-select-option>
                <a-select-option value="software_development">软件开发</a-select-option>
                <a-select-option value="product_own">产品（自有）</a-select-option>
                <a-select-option value="product_external">产品（外采）</a-select-option>
                <a-select-option value="service_own">服务（自有）</a-select-option>
                <a-select-option value="service_external">服务（外采）</a-select-option>
                <a-select-option value="integration">集成</a-select-option>
                <a-select-option value="other">其他（行政、租房、人事等）</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="项目状态" name="status">
              <a-select v-model:value="formState.status" placeholder="请选择项目状态">
                <a-select-option value="pending">准备中/待启动</a-select-option>
                <a-select-option value="active">进行中</a-select-option>
                <a-select-option value="paused">暂停</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="cancelled">已取消/已终止</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="销售负责人" name="managerId">
              <a-select v-model:value="formState.managerId" placeholder="请选择销售负责人">
                <a-select-option value="1">张三</a-select-option>
                <a-select-option value="2">李四</a-select-option>
                <a-select-option value="3">王五</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="立项理由" name="reason">
              <a-select v-model:value="formState.reason" placeholder="请选择立项理由">
                <a-select-option value="bidding">投标</a-select-option>
                <a-select-option value="signed">签约</a-select-option>
                <a-select-option value="poc">概念验证（POC）</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="预计毛利率" name="expectedProfitRate">
              <a-input-number
                v-model:value="formState.expectedProfitRate"
                placeholder="请输入预计毛利率"
                style="width: 100%"
                :min="0"
                :max="100"
                :formatter="(value: number) => `${value}%`"
                :parser="(value: string) => value.replace('%', '')"
              />
            </a-form-item>
            
            <a-form-item label="项目描述" name="description">
              <a-textarea
                v-model:value="formState.description"
                placeholder="请输入项目描述"
                :rows="4"
              />
            </a-form-item>
          </a-form>
        </div>
        
        <!-- 右侧：最终用户信息 -->
        <div class="flex-1">
          <a-divider orientation="left">最终用户信息</a-divider>
          <a-form
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="最终用户名称" name="endUserName">
              <a-input v-model:value="formState.endUserName" placeholder="请输入最终用户名称" />
            </a-form-item>
            
            <a-form-item label="最终用户联系人" name="endUserContact">
              <a-input v-model:value="formState.endUserContact" placeholder="请输入最终用户联系人" />
            </a-form-item>
            
            <a-form-item label="最终用户电话" name="endUserPhone">
              <a-input v-model:value="formState.endUserPhone" placeholder="请输入最终用户电话" />
            </a-form-item>
            
            <a-form-item label="最终用户地址" name="endUserAddress">
              <a-input v-model:value="formState.endUserAddress" placeholder="请输入最终用户地址" />
            </a-form-item>
          </a-form>
                 </div>
       </div>
       
       <template #footer>
         <div class="text-right">
           <a-space>
             <a-button @click="handleModalCancel">取消</a-button>
             <a-button type="primary" @click="handleModalOk" :loading="false">
               {{ isEdit ? '更新' : '保存' }}
             </a-button>
           </a-space>
         </div>
       </template>
     </a-drawer>

    <!-- 项目详情弹框 -->
    <a-modal
      v-model:visible="detailModalVisible"
      :title="`项目详情 - ${currentProjectDetail?.name || ''}`"
      width="800px"
      :footer="null"
    >
      <a-descriptions v-if="currentProjectDetail" :column="2" bordered>
        <a-descriptions-item label="项目编号">{{ currentProjectDetail.id }}</a-descriptions-item>
        <a-descriptions-item label="项目名称">{{ currentProjectDetail.name }}</a-descriptions-item>
        <a-descriptions-item label="客户信息">{{ currentProjectDetail.customerName }}</a-descriptions-item>
        <a-descriptions-item label="客户联系人">{{ currentProjectDetail.customerContact }}</a-descriptions-item>
        <a-descriptions-item label="项目类型">
          <a-tag>{{ getProjectTypeName(currentProjectDetail.type) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="负责人">{{ currentProjectDetail.managerName }}</a-descriptions-item>
        <a-descriptions-item label="预计毛利率">{{ currentProjectDetail.expectedProfitRate }}%</a-descriptions-item>
        <a-descriptions-item label="立项日期">{{ currentProjectDetail.createTime }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(currentProjectDetail.status)">
            {{ getStatusName(currentProjectDetail.status) }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 新增客户弹框 -->
    <a-drawer
      v-model:visible="customerModalVisible"
      title="新增客户"
      width="700px"
      placement="right"
      @close="handleCustomerModalCancel"
    >
      <a-form
        :model="customerForm"
        :rules="customerRules"
        ref="customerFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 客户信息 -->
        <a-divider>客户信息</a-divider>
        
        <a-form-item label="客户名称" name="name">
          <a-input v-model:value="customerForm.name" placeholder="请输入客户公司全称" />
        </a-form-item>
        
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="customerForm.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="客户类型" name="type">
          <a-select v-model:value="customerForm.type" placeholder="请选择客户类型">
            <a-select-option value="C01">企业</a-select-option>
            <a-select-option value="C02">政府</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="客户状态" name="status">
          <a-select v-model:value="customerForm.status" placeholder="请选择客户状态">
            <a-select-option value="new">新建</a-select-option>
            <a-select-option value="following">跟进中</a-select-option>
            <a-select-option value="negotiating">商务谈判</a-select-option>
            <a-select-option value="signed">已签约</a-select-option>
            <a-select-option value="lost">已流失</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="所属行业" name="industry">
          <a-select v-model:value="customerForm.industry" placeholder="请选择所属行业">
            <a-select-option value="I01">政府机构</a-select-option>
            <a-select-option value="I02">金融服务</a-select-option>
            <a-select-option value="I03">信息技术/互联网</a-select-option>
            <a-select-option value="I04">制造与工业</a-select-option>
            <a-select-option value="I05">零售与消费品</a-select-option>
            <a-select-option value="I06">能源与公用事业</a-select-option>
            <a-select-option value="I07">交通与物流</a-select-option>
            <a-select-option value="I08">医疗与健康</a-select-option>
            <a-select-option value="I09">教育与科研</a-select-option>
            <a-select-option value="I10">房地产与建筑</a-select-option>
            <a-select-option value="I11">专业服务</a-select-option>
            <a-select-option value="I12">农林牧渔</a-select-option>
            <a-select-option value="I13">其他/未分类</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="客户来源" name="source">
          <a-select v-model:value="customerForm.source" placeholder="请选择客户来源">
            <a-select-option value="website">官网</a-select-option>
            <a-select-option value="referral">转介绍</a-select-option>
            <a-select-option value="exhibition">展会</a-select-option>
            <a-select-option value="advertisement">广告</a-select-option>
            <a-select-option value="social">社交媒体</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="客户负责人" name="owner">
          <a-select v-model:value="customerForm.owner" placeholder="请选择负责人">
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 联系信息 -->
        <a-divider>联系信息</a-divider>
        
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="customerForm.contact" placeholder="请输入主要联系人姓名" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="customerForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="电子邮箱" name="email">
          <a-input v-model:value="customerForm.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        
        <a-form-item label="联系地址" name="address">
          <a-textarea v-model:value="customerForm.address" placeholder="请输入详细地址" :rows="3" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="customerForm.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
        
        <!-- 开票信息 -->
        <a-divider>开票信息</a-divider>
        
        <a-form-item label="开票公司名称" name="invoiceCompanyName">
          <a-input v-model:value="customerForm.invoiceCompanyName" placeholder="请输入开票公司名称" />
        </a-form-item>
        
        <a-form-item label="开票纳税人识别号" name="invoiceTaxId">
          <a-input v-model:value="customerForm.invoiceTaxId" placeholder="请输入开票纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="开户银行" name="invoiceBank">
          <a-input v-model:value="customerForm.invoiceBank" placeholder="请输入开户银行" />
        </a-form-item>
        
        <a-form-item label="银行账号" name="invoiceBankAccount">
          <a-input v-model:value="customerForm.invoiceBankAccount" placeholder="请输入银行账号" />
        </a-form-item>
        
        <a-form-item label="开票地址" name="invoiceAddress">
          <a-input v-model:value="customerForm.invoiceAddress" placeholder="请输入开票地址" />
        </a-form-item>
        
        <a-form-item label="开票电话" name="invoicePhone">
          <a-input v-model:value="customerForm.invoicePhone" placeholder="请输入开票电话" />
        </a-form-item>
        
        <a-form-item label="开票备注" name="invoiceRemark">
          <a-textarea v-model:value="customerForm.invoiceRemark" placeholder="请输入开票备注信息" :rows="2" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="text-right">
          <a-space>
            <a-button @click="handleCustomerModalCancel">取消</a-button>
            <a-button type="primary" @click="handleCustomerSubmit" :loading="customerSubmitting">
              保存
            </a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>

    <!-- 附件管理弹框 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      :title="`附件管理 - ${currentProject?.name || ''}`"
      width="1000px"
      :footer="null"
    >
      <div class="mb-4">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-select
              v-model:value="selectedAttachmentCategory"
              placeholder="请选择附件类别"
              style="width: 100%"
              @change="handleCategoryChange"
            >
              <a-select-option v-for="category in attachmentCategories" :key="category.value" :value="category.value">
                {{ category.label }}
              </a-select-option>
              <a-select-option value="__add_new__" class="add-category-option">
                <plus-outlined /> 新增类别
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="16">
            <a-upload
              :file-list="[]"
              :before-upload="beforeUploadAttachment"
              @change="handleAttachmentChange"
              :multiple="true"
              :disabled="!selectedAttachmentCategory || selectedAttachmentCategory === '__add_new__'"
            >
              <a-button type="primary" :disabled="!selectedAttachmentCategory || selectedAttachmentCategory === '__add_new__'">
                <template #icon><upload-outlined /></template>
                上传附件
              </a-button>
            </a-upload>
          </a-col>
        </a-row>
      </div>
      
      <a-table
        :columns="attachmentColumns"
        :data-source="attachmentList"
        :pagination="false"
        row-key="uid"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <!-- 文件名称 -->
          <template v-if="column.dataIndex === 'name'">
            <a @click="() => viewAttachment(record)">{{ record.name }}</a>
          </template>
          
          <!-- 附件类别 -->
          <template v-if="column.dataIndex === 'category'">
            <a-tag :color="getAttachmentCategoryColor(record.category)">
              {{ getAttachmentCategoryName(record.category) }}
            </a-tag>
          </template>
          
          <!-- 附件类型 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag :color="getAttachmentTypeColor(record.type)">
              {{ getAttachmentTypeName(record.type) }}
            </a-tag>
          </template>
          
          <!-- 文件大小 -->
          <template v-if="column.dataIndex === 'size'">
            {{ formatFileSize(record.size) }}
          </template>
          
          <!-- 上传时间 -->
          <template v-if="column.dataIndex === 'uploadTime'">
            {{ formatDate(record.uploadTime) }}
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="() => viewAttachment(record)">查看</a-button>
              <a-button type="link" size="small" danger @click="() => deleteAttachment(record)">删除</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 新增附件类别弹框 -->
    <a-modal
      v-model:visible="addCategoryModalVisible"
      title="新增附件类别"
      @ok="handleAddCategory"
      @cancel="handleCancelAddCategory"
      width="400px"
    >
      <a-form
        ref="categoryFormRef"
        :model="newCategoryForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="类别名称"
          name="name"
          :rules="[{ required: true, message: '请输入类别名称' }]"
        >
          <a-input v-model:value="newCategoryForm.name" placeholder="请输入类别名称" />
        </a-form-item>
        <a-form-item label="类别描述" name="description">
          <a-textarea v-model:value="newCategoryForm.description" placeholder="请输入类别描述（可选）" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 项目完成弹框 -->
    <a-modal
      v-model:visible="completeModalVisible"
      title="项目完成"
      width="800px"
      :confirm-loading="completeUploading"
      @ok="handleSubmitComplete"
      @cancel="handleCancelComplete"
    >
      <div class="mb-4">
        <a-alert
          message="项目完成要求"
          description="完成项目时需要上传试算表文件（必传，可多个），其他相关文件为可选。提交后项目状态将变更为已完成。"
          type="info"
          show-icon
          class="mb-4"
        />
      </div>

      <a-form
        ref="completeFormRef"
        :model="completeForm"
        :rules="completeRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="项目信息">
          <div class="bg-gray-50 p-3 rounded">
            <div><strong>项目名称：</strong>{{ currentCompletingProject?.name }}</div>
            <div><strong>项目编号：</strong>{{ currentCompletingProject?.code }}</div>
            <div><strong>客户名称：</strong>{{ currentCompletingProject?.customerName }}</div>
          </div>
        </a-form-item>

        <a-form-item label="试算表" name="spreadsheets" required>
          <div class="space-y-2">
            <a-upload
              :file-list="completeForm.spreadsheets"
              :before-upload="beforeUploadSpreadsheet"
              @change="handleSpreadsheetChange"
              :multiple="true"
              accept=".xls,.xlsx,.csv"
            >
              <a-button type="primary">
                <template #icon><upload-outlined /></template>
                上传试算表（必传）
              </a-button>
            </a-upload>
            <div class="text-sm text-gray-500">
              支持格式：XLS、XLSX、CSV，单个文件不超过5MB，可上传多个文件
            </div>
          </div>
        </a-form-item>

        <a-form-item label="其他附件">
          <div class="space-y-2">
            <a-upload
              :file-list="completeForm.others"
              :before-upload="beforeUploadOther"
              @change="handleOtherChange"
              :multiple="true"
            >
              <a-button>
                <template #icon><upload-outlined /></template>
                上传其他文件（可选）
              </a-button>
            </a-upload>
            <div class="text-sm text-gray-500">
              支持格式：PDF、DOC、DOCX、JPG、PNG等，单个文件不超过10MB
            </div>
          </div>
        </a-form-item>

        <a-form-item label="完成备注">
          <a-textarea
            v-model:value="completeForm.remark"
            placeholder="请输入项目完成的相关说明（可选）"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';

// 路由
const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '项目编号',
    dataIndex: 'code',
    key: 'code',
    width: 150,
  },
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 200,
  },
  {
    title: '项目类型',
    dataIndex: 'type',
    key: 'type',
    width: 150,
  },
  {
    title: '销售负责人',
    dataIndex: 'managerName',
    key: 'managerName',
    width: 120,
  },
  {
    title: '预计毛利率',
    dataIndex: 'expectedProfitRate',
    key: 'expectedProfitRate',
    width: 120,
  },
  {
    title: '立项日期',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
];

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  manager: undefined,
  status: undefined,
});

// 是否为审批人
const isApprover = ref(true); // 模拟数据，实际应从权限系统获取

// 项目列表数据
const projectList = ref<any[]>([]);

// 客户选项
const customerOptions = ref([
  { id: 'C250624001', name: '北京科技有限公司', contact: '张经理', phone: '13800138001', address: '北京市朝阳区科技园' },
  { id: 'C250624002', name: '上海数字科技有限公司', contact: '李总', phone: '13800138002', address: '上海市浦东新区张江高科技园区' },
  { id: 'C250624003', name: '广州智能科技有限公司', contact: '王董', phone: '13800138003', address: '广州市天河区软件园' },
  { id: 'G250624001', name: '深圳市政府信息中心', contact: '赵主任', phone: '13800138004', address: '深圳市南山区政府大楼' },
  { id: 'C250624004', name: '杭州网络科技有限公司', contact: '钱经理', phone: '13800138005', address: '杭州市西湖区文三路' },
]);

// 销售机会选项
const opportunityOptions = ref<any[]>([]);

// 弹窗相关
const modalVisible = ref(false);
const detailModalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();
const currentProjectDetail = ref<any>(null);

// 客户相关信息
const customerContact = ref('');
const customerPhone = ref('');
const customerAddress = ref('');

// 是否最终用户与客户相同
const isEndUserSameAsCustomer = ref(false);

// 新增客户弹框
const customerModalVisible = ref(false);
const customerFormRef = ref();
const customerSubmitting = ref(false);

// 客户表单数据（复用客户管理的表单结构）
const customerForm = reactive({
  id: '',
  name: '',
  taxId: '',
  type: 'C01',
  status: 'new',
  industry: 'I01',
  source: 'website',
  owner: '1',
  contact: '',
  phone: '',
  email: '',
  address: '',
  remark: '',
  // 开票信息
  invoiceCompanyName: '',
  invoiceTaxId: '',
  invoiceBank: '',
  invoiceBankAccount: '',
  invoiceAddress: '',
  invoicePhone: '',
  invoiceRemark: '',
});

// 客户表单验证规则
const customerRules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择客户状态', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
};

// 表单数据
const formState = reactive({
  id: '',
  customerId: undefined,
  opportunityId: undefined,
  type: undefined,
  managerId: undefined,
  name: '',
  endUserName: '',
  endUserContact: '',
  endUserPhone: '',
  endUserAddress: '',
  projectTimeRange: [] as any[],
  reason: undefined,
  expectedProfitRate: 0,
  description: '',
  status: 'pending',
});

// 表单验证规则
const rules = {
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  managerId: [{ required: true, message: '请选择销售负责人', trigger: 'change' }],
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  endUserName: [{ required: true, message: '请输入最终用户名称', trigger: 'blur' }],
  projectTimeRange: [{ required: true, message: '请选择项目起止时间', trigger: 'change' }],
  reason: [{ required: true, message: '请选择立项理由', trigger: 'change' }],
  expectedProfitRate: [{ required: true, message: '请输入预计毛利率', trigger: 'change' }],
};

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = '';
  searchForm.manager = undefined;
  searchForm.status = undefined;
  handleSearch();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchProjectList();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchProjectList();
};

// 获取项目列表
const fetchProjectList = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    try {
      // 模拟数据
      const mockData = Array.from({ length: 30 }, (_, index) => {
        const customerInfo = customerOptions.value[index % customerOptions.value.length];
        
        return {
          id: `PI${String(10000 + index).padStart(5, '0')}`,
          code: `P${String(index + 1).padStart(3, '0')}-${customerInfo.id}`,
          name: `${['新零售系统', '医疗平台', '云计算平台', '智能家居', 'AI助手'][index % 5]}项目${index + 1}`,
          customerId: customerInfo.id,
          customerName: customerInfo.name,
          customerContact: customerInfo.contact,
          type: ['software_sale', 'integration_sale', 'software_dev_own', 'hardware_sale', 'system_maintenance'][index % 5],
          managerId: String(index % 3 + 1),
          managerName: ['张三', '李四', '王五'][index % 3],
          expectedProfitRate: Math.floor(Math.random() * 30) + 10, // 10-40%的毛利率
          budget: Math.floor(Math.random() * 1000000) + 50000,
          status: ['active', 'active', 'completed', 'suspended', 'cancelled'][Math.floor(Math.random() * 5)],
          createTime: dayjs().subtract(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD'),
          updateTime: dayjs().subtract(Math.floor(Math.random() * 10), 'day').format('YYYY-MM-DD'),
        };
      });
      
      // 根据搜索条件过滤
      let filteredData = [...mockData];
      
      if (searchForm.keyword) {
        filteredData = filteredData.filter(item => 
          item.name.includes(searchForm.keyword) || 
          item.code.includes(searchForm.keyword) ||
          item.id.includes(searchForm.keyword)
        );
      }
      
      if (searchForm.manager) {
        filteredData = filteredData.filter(item => item.managerId === searchForm.manager);
      }
      
      if (searchForm.status) {
        filteredData = filteredData.filter(item => item.status === searchForm.status);
      }
      
      // 应用分页
      const start = (pagination.current - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      
      // 更新列表数据和分页信息
      projectList.value = filteredData.slice(start, end);
      pagination.total = filteredData.length;
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
      projectList.value = [];
    } finally {
      loading.value = false;
    }
  }, 500);
};

// 客户变更
const handleCustomerChange = (value: string) => {
  // 根据客户ID获取客户信息
  const customer = customerOptions.value.find(item => item.id === value);
  if (customer) {
    customerContact.value = customer.contact;
    customerPhone.value = customer.phone;
    customerAddress.value = customer.address;
    
    // 获取该客户的销售机会
    fetchOpportunities(value);
  } else {
    customerContact.value = '';
    customerPhone.value = '';
    customerAddress.value = '';
    opportunityOptions.value = [];
  }
  
  // 清空已选择的销售机会
  formState.opportunityId = undefined;
};

// 获取销售机会
const fetchOpportunities = (customerId: string) => {
  // 模拟获取销售机会数据
  setTimeout(() => {
    opportunityOptions.value = Array.from({ length: 3 }, (_, index) => ({
      id: `O${String(10000 + index).padStart(5, '0')}`,
      name: `${['ERP升级', 'CRM实施', '数据中心建设'][index]}机会`,
    }));
  }, 300);
};

// 获取项目类型名称
const getProjectTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'software_sale': '软件产品销售',
    'hardware_sale': '硬件产品销售',
    'integration_sale': '系统集成销售',
    'own_software_sale': '自有软件销售',
    'own_hardware_sale': '自有硬件销售',
    'software_dev_own': '软件开发服务（自有）',
    'software_dev_outsource': '软件开发服务（外包）',
    'system_maintenance': '系统运维服务',
    'other_tech_service': '其他技术服务',
  };
  return typeMap[type] || type;
};

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': '进行中',
    'completed': '已完成',
    'suspended': '已暂停',
    'cancelled': '已取消',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'active': 'blue',
    'completed': 'green',
    'suspended': 'orange',
    'cancelled': 'red',
  };
  return colorMap[status] || 'default';
};

// 重置表单
const resetForm = () => {
  // 重置表单
  Object.keys(formState).forEach(key => {
    const k = key as keyof typeof formState;
    if (k === 'expectedProfitRate') {
      formState[k] = 0;
    } else if (k === 'projectTimeRange') {
      formState[k] = [];
    } else {
      // @ts-ignore - Safely ignore this type error as we've verified the property keys
      formState[k] = '';
    }
  });
  
  customerContact.value = '';
  customerPhone.value = '';
  customerAddress.value = '';
  opportunityOptions.value = [];
};

// 创建项目
const handleCreateProject = () => {
  isEdit.value = false;
  
  resetForm();
  
  modalVisible.value = true;
};

// 查看项目
const viewProject = (record: any) => {
  currentProjectDetail.value = record;
  detailModalVisible.value = true;
};

// 查看客户详情
const viewCustomer = (record: any) => {
  router.push(`/customer/detail/${record.customerId}`);
};

// 跳转到合同管理
const viewRelatedContracts = (record: any) => {
  router.push({
    path: '/contracts',
    query: { projectId: record.id }
  });
};

// 编辑项目
const editProject = (record: any) => {
  isEdit.value = true;
  
  // 填充表单数据
  formState.id = record.id;
  formState.customerId = record.customerId;
  formState.name = record.name;
  formState.type = record.type;
  formState.managerId = record.managerId;
  formState.expectedProfitRate = record.expectedProfitRate;
  
  // 获取客户信息
  handleCustomerChange(record.customerId);
  
  modalVisible.value = true;
};

// 弹窗确认
const handleModalOk = () => {
  formRef.value.validate().then(() => {
    // 模拟保存
    if (isEdit.value) {
      message.success('项目立项已更新');
    } else {
      message.success('项目立项已创建');
      
      // 模拟生成项目编号
      const newId = `P${String(10000 + projectList.value.length).padStart(5, '0')}`;
      projectList.value.unshift({
        id: newId,
        name: formState.name,
        customerId: formState.customerId,
        customerName: customerOptions.value.find(c => c.id === formState.customerId)?.name,
        customerContact: customerContact.value,
        type: formState.type,
        managerId: formState.managerId,
        managerName: ['张三', '李四', '王五'][Number(formState.managerId) - 1],
        expectedProfitRate: formState.expectedProfitRate || Math.floor(Math.random() * 30) + 10,
        createTime: dayjs().format('YYYY-MM-DD'),
        status: 'active',
      });
    }
    
    modalVisible.value = false;
  }).catch((error: any) => {
    console.log('验证失败', error);
  });
};

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 处理最终用户复选框变化
const handleEndUserCheckChange = (checked: boolean) => {
  if (checked && formState.customerId) {
    // 如果勾选且已选择客户，则将客户信息填充到最终用户信息
    const customer = customerOptions.value.find(item => item.id === formState.customerId);
    if (customer) {
      formState.endUserName = customer.name;
      formState.endUserContact = customer.contact;
      formState.endUserPhone = customer.phone;
      formState.endUserAddress = customer.address;
    }
  } else if (!checked) {
    // 如果取消勾选，则清空最终用户信息
    formState.endUserName = '';
    formState.endUserContact = '';
    formState.endUserPhone = '';
    formState.endUserAddress = '';
  }
};

// 处理新增客户
const handleAddCustomer = () => {
  resetCustomerForm();
  customerModalVisible.value = true;
};

// 重置客户表单
const resetCustomerForm = () => {
  customerForm.id = '';
  customerForm.name = '';
  customerForm.taxId = '';
  customerForm.type = 'potential';
  customerForm.status = 'new';
  customerForm.industry = 'other';
  customerForm.source = 'website';
  customerForm.owner = '1';
  customerForm.contact = '';
  customerForm.phone = '';
  customerForm.email = '';
  customerForm.address = '';
  customerForm.remark = '';
  // 重置开票信息
  customerForm.invoiceCompanyName = '';
  customerForm.invoiceTaxId = '';
  customerForm.invoiceBank = '';
  customerForm.invoiceBankAccount = '';
  customerForm.invoiceAddress = '';
  customerForm.invoicePhone = '';
  customerForm.invoiceRemark = '';
};

// 客户弹框取消
const handleCustomerModalCancel = () => {
  customerModalVisible.value = false;
  resetCustomerForm();
};

// 客户表单提交
const handleCustomerSubmit = async () => {
  try {
    await customerFormRef.value.validate();
    customerSubmitting.value = true;
    
    // 模拟创建客户
    const newCustomer = {
      id: `C${String(100000 + customerOptions.value.length + 1).padStart(6, '0')}`,
      name: customerForm.name,
      contact: customerForm.contact,
      phone: customerForm.phone,
      address: customerForm.address,
    };
    
    // 添加到客户选项中
    customerOptions.value.push(newCustomer);
    
    // 自动选择新创建的客户
    formState.customerId = newCustomer.id as any;
    handleCustomerChange(newCustomer.id);
    
    message.success('客户创建成功');
    customerModalVisible.value = false;
  } catch (error) {
    console.error('保存客户信息失败', error);
  } finally {
    customerSubmitting.value = false;
  }
};

// select筛选
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 附件管理相关变量
const attachmentModalVisible = ref(false);
const attachmentList = ref<any[]>([]);
const selectedAttachmentCategory = ref('');
const addCategoryModalVisible = ref(false);
const categoryFormRef = ref(null);
const currentProject = ref<any>(null);

// 附件类别管理
const attachmentCategories = ref([
  { value: 'design', label: '设计文档' },
  { value: 'requirement', label: '需求文档' },
  { value: 'code', label: '代码文件' },
  { value: 'test', label: '测试文档' },
  { value: 'deployment', label: '部署文档' },
  { value: 'other', label: '其他附件' },
]);

// 新增类别表单
const newCategoryForm = ref({
  name: '',
  description: '',
});

const attachmentColumns = [
  {
    title: '文件名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '附件类别',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '附件类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    key: 'size',
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    key: 'uploadTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
];

// 管理附件功能已移至项目详情页面

// 获取附件类别名称
const getAttachmentCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    design: '设计文档',
    requirement: '需求文档',
    code: '代码文件',
    test: '测试文档',
    deployment: '部署文档',
    other: '其他附件',
  };
  return categoryMap[category] || category;
};

// 获取附件类别颜色
const getAttachmentCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    design: 'blue',
    requirement: 'green',
    code: 'purple',
    test: 'orange',
    deployment: 'cyan',
    other: 'default',
  };
  return colorMap[category] || 'default';
};

// 获取附件类型名称
const getAttachmentTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    pdf: 'PDF',
    doc: 'Word',
    docx: 'Word',
    xls: 'Excel',
    xlsx: 'Excel',
    ppt: 'PPT',
    pptx: 'PPT',
    txt: '文本',
    jpg: '图片',
    jpeg: '图片',
    png: '图片',
    gif: '图片',
    zip: '压缩包',
    rar: '压缩包',
  };
  return typeMap[type] || type;
};

// 获取附件类型颜色
const getAttachmentTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    pdf: 'blue',
    doc: 'green',
    docx: 'green',
    xls: 'orange',
    xlsx: 'orange',
    ppt: 'purple',
    pptx: 'purple',
    txt: 'default',
    jpg: 'red',
    jpeg: 'red',
    png: 'red',
    gif: 'red',
    zip: 'volcano',
    rar: 'volcano',
  };
  return colorMap[type] || 'default';
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 格式化日期
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
};

// 上传附件
const beforeUploadAttachment = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword', 
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/jpg',
    'text/plain',
    'application/zip',
    'application/x-rar-compressed'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持上传 PDF、DOC、DOCX、XLS、XLSX、JPG、PNG、TXT、ZIP、RAR 格式的文件！');
    return false;
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB！');
    return false;
  }
  
  return true;
};

// 处理附件变化
const handleAttachmentChange = (info: any) => {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 附件上传成功`);
    // 添加到附件列表
    attachmentList.value.push({
      uid: Date.now().toString(),
      name: info.file.name,
      category: selectedAttachmentCategory.value,
      type: info.file.name.split('.').pop()?.toLowerCase() || 'unknown',
      size: info.file.size,
      uploadTime: new Date().toISOString(),
      url: info.file.response?.url || '#'
    });
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 附件上传失败`);
  }
};

// 查看附件
const viewAttachment = (record: any) => {
  window.open(record.url, '_blank');
};

// 删除附件
const deleteAttachment = (record: any) => {
  attachmentList.value = attachmentList.value.filter(item => item.uid !== record.uid);
  message.success('附件删除成功');
};

// 处理附件类别变化
const handleCategoryChange = (value: string) => {
  if (value === '__add_new__') {
    addCategoryModalVisible.value = true;
    selectedAttachmentCategory.value = '';
  }
};

// 添加新类别
const handleAddCategory = () => {
  if (!newCategoryForm.value.name.trim()) {
    message.error('请输入类别名称');
    return;
  }
  
  const categoryValue = newCategoryForm.value.name.toLowerCase().replace(/\s+/g, '_');
  const newCategory = {
    value: categoryValue,
    label: newCategoryForm.value.name
  };
  
  attachmentCategories.value.push(newCategory);
  selectedAttachmentCategory.value = categoryValue;
  addCategoryModalVisible.value = false;
  
  // 重置表单
  newCategoryForm.value = {
    name: '',
    description: '',
  };
  
  message.success('类别添加成功');
};

// 取消添加类别
const handleCancelAddCategory = () => {
  addCategoryModalVisible.value = false;
  newCategoryForm.value = {
    name: '',
    description: '',
  };
};

// 项目完成相关
const completeModalVisible = ref(false);
const completeFormRef = ref();
const currentCompletingProject = ref<any>(null);
const completeUploading = ref(false);

// 完成项目表单
const completeForm = reactive({
  spreadsheets: [] as any[],  // 试算表附件（必传）
  others: [] as any[],        // 其他附件（可选）
  remark: '',                 // 备注
});

// 完成项目表单验证规则
const completeRules = {
  spreadsheets: [
    { required: true, message: '请至少上传一个试算表文件', trigger: 'change', validator: validateSpreadsheets }
  ],
};

// 验证试算表文件
function validateSpreadsheets(rule: any, value: any[], callback: any) {
  if (!value || value.length === 0) {
    callback(new Error('请至少上传一个试算表文件'));
  } else {
    callback();
  }
}

// 查看项目详情
function handleViewProject(record: any) {
  router.push(`/projects/detail/${record.id}`);
}

// 处理项目完成
function handleCompleteProject(record: any) {
  currentCompletingProject.value = record;
  completeModalVisible.value = true;
  
  // 重置表单
  completeForm.spreadsheets = [];
  completeForm.others = [];
  completeForm.remark = '';
}

// 试算表文件上传前验证
function beforeUploadSpreadsheet(file: File) {
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    message.error('试算表只支持上传 XLS、XLSX、CSV 格式的文件！');
    return false;
  }
  
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过 5MB！');
    return false;
  }
  
  return true;
}

// 其他文件上传前验证
function beforeUploadOther(file: File) {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB！');
    return false;
  }
  
  return true;
}

// 处理试算表文件上传
function handleSpreadsheetChange(info: any) {
  if (info.file.status === 'uploading') {
    completeUploading.value = true;
    return;
  }
  
  if (info.file.status === 'done') {
    completeUploading.value = false;
    message.success(`${info.file.name} 上传成功`);
    
    // 添加到试算表列表
    completeForm.spreadsheets.push({
      uid: info.file.uid,
      name: info.file.name,
      status: 'done',
      url: info.file.response?.url || '#',
      size: info.file.size,
    });
  } else if (info.file.status === 'error') {
    completeUploading.value = false;
    message.error(`${info.file.name} 上传失败`);
  }
}

// 处理其他文件上传
function handleOtherChange(info: any) {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`);
    
    // 添加到其他文件列表
    completeForm.others.push({
      uid: info.file.uid,
      name: info.file.name,
      status: 'done',
      url: info.file.response?.url || '#',
      size: info.file.size,
    });
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`);
  }
}

// 提交项目完成
async function handleSubmitComplete() {
  try {
    await completeFormRef.value.validate();
    
    // 模拟提交API请求
    setTimeout(() => {
      // 更新项目状态为已完成
      const project = projectList.value.find(p => p.id === currentCompletingProject.value.id);
      if (project) {
        project.status = 'completed';
      }
      
      completeModalVisible.value = false;
      message.success('项目标记为完成成功！');
    }, 1000);
    
  } catch (error) {
    console.error('项目完成提交失败:', error);
  }
}

// 取消项目完成
function handleCancelComplete() {
  completeModalVisible.value = false;
  completeForm.spreadsheets = [];
  completeForm.others = [];
  completeForm.remark = '';
  currentCompletingProject.value = null;
}

// 初始化
onMounted(() => {
  fetchProjectList();
});
</script>

<style scoped>
.project-initiation-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 