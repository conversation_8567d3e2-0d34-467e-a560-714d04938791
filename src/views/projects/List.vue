<template>
  <div class="project-list-container animate-fadeIn">
    <!-- 页面标题区域 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">项目管理</h2>
          <p class="text-gray-500 mt-1">管理项目的生命周期</p>
        </div>
        <div class="flex space-x-3">
          <a-button type="primary" @click="handleCreateProject">
            <template #icon><plus-outlined /></template>
            新建项目
          </a-button>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            导出
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6" :bordered="false">
      <div class="flex justify-between items-start flex-wrap">
        <div class="search-area w-full lg:w-auto flex-1">
          <a-input-search
            v-model:value="searchForm.keyword"
            placeholder="搜索项目名称或项目编号"
            enter-button
            @search="handleSearch"
            class="search-input mb-4 max-w-lg"
            allow-clear
          />
          
          <a-form 
            layout="inline" 
            :model="searchForm" 
            :class="['advanced-search', showAdvanced ? 'expanded' : '']" 
            ref="searchFormRef"
          >
            <div class="flex flex-wrap gap-3">
              <a-form-item label="项目类型" class="mb-2">
                <a-select v-model:value="searchForm.type" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option value="software">软件开发</a-select-option>
                  <a-select-option value="integration">系统集成</a-select-option>
                  <a-select-option value="implementation">实施交付</a-select-option>
                  <a-select-option value="service">技术服务</a-select-option>
                  <a-select-option value="research">研发项目</a-select-option>
                  <a-select-option value="maintenance">运维服务</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="项目状态" class="mb-2">
                <a-select v-model:value="searchForm.status" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option value="pending">准备中</a-select-option>
                  <a-select-option value="progress">进行中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="suspended">暂停</a-select-option>
                  <a-select-option value="cancelled">已取消</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="项目负责人" class="mb-2">
                <a-select v-model:value="searchForm.manager" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option value="1">张三</a-select-option>
                  <a-select-option value="2">李四</a-select-option>
                  <a-select-option value="3">王五</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="客户名称" class="mb-2">
                <a-select 
                  v-model:value="searchForm.customer" 
                  style="width: 180px" 
                  placeholder="请选择"
                  showSearch
                  :filter-option="filterOption"
                  allowClear
                >
                  <a-select-option v-for="item in customerOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="创建日期" class="mb-2">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 240px"
                  format="YYYY-MM-DD"
                  :placeholder="['开始日期', '结束日期']"
                  allowClear />
              </a-form-item>
            </div>
            
            <div class="flex justify-end w-full mt-3">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
                <a-button type="link" @click="toggleAdvancedSearch">
                  {{ showAdvanced ? '收起筛选' : '展开筛选' }}
                  <template #icon>
                    <up-outlined v-if="showAdvanced" />
                    <down-outlined v-else />
                  </template>
                </a-button>
              </a-space>
            </div>
          </a-form>
        </div>
      </div>
    </a-card>
    
    <!-- 项目列表区域 -->
    <a-card :bordered="false">
      <div class="flex justify-end mb-4">
        <a-space>
          <a-button @click="showColumnConfig">
            <template #icon><setting-outlined /></template>
            字段配置
          </a-button>
          <a-button @click="fetchProjectList">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
      <a-table
        :columns="visibleColumns"
        :data-source="projectList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 自定义列内容 -->
        <template #bodyCell="{ column, record }">
          <!-- 项目名称列 -->
          <template v-if="column.dataIndex === 'name'">
            <div class="flex items-center">
              <a-avatar :size="40" :style="{ backgroundColor: getProjectTypeColor(record.type) }">
                {{ getProjectTypeIcon(record.type) }}
              </a-avatar>
              <div class="ml-2">
                <a @click="() => viewProject(record)" class="font-medium">{{ record.name }}</a>
                <div class="text-xs text-gray-500">编号: {{ record.code }}</div>
              </div>
            </div>
          </template>
          
          <!-- 客户名称列 -->
          <template v-if="column.dataIndex === 'customerName'">
            <a @click="() => viewCustomer(record)" class="text-blue-600 hover:text-blue-800">{{ record.customerName }}</a>
          </template>
          
          <!-- 项目类型列 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag :color="getProjectTypeColor(record.type)">
              {{ getProjectTypeName(record.type) }}
            </a-tag>
          </template>
          
          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          
          <!-- 进度列 -->
          <template v-if="column.dataIndex === 'progress'">
            <a-progress 
              :percent="record.progress" 
              :status="getProgressStatus(record)" 
              size="small" 
            />
          </template>
          
          <!-- 负责人列 -->
          <template v-if="column.dataIndex === 'managerName'">
            <div class="flex items-center">
              <a-avatar :size="24" class="mr-1">
                <template #icon><user-outlined /></template>
              </a-avatar>
              {{ record.managerName }}
            </div>
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="() => viewProject(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="() => editProject(record)">
                编辑
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="() => deleteProject(record)">
                      <delete-outlined /> 删除项目
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 字段配置弹框 -->
    <a-modal
      v-model:visible="columnConfigVisible"
      title="字段配置"
      @ok="handleColumnConfigSave"
      @cancel="handleColumnConfigCancel"
      width="600px"
    >
      <div class="column-config-content">
        <div class="mb-4">
          <a-alert
            message="拖拽调整字段顺序，勾选控制字段显示"
            type="info"
            show-icon
            banner
          />
        </div>
        
        <div class="column-list">
          <draggable
            v-model="configColumns"
            item-key="key"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <template #item="{ element }">
              <div class="column-item">
                <div class="column-item-content">
                  <div class="drag-handle">
                    <drag-outlined />
                  </div>
                  <a-checkbox 
                    v-model:checked="element.visible"
                    :disabled="element.key === 'action'"
                  >
                    {{ element.title }}
                  </a-checkbox>
                  <div class="column-width">
                    <span class="width-label">宽度:</span>
                    <a-input-number
                      v-model:value="element.width"
                      :min="80"
                      :max="500"
                      size="small"
                      style="width: 80px"
                    />
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
        
        <div class="mt-4">
          <a-space>
            <a-button @click="resetColumnConfig">重置默认</a-button>
            <a-button @click="saveColumnTemplate">保存模板</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import draggable from 'vuedraggable';
import {
  PlusOutlined,
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  UpOutlined,
  DownOutlined,
  UserOutlined,
  DeleteOutlined,
  SettingOutlined,
  DragOutlined,
} from '@ant-design/icons-vue';
import { useProjectStore } from '@/stores/project';
import dayjs from 'dayjs';

const router = useRouter();
const projectStore = useProjectStore();

// 响应式数据
const loading = ref(false);
const showAdvanced = ref(false);
const searchFormRef = ref();

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: undefined,
  status: undefined,
  manager: undefined,
  customer: undefined,
  dateRange: undefined,
});

// 项目列表
const projectList = ref<any[]>([]);

// 字段配置相关状态
const columnConfigVisible = ref(false);
const configColumns = ref([]);

// 初始化配置列
const initConfigColumns = () => {
  configColumns.value = columns.map(col => ({
    ...col,
    visible: true
  }));
};

// 可见列计算属性
const visibleColumns = computed(() => {
  return configColumns.value.filter(col => col.visible);
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 客户选项
const customerOptions = ref([
  { label: '北京未来科技有限公司', value: '1' },
  { label: '上海数创信息技术有限公司', value: '2' },
  { label: '广州智能科技有限公司', value: '3' },
  { label: '深圳创新科技有限公司', value: '4' },
]);

// 表格列配置
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 250,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 200,
  },
  {
    title: '项目类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 120,
  },
  {
    title: '负责人',
    dataIndex: 'managerName',
    key: 'managerName',
    width: 120,
  },
  {
    title: '开始时间',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 120,
  },
  {
    title: '预计结束',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 180,
    fixed: 'right',
  },
];

// 获取项目类型颜色
function getProjectTypeColor(type: string) {
  const colorMap: Record<string, string> = {
    software: '#1890ff',
    integration: '#52c41a',
    implementation: '#fa8c16',
    service: '#722ed1',
    research: '#eb2f96',
    maintenance: '#13c2c2',
  };
  return colorMap[type] || '#1890ff';
}

// 获取项目类型图标
function getProjectTypeIcon(type: string) {
  const iconMap: Record<string, string> = {
    software: '软',
    integration: '集',
    implementation: '实',
    service: '服',
    research: '研',
    maintenance: '维',
  };
  return iconMap[type] || '项';
}

// 获取项目类型名称
function getProjectTypeName(type: string) {
  const nameMap: Record<string, string> = {
    software: '软件开发',
    integration: '系统集成',
    implementation: '实施交付',
    service: '技术服务',
    research: '研发项目',
    maintenance: '运维服务',
  };
  return nameMap[type] || type;
}

// 获取状态颜色
function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    pending: 'default',
    progress: 'processing',
    completed: 'success',
    suspended: 'warning',
    cancelled: 'error',
  };
  return colorMap[status] || 'default';
}

// 获取状态名称
function getStatusName(status: string) {
  const nameMap: Record<string, string> = {
    pending: '准备中',
    progress: '进行中',
    completed: '已完成',
    suspended: '暂停',
    cancelled: '已取消',
  };
  return nameMap[status] || status;
}

// 获取进度状态
function getProgressStatus(record: any) {
  if (record.status === 'completed') return 'success';
  if (record.status === 'suspended' || record.status === 'cancelled') return 'exception';
  return 'active';
}

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 过滤选项
function filterOption(input: string, option: any) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

// 切换高级搜索
function toggleAdvancedSearch() {
  showAdvanced.value = !showAdvanced.value;
}

// 搜索
function handleSearch() {
  console.log('搜索条件:', searchForm);
  loadProjectList();
}

// 重置搜索
function resetSearch() {
  Object.assign(searchForm, {
    keyword: '',
    type: undefined,
    status: undefined,
    manager: undefined,
    customer: undefined,
    dateRange: undefined,
  });
  loadProjectList();
}

// 表格变化处理
function handleTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadProjectList();
}

// 新建项目
function handleCreateProject() {
  router.push('/projects/new');
}

// 查看项目
function viewProject(record: any) {
  router.push(`/projects/detail/${record.id}`);
}

// 查看客户
function viewCustomer(record: any) {
  router.push(`/customer/detail/${record.customerId}`);
}

// 编辑项目
function editProject(record: any) {
  router.push(`/projects/edit/${record.id}`);
}

// 删除项目
function deleteProject(record: any) {
  message.success('删除功能待实现');
}

// 导出
function handleExport() {
  message.success('导出功能待实现');
}

// 字段配置相关方法
const showColumnConfig = () => {
  columnConfigVisible.value = true;
};

const handleColumnConfigSave = () => {
  // 保存配置到本地存储
  localStorage.setItem('project-columns-config', JSON.stringify(configColumns.value));
  message.success('字段配置已保存');
  columnConfigVisible.value = false;
};

const handleColumnConfigCancel = () => {
  // 重新加载配置
  loadColumnConfig();
  columnConfigVisible.value = false;
};

const resetColumnConfig = () => {
  initConfigColumns();
  message.success('已重置为默认配置');
};

const saveColumnTemplate = () => {
  // 保存为模板
  localStorage.setItem('project-columns-template', JSON.stringify(configColumns.value));
  message.success('已保存为模板');
};

const loadColumnConfig = () => {
  const saved = localStorage.getItem('project-columns-config');
  if (saved) {
    try {
      const savedConfig = JSON.parse(saved);
      configColumns.value = columns.map(col => {
        const savedCol = savedConfig.find(saved => saved.key === col.key);
        return {
          ...col,
          visible: savedCol ? savedCol.visible : true,
          width: savedCol ? savedCol.width : col.width
        };
      });
    } catch (error) {
      console.error('加载字段配置失败:', error);
      initConfigColumns();
    }
  } else {
    initConfigColumns();
  }
};

const onDragStart = () => {
  // 拖拽开始
};

const onDragEnd = () => {
  // 拖拽结束，可以在这里保存顺序
};

// 刷新项目列表
const fetchProjectList = () => {
  loadProjectList();
};

// 加载项目列表
async function loadProjectList() {
  loading.value = true;
  try {
    // 模拟数据
    projectList.value = [
      {
        id: '1',
        code: 'P001-C250624001',
        name: '智能办公平台开发',
        customerName: '北京未来科技有限公司',
        customerId: '1',
        type: 'software',
        status: 'progress',
        progress: 65,
        managerName: '张三',
        startDate: '2024-01-15',
        endDate: '2024-06-30',
      },
      {
        id: '2',
        code: 'P002-C250624002',
        name: '企业CRM系统集成',
        customerName: '上海数创信息技术有限公司',
        customerId: '2',
        type: 'integration',
        status: 'progress',
        progress: 80,
        managerName: '李四',
        startDate: '2024-02-01',
        endDate: '2024-07-15',
      },
      {
        id: '3',
        code: 'P003-C250624003',
        name: '数据中心运维服务',
        customerName: '广州智能科技有限公司',
        customerId: '3',
        type: 'maintenance',
        status: 'completed',
        progress: 100,
        managerName: '王五',
        startDate: '2024-01-01',
        endDate: '2024-05-31',
      },
    ];
    
    pagination.total = projectList.value.length;
  } catch (error) {
    console.error('加载项目列表失败:', error);
    message.error('加载项目列表失败');
  } finally {
    loading.value = false;
  }
}

// 初始化
onMounted(() => {
  // 初始化字段配置
  loadColumnConfig();
  loadProjectList();
});
</script>

<style scoped>
.project-list-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.advanced-search {
  transition: all 0.3s ease;
  max-height: 0;
  overflow: hidden;
}

.advanced-search.expanded {
  max-height: 200px;
}

.search-input {
  transition: all 0.3s ease;
}

.search-input:focus-within {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 字段配置样式 */
.column-config-content {
  .column-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 8px;
  }
  
  .column-item {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .column-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 4px;
    cursor: move;
    
    &:hover {
      background: #f0f0f0;
    }
  }
  
  .drag-handle {
    margin-right: 8px;
    color: #999;
    cursor: move;
  }
  
  .column-width {
    margin-left: auto;
    display: flex;
    align-items: center;
    
    .width-label {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }
}
</style>