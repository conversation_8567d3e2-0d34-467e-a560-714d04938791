<template>
  <div class="project-information-container animate-fadeIn">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">项目信息</h2>
          <p class="text-gray-500 mt-1">管理和查看所有项目基本信息</p>
        </div>
        <a-button type="primary" @click="exportProjects">
          <template #icon><download-outlined /></template>
          导出项目信息
        </a-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <a-card class="mb-6" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="项目名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入项目名称" allowClear />
        </a-form-item>
        
        <a-form-item label="项目编号">
          <a-input v-model:value="searchForm.id" placeholder="请输入项目编号" allowClear />
        </a-form-item>
        
        <a-form-item label="项目类型">
          <a-select
            v-model:value="searchForm.type"
            style="width: 180px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="software_sale">软件产品销售</a-select-option>
            <a-select-option value="hardware_sale">硬件产品销售</a-select-option>
            <a-select-option value="integration_sale">系统集成销售</a-select-option>
            <a-select-option value="own_software_sale">自有软件销售</a-select-option>
            <a-select-option value="own_hardware_sale">自有硬件销售</a-select-option>
            <a-select-option value="software_dev_own">软件开发服务（自有）</a-select-option>
            <a-select-option value="software_dev_outsource">软件开发服务（外包）</a-select-option>
            <a-select-option value="system_maintenance">系统运维服务</a-select-option>
            <a-select-option value="other_tech_service">其他技术服务</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="负责人">
          <a-select
            v-model:value="searchForm.manager"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="客户">
          <a-select
            v-model:value="searchForm.customer"
            style="width: 160px"
            placeholder="请选择"
            allowClear
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="item in customerOptions" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="日期范围">
          <a-range-picker v-model:value="searchForm.dateRange" style="width: 240px" />
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <template #icon><reload-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据统计 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">总项目数</span>
            <a-tooltip title="所有项目数量">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <div class="text-2xl font-bold">{{ statistics.totalProjects }}</div>
        <div class="text-sm text-gray-500">较上月增长 {{ statistics.totalGrowth }}%</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">进行中项目</span>
            <a-tooltip title="正在执行中的项目数量">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <div class="text-2xl font-bold text-blue-500">{{ statistics.inProgressProjects }}</div>
        <div class="text-sm text-gray-500">占总数 {{ statistics.inProgressPercentage }}%</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">已完成项目</span>
            <a-tooltip title="已结项的项目数量">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <div class="text-2xl font-bold text-green-500">{{ statistics.completedProjects }}</div>
        <div class="text-sm text-gray-500">占总数 {{ statistics.completedPercentage }}%</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">逾期项目</span>
            <a-tooltip title="超出计划结束日期但未完成的项目">
              <question-circle-outlined />
            </a-tooltip>
          </div>
        </template>
        <div class="text-2xl font-bold text-red-500">{{ statistics.overdueProjects }}</div>
        <div class="text-sm text-gray-500">占进行中 {{ statistics.overduePercentage }}%</div>
      </a-card>
    </div>

    <!-- 视图切换 -->
    <div class="flex justify-between mb-4">
      <a-radio-group v-model:value="viewMode" button-style="solid">
        <a-radio-button value="table">
          <template #icon><table-outlined /></template>
          表格视图
        </a-radio-button>
        <a-radio-button value="card">
          <template #icon><appstore-outlined /></template>
          卡片视图
        </a-radio-button>
      </a-radio-group>
      <a-select v-model:value="sortOrder" style="width: 160px">
        <a-select-option value="newest">最近创建</a-select-option>
        <a-select-option value="name">名称排序</a-select-option>
        <a-select-option value="endDate">结束日期</a-select-option>
        <a-select-option value="progress">进度</a-select-option>
      </a-select>
    </div>

    <!-- 表格视图 -->
    <a-card v-if="viewMode === 'table'" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="projectList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <!-- 项目名称 -->
          <template v-if="column.dataIndex === 'name'">
            <a @click="viewProject(record)">{{ record.name }}</a>
                          <div class="text-xs text-gray-500">编号: {{ record.id }}</div>
          </template>
          
          <!-- 客户信息 -->
          <template v-if="column.dataIndex === 'customer'">
            <div>
              <div>{{ record.customerName }}</div>
              <div class="text-xs text-gray-500">{{ record.customerContact }}</div>
            </div>
          </template>
          
          <!-- 项目类型 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag>{{ getProjectTypeName(record.type) }}</a-tag>
          </template>
          
          <!-- 进度 -->
          <template v-if="column.dataIndex === 'progress'">
            <div>
              <a-progress :percent="record.progress" size="small" :status="getProgressStatus(record)" />
              <div class="text-xs text-gray-500">
                {{ record.progressUpdateTime }}更新
              </div>
            </div>
          </template>
          
          <!-- 状态 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          
          <!-- 时间 -->
          <template v-if="column.dataIndex === 'timeRange'">
            <div>
              <div>{{ record.startDate }} 至</div>
              <div>{{ record.endDate }}</div>
            </div>
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="viewProject(record)"
              >
                查看
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="editProject(record)">编辑</a-menu-item>
                    <a-menu-item key="2" @click="exportProjectDetails(record)">导出</a-menu-item>
                    <a-menu-item key="3" @click="archiveProject(record)">归档</a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 卡片视图 -->
    <div v-else class="grid grid-cols-3 gap-4">
      <a-card 
        v-for="project in projectList" 
        :key="project.id" 
        :bordered="false" 
        class="hover:shadow-md transition-shadow"
      >
        <template #title>
          <div class="flex items-center justify-between">
            <a @click="viewProject(project)" class="text-lg font-medium hover:text-blue-500">
              {{ project.name }}
            </a>
            <a-tag :color="getStatusColor(project.status)">
              {{ getStatusName(project.status) }}
            </a-tag>
          </div>
        </template>
        <div class="space-y-3">
          <div class="text-sm text-gray-500">编号: {{ project.id }}</div>
          <div class="flex justify-between items-center">
            <span>类型:</span>
            <a-tag>{{ getProjectTypeName(project.type) }}</a-tag>
          </div>
          <div class="flex justify-between items-center">
            <span>客户:</span>
            <span>{{ project.customerName }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span>负责人:</span>
            <span>{{ project.managerName }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span>周期:</span>
            <span>{{ project.startDate }} 至 {{ project.endDate }}</span>
          </div>
          <div>
            <div class="flex justify-between items-center mb-1">
              <span>进度:</span>
              <span>{{ project.progress }}%</span>
            </div>
            <a-progress :percent="project.progress" size="small" :status="getProgressStatus(project)" />
          </div>
        </div>
        <template #actions>
          <a-button type="link" @click="viewProject(project)">查看</a-button>
          <a-button type="link" @click="editProject(project)">编辑</a-button>
          <a-button type="link" @click="exportProjectDetails(project)">导出</a-button>
        </template>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  DownloadOutlined,
  SearchOutlined,
  ReloadOutlined,
  TableOutlined,
  AppstoreOutlined,
  QuestionCircleOutlined,
  DownOutlined,
} from '@ant-design/icons-vue';

// 路由
const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '项目信息',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '客户',
    dataIndex: 'customer',
    key: 'customer',
    width: 150,
  },
  {
    title: '项目类型',
    dataIndex: 'type',
    key: 'type',
    width: 130,
  },
  {
    title: '项目进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '时间范围',
    dataIndex: 'timeRange',
    key: 'timeRange',
    width: 180,
  },
  {
    title: '负责人',
    dataIndex: 'managerName',
    key: 'managerName',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 150,
  },
];

// 加载状态
const loading = ref(false);

// 视图模式
const viewMode = ref('table');
const sortOrder = ref('newest');

// 统计信息
const statistics = reactive({
  totalProjects: 125,
  totalGrowth: 8.5,
  inProgressProjects: 48,
  inProgressPercentage: 38.4,
  completedProjects: 72,
  completedPercentage: 57.6,
  overdueProjects: 5,
  overduePercentage: 10.4,
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  name: '',
  id: '',
  type: undefined,
  manager: undefined,
  customer: undefined,
  dateRange: [],
});

// 项目列表数据
const projectList = ref<any[]>([]);

// 客户选项
const customerOptions = ref([
  { id: 'C100001', name: '北京科技有限公司', contact: '张经理', phone: '13800138001', address: '北京市朝阳区科技园' },
  { id: 'C100002', name: '上海数字科技有限公司', contact: '李总', phone: '13800138002', address: '上海市浦东新区张江高科技园区' },
  { id: 'C100003', name: '广州智能科技有限公司', contact: '王董', phone: '13800138003', address: '广州市天河区软件园' },
  { id: 'C100004', name: '深圳创新科技有限公司', contact: '赵主任', phone: '13800138004', address: '深圳市南山区科技园' },
  { id: 'C100005', name: '杭州网络科技有限公司', contact: '钱经理', phone: '13800138005', address: '杭州市西湖区文三路' },
]);

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  searchForm.id = '';
  searchForm.type = undefined;
  searchForm.manager = undefined;
  searchForm.customer = undefined;
  searchForm.dateRange = [];
  handleSearch();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchProjectList();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchProjectList();
};

// 获取项目列表
const fetchProjectList = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    try {
      // 模拟数据
      const mockData = Array.from({ length: 30 }, (_, index) => {
        const customerId = `C${String(100000 + index % 5 + 1).padStart(6, '0')}`;
        const customerInfo = customerOptions.value.find(c => c.id === customerId) || customerOptions.value[0];
        
        const startDate = dayjs().subtract(Math.floor(Math.random() * 180), 'day');
        const endDate = dayjs(startDate).add(Math.floor(Math.random() * 180) + 30, 'day');
        const progress = Math.floor(Math.random() * 101);
        
        let status = 'in_progress';
        if (progress === 100) {
          status = 'completed';
        } else if (dayjs().isAfter(endDate) && progress < 100) {
          status = 'overdue';
        } else if (progress < 10) {
          status = 'not_started';
        }
        
        return {
          id: `P${String(10000 + index).padStart(5, '0')}`,
          name: `${['智能办公系统', 'ERP系统升级', '数据中心建设', '网络安全防护', 'CRM系统实施'][index % 5]}项目${index + 1}`,
          customerId: customerInfo.id,
          customerName: customerInfo.name,
          customerContact: customerInfo.contact,
          type: ['software_sale', 'hardware_sale', 'integration_sale', 'software_dev_own', 'system_maintenance'][index % 5],
          managerId: String(index % 3 + 1),
          managerName: ['张三', '李四', '王五'][index % 3],
          progress: progress,
          progressUpdateTime: dayjs().subtract(Math.floor(Math.random() * 10), 'day').format('YYYY-MM-DD'),
          startDate: startDate.format('YYYY-MM-DD'),
          endDate: endDate.format('YYYY-MM-DD'),
          status: status,
        };
      });
      
      // 根据搜索条件过滤
      let filteredData = [...mockData];
      
      if (searchForm.name) {
        filteredData = filteredData.filter(item => item.name.includes(searchForm.name));
      }
      
      if (searchForm.id) {
        filteredData = filteredData.filter(item => item.id.includes(searchForm.id));
      }
      
      if (searchForm.type) {
        filteredData = filteredData.filter(item => item.type === searchForm.type);
      }
      
      if (searchForm.manager) {
        filteredData = filteredData.filter(item => item.managerId === searchForm.manager);
      }
      
      if (searchForm.customer) {
        filteredData = filteredData.filter(item => item.customerId === searchForm.customer);
      }
      
      if (searchForm.dateRange && searchForm.dateRange.length === 2) {
        const startDate = dayjs(searchForm.dateRange[0]).format('YYYY-MM-DD');
        const endDate = dayjs(searchForm.dateRange[1]).format('YYYY-MM-DD');
        
        filteredData = filteredData.filter(item => {
          return (item.startDate >= startDate && item.startDate <= endDate) ||
                 (item.endDate >= startDate && item.endDate <= endDate);
        });
      }
      
      // 排序
      if (sortOrder.value === 'newest') {
        filteredData.sort((a, b) => a.id.localeCompare(b.id));
      } else if (sortOrder.value === 'name') {
        filteredData.sort((a, b) => a.name.localeCompare(b.name));
      } else if (sortOrder.value === 'endDate') {
        filteredData.sort((a, b) => a.endDate.localeCompare(b.endDate));
      } else if (sortOrder.value === 'progress') {
        filteredData.sort((a, b) => b.progress - a.progress);
      }
      
      // 分页
      const start = (pagination.current - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      projectList.value = filteredData.slice(start, end);
      pagination.total = filteredData.length;
      
      // 更新统计数据
      statistics.totalProjects = filteredData.length;
      statistics.completedProjects = filteredData.filter(item => item.status === 'completed').length;
      statistics.inProgressProjects = filteredData.filter(item => ['in_progress', 'not_started'].includes(item.status)).length;
      statistics.overdueProjects = filteredData.filter(item => item.status === 'overdue').length;
      
      statistics.totalGrowth = 5.8; // 模拟数据
      statistics.completedPercentage = Math.round((statistics.completedProjects / statistics.totalProjects) * 100) || 0;
      statistics.inProgressPercentage = Math.round((statistics.inProgressProjects / statistics.totalProjects) * 100) || 0;
      statistics.overduePercentage = Math.round((statistics.overdueProjects / statistics.inProgressProjects) * 100) || 0;
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
      projectList.value = [];
    } finally {
      loading.value = false;
    }
  }, 500);
};

// 获取项目类型名称
const getProjectTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'software_sale': '软件产品销售',
    'hardware_sale': '硬件产品销售',
    'integration_sale': '系统集成销售',
    'own_software_sale': '自有软件销售',
    'own_hardware_sale': '自有硬件销售',
    'software_dev_own': '软件开发服务（自有）',
    'software_dev_outsource': '软件开发服务（外包）',
    'system_maintenance': '系统运维服务',
    'other_tech_service': '其他技术服务',
  };
  return typeMap[type] || type;
};

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'not_started': '未开始',
    'in_progress': '进行中',
    'completed': '已完成',
    'overdue': '已逾期',
    'suspended': '已暂停',
    'cancelled': '已取消',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'not_started': 'default',
    'in_progress': 'blue',
    'completed': 'green',
    'overdue': 'red',
    'suspended': 'orange',
    'cancelled': 'gray',
  };
  return colorMap[status] || 'default';
};

// 获取进度状态
const getProgressStatus = (record: any) => {
  if (record.status === 'completed') return 'success';
  if (record.status === 'overdue') return 'exception';
  return 'normal';
};

// 查看项目
const viewProject = (record: any) => {
  router.push(`/projects/detail/${record.id}`);
};

// 编辑项目
const editProject = (record: any) => {
  message.info(`编辑项目: ${record.name}`);
};

// 导出项目详情
const exportProjectDetails = (record: any) => {
  message.success(`已开始导出项目 ${record.name} 的详细信息`);
};

// 归档项目
const archiveProject = (record: any) => {
  message.success(`项目 ${record.name} 已归档`);
};

// 导出所有项目
const exportProjects = () => {
  message.success('已开始导出所有项目信息');
};

// select筛选
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 初始化
onMounted(() => {
  fetchProjectList();
});
</script>

<style scoped>
.project-information-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 