<template>
  <div class="project-completion-container animate-fadeIn">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">项目结项</h2>
          <p class="text-gray-500 mt-1">管理项目的结项流程与验收交付</p>
        </div>
        <a-button type="primary" @click="exportCompletionReport">
          <template #icon><file-excel-outlined /></template>
          导出结项报告
        </a-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <a-card class="mb-6" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="项目名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入项目名称" allowClear />
        </a-form-item>
        
        <a-form-item label="项目编号">
          <a-input v-model:value="searchForm.id" placeholder="请输入项目编号" allowClear />
        </a-form-item>
        
        <a-form-item label="负责人">
          <a-select
            v-model:value="searchForm.manager"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="结项状态">
          <a-select
            v-model:value="searchForm.status"
            style="width: 160px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="pending_completion">待结项</a-select-option>
            <a-select-option value="completion_in_progress">结项中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="archived">已归档</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="结项时间">
          <a-range-picker v-model:value="searchForm.completionTimeRange" style="width: 230px" />
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <template #icon><reload-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">待结项</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-orange-500">{{ statistics.pendingCompletion }}</div>
        <div class="text-sm text-gray-500">较上月{{ statistics.pendingCompletionChange > 0 ? '增加' : '减少' }} {{ Math.abs(statistics.pendingCompletionChange) }}个</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">结项中</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-blue-500">{{ statistics.inCompletion }}</div>
        <div class="text-sm text-gray-500">较上月{{ statistics.inCompletionChange > 0 ? '增加' : '减少' }} {{ Math.abs(statistics.inCompletionChange) }}个</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">本月已完成</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-green-500">{{ statistics.completedThisMonth }}</div>
        <div class="text-sm text-gray-500">较上月{{ statistics.completedThisMonthChange > 0 ? '增加' : '减少' }} {{ Math.abs(statistics.completedThisMonthChange) }}%</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">平均结项周期</span>
          </div>
        </template>
        <div class="text-2xl font-bold">{{ statistics.avgCompletionDays }}天</div>
        <div class="text-sm text-gray-500">较上月{{ statistics.avgCompletionDaysChange > 0 ? '增加' : '减少' }} {{ Math.abs(statistics.avgCompletionDaysChange) }}天</div>
      </a-card>
    </div>

    <!-- 项目结项列表 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="projectList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <!-- 项目信息 -->
          <template v-if="column.dataIndex === 'name'">
            <div>
              <a @click="viewProject(record)" class="font-medium">{{ record.name }}</a>
              <div class="text-xs text-gray-500">编号: {{ record.id }}</div>
            </div>
          </template>
          
          <!-- 客户信息 -->
          <template v-if="column.dataIndex === 'customer'">
            <div>
              <div>{{ record.customerName }}</div>
              <div class="text-xs text-gray-500">{{ record.customerContact }}</div>
            </div>
          </template>
          
          <!-- 合同履行 -->
          <template v-if="column.dataIndex === 'contract'">
            <div>
              <div>合同额: ¥{{ formatMoney(record.contractAmount) }}</div>
              <div class="mt-1 flex items-center">
                <span class="mr-1">履行率:</span>
                <a-progress 
                  :percent="record.contractFulfillmentRate" 
                  size="small" 
                  :status="record.contractFulfillmentRate === 100 ? 'success' : 'normal'" 
                />
              </div>
            </div>
          </template>
          
          <!-- 结项状态 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          
          <!-- 结项时间 -->
          <template v-if="column.dataIndex === 'completionDate'">
            <div>
              <div>计划: {{ record.plannedCompletionDate || '未设置' }}</div>
              <div>实际: {{ record.actualCompletionDate || '未完成' }}</div>
            </div>
          </template>
          
          <!-- 验收信息 -->
          <template v-if="column.dataIndex === 'acceptance'">
            <div>
              <div>
                <a-tag :color="getAcceptanceStatusColor(record.acceptanceStatus)">
                  {{ getAcceptanceStatusName(record.acceptanceStatus) }}
                </a-tag>
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ record.acceptanceDate || '未验收' }}
              </div>
            </div>
          </template>
          
          <!-- 结项文档 -->
          <template v-if="column.dataIndex === 'documents'">
            <div>
              <a-progress 
                type="circle" 
                :percent="getDocumentCompletionRate(record)" 
                :width="30" 
                :format="(percent: number) => `${record.completedDocuments}/${record.totalDocuments}`"
              />
            </div>
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="viewProject(record)"
              >
                详情
              </a-button>
              <a-button 
                v-if="['pending_completion', 'completion_in_progress'].includes(record.status)" 
                type="link" 
                size="small" 
                @click="startCompletion(record)"
              >
                {{ record.status === 'pending_completion' ? '发起结项' : '结项管理' }}
              </a-button>
              <a-button 
                v-if="record.status === 'completion_in_progress'" 
                type="link" 
                size="small" 
                @click="manageAcceptance(record)"
              >
                验收管理
              </a-button>
              <a-button 
                v-if="record.status === 'completed'" 
                type="link" 
                size="small" 
                @click="archiveProject(record)"
              >
                归档
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 结项表单弹窗 -->
    <a-modal
      v-model:visible="completionModalVisible"
      :title="selectedProject ? `${selectedProject.name} - 项目结项管理` : '项目结项管理'"
      width="800px"
      @ok="handleCompletionSubmit"
      @cancel="completionModalVisible = false"
    >
      <a-form
        :model="completionForm"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        v-if="selectedProject"
      >
        <a-tabs v-model:activeKey="activeCompletionTab">
          <!-- 基本信息 -->
          <a-tab-pane key="basic" tab="基本信息">
                          <a-form-item label="项目编号">
              <span>{{ selectedProject.id }}</span>
            </a-form-item>
            
            <a-form-item label="计划结项日期" name="plannedCompletionDate">
              <a-date-picker 
                v-model:value="completionForm.plannedCompletionDate" 
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
            
            <a-form-item label="实际结项日期" name="actualCompletionDate">
              <a-date-picker 
                v-model:value="completionForm.actualCompletionDate" 
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
            
            <a-form-item label="结项状态" name="status">
              <a-select v-model:value="completionForm.status">
                <a-select-option value="pending_completion">待结项</a-select-option>
                <a-select-option value="completion_in_progress">结项中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="结项说明" name="completionNotes">
              <a-textarea 
                v-model:value="completionForm.completionNotes" 
                :rows="4" 
                placeholder="请输入项目结项说明" 
              />
            </a-form-item>
          </a-tab-pane>
          
          <!-- 验收信息 -->
          <a-tab-pane key="acceptance" tab="验收信息">
            <a-form-item label="验收状态" name="acceptanceStatus">
              <a-select v-model:value="completionForm.acceptanceStatus">
                <a-select-option value="not_started">未开始</a-select-option>
                <a-select-option value="in_progress">验收中</a-select-option>
                <a-select-option value="accepted">已验收</a-select-option>
                <a-select-option value="accepted_with_issues">验收有条件通过</a-select-option>
                <a-select-option value="rejected">验收不通过</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="验收日期" name="acceptanceDate">
              <a-date-picker 
                v-model:value="completionForm.acceptanceDate" 
                style="width: 100%"
                format="YYYY-MM-DD"
              />
            </a-form-item>
            
            <a-form-item label="验收人员" name="acceptancePersonnel">
              <a-select 
                v-model:value="completionForm.acceptancePersonnel" 
                mode="multiple" 
                placeholder="请选择验收人员"
              >
                <a-select-option value="1">张三（客户方）</a-select-option>
                <a-select-option value="2">李四（客户方）</a-select-option>
                <a-select-option value="3">王五（本方）</a-select-option>
                <a-select-option value="4">赵六（本方）</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="验收意见" name="acceptanceComments">
              <a-textarea 
                v-model:value="completionForm.acceptanceComments" 
                :rows="4" 
                placeholder="请输入验收意见" 
              />
            </a-form-item>
          </a-tab-pane>
          
          <!-- 文档管理 -->
          <a-tab-pane key="documents" tab="结项文档">
            <a-form-item label="结项文档">
              <a-checkbox-group v-model:value="completionForm.documents">
                <div class="space-y-2">
                  <a-checkbox value="projectDeliveryReport">项目交付报告</a-checkbox>
                  <a-checkbox value="acceptanceCertificate">验收证书</a-checkbox>
                  <a-checkbox value="userFeedback">用户反馈表</a-checkbox>
                  <a-checkbox value="finalPaymentInvoice">尾款发票</a-checkbox>
                  <a-checkbox value="projectSummary">项目总结报告</a-checkbox>
                  <a-checkbox value="technicalDocumentation">技术文档</a-checkbox>
                  <a-checkbox value="userManual">用户手册</a-checkbox>
                </div>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="上传文档">
              <a-upload
                name="file"
                :multiple="true"
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                :headers="{ authorization: 'authorization-text' }"
                :file-list="fileList"
              >
                <a-button>
                  <upload-outlined /> 上传文件
                </a-button>
              </a-upload>
            </a-form-item>
          </a-tab-pane>
          
          <!-- 结算信息 -->
          <a-tab-pane key="settlement" tab="结算信息">
            <a-form-item label="合同总金额">
              <span>¥{{ formatMoney(selectedProject.contractAmount) }}</span>
            </a-form-item>
            
            <a-form-item label="已收款金额" name="receivedAmount">
              <a-input-number 
                v-model:value="completionForm.receivedAmount"
                style="width: 100%"
                :min="0"
                :max="selectedProject.contractAmount"
                :formatter="(value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value: string) => value.replace(/\¥\s?|(,*)/g, '')"
              />
            </a-form-item>
            
            <a-form-item label="待收款金额">
              <span>¥{{ formatMoney(selectedProject.contractAmount - completionForm.receivedAmount) }}</span>
            </a-form-item>
            
            <a-form-item label="收款状态" name="paymentStatus">
              <a-select v-model:value="completionForm.paymentStatus">
                <a-select-option value="pending">待收款</a-select-option>
                <a-select-option value="partial">部分收款</a-select-option>
                <a-select-option value="completed">已收全款</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="发票状态" name="invoiceStatus">
              <a-select v-model:value="completionForm.invoiceStatus">
                <a-select-option value="not_issued">未开票</a-select-option>
                <a-select-option value="partial">部分开票</a-select-option>
                <a-select-option value="completed">已全部开票</a-select-option>
              </a-select>
            </a-form-item>
          </a-tab-pane>
        </a-tabs>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  FileExcelOutlined,
  SearchOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';

// 路由
const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '项目信息',
    dataIndex: 'name',
    key: 'name',
    width: 180,
  },
  {
    title: '客户信息',
    dataIndex: 'customer',
    key: 'customer',
    width: 180,
  },
  {
    title: '合同履行',
    dataIndex: 'contract',
    key: 'contract',
    width: 180,
  },
  {
    title: '结项状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '结项时间',
    dataIndex: 'completionDate',
    key: 'completionDate',
    width: 150,
  },
  {
    title: '验收情况',
    dataIndex: 'acceptance',
    key: 'acceptance',
    width: 120,
  },
  {
    title: '结项文档',
    dataIndex: 'documents',
    key: 'documents',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 240,
  },
];

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 统计信息
const statistics = reactive({
  pendingCompletion: 12,
  pendingCompletionChange: -3,
  inCompletion: 8,
  inCompletionChange: 2,
  completedThisMonth: 5,
  completedThisMonthChange: 25,
  avgCompletionDays: 18,
  avgCompletionDaysChange: -2,
});

// 搜索表单
const searchForm = reactive({
  name: '',
  id: '',
  manager: undefined,
  status: undefined,
  completionTimeRange: [] as any[],
});

// 项目列表数据
const projectList = ref<any[]>([]);

// 结项弹窗相关
const completionModalVisible = ref(false);
const selectedProject = ref<any>(null);
const activeCompletionTab = ref('basic');
const fileList = ref<any[]>([]);

// 结项表单
const completionForm = reactive({
  plannedCompletionDate: null as any,
  actualCompletionDate: null as any,
  status: 'completion_in_progress',
  completionNotes: '',
  acceptanceStatus: 'not_started',
  acceptanceDate: null as any,
  acceptancePersonnel: [] as string[],
  acceptanceComments: '',
  documents: [] as string[],
  receivedAmount: 0,
  paymentStatus: 'pending',
  invoiceStatus: 'not_issued',
});

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  searchForm.id = '';
  searchForm.manager = undefined;
  searchForm.status = undefined;
  searchForm.completionTimeRange = [];
  handleSearch();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchProjectList();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchProjectList();
};

// 获取项目列表
const fetchProjectList = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    try {
      // 模拟数据
      const mockData = Array.from({ length: 30 }, (_, index) => ({
        id: `P${String(10000 + index).padStart(5, '0')}`,
        name: `${['智能办公系统', 'ERP系统升级', '数据中心建设', '网络安全防护', 'CRM系统实施'][index % 5]}项目${index + 1}`,
        customerId: String(Math.floor(Math.random() * 5 + 1)),
        customerName: ['北京科技有限公司', '上海数字科技有限公司', '广州智能科技有限公司', '深圳创新科技有限公司', '杭州网络科技有限公司'][Math.floor(Math.random() * 5)],
        customerContact: ['张经理', '李经理', '王总', '赵主管', '钱总'][Math.floor(Math.random() * 5)],
        contractAmount: Math.floor(Math.random() * 1000000) + 200000,
        contractFulfillmentRate: Math.floor(Math.random() * 101),
        plannedCompletionDate: dayjs().add(Math.floor(Math.random() * 90) - 45, 'day').format('YYYY-MM-DD'),
        actualCompletionDate: Math.random() > 0.3 ? dayjs().add(Math.floor(Math.random() * 30) - 15, 'day').format('YYYY-MM-DD') : null,
        managerId: String(Math.floor(Math.random() * 3 + 1)),
        managerName: ['张三', '李四', '王五'][Math.floor(Math.random() * 3)],
        status: ['pending_completion', 'completion_in_progress', 'completed', 'archived'][Math.floor(Math.random() * 4)],
        acceptanceStatus: ['not_started', 'in_progress', 'accepted', 'rejected'][Math.floor(Math.random() * 4)],
        acceptanceDate: Math.random() > 0.4 ? dayjs().add(Math.floor(Math.random() * 15) - 7, 'day').format('YYYY-MM-DD') : null,
        completedDocuments: Math.floor(Math.random() * 11),
        totalDocuments: 10,
        qualityScore: Math.floor(Math.random() * 41) + 60,
        createTime: dayjs().subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD'),
      }));
      
      // 根据搜索条件过滤
      let filteredData = [...mockData];
      
      if (searchForm.name) {
        filteredData = filteredData.filter(item => item.name.includes(searchForm.name));
      }
      
      if (searchForm.id) {
        filteredData = filteredData.filter(item => item.id.includes(searchForm.id));
      }
      
      if (searchForm.manager) {
        filteredData = filteredData.filter(item => item.managerId === searchForm.manager);
      }
      
      if (searchForm.status) {
        filteredData = filteredData.filter(item => item.status === searchForm.status);
      }
      
      if (searchForm.completionTimeRange && searchForm.completionTimeRange.length === 2) {
        const startDate = dayjs(searchForm.completionTimeRange[0]).format('YYYY-MM-DD');
        const endDate = dayjs(searchForm.completionTimeRange[1]).format('YYYY-MM-DD');
        
        filteredData = filteredData.filter(item => {
          if (!item.actualCompletionDate) return false;
          return item.actualCompletionDate >= startDate && item.actualCompletionDate <= endDate;
        });
      }
      
      // 分页
      const start = (pagination.current - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      projectList.value = filteredData.slice(start, end);
      pagination.total = filteredData.length;
      
      // 更新统计数据
      statistics.pendingCompletion = filteredData.filter(item => item.status === 'pending_completion').length;
      statistics.inCompletion = filteredData.filter(item => item.status === 'completion_in_progress').length;
      statistics.completedThisMonth = filteredData.filter(item => 
        item.status === 'completed' && 
        item.actualCompletionDate && 
        dayjs(item.actualCompletionDate).isAfter(dayjs().startOf('month'))
      ).length;
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
      projectList.value = [];
    } finally {
      loading.value = false;
    }
  }, 500);
};

// 辅助函数：基于权重随机选择
const weightedRandom = (options: string[], weights: number[]) => {
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
  const randomValue = Math.random() * totalWeight;
  
  let weightSum = 0;
  for (let i = 0; i < options.length; i++) {
    weightSum += weights[i];
    if (randomValue <= weightSum) {
      return options[i];
    }
  }
  
  return options[0];
};

// 格式化金额
const formatMoney = (amount: number) => {
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending_completion': '待结项',
    'completion_in_progress': '结项中',
    'completed': '已完成',
    'archived': '已归档',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'pending_completion': 'orange',
    'completion_in_progress': 'blue',
    'completed': 'green',
    'archived': 'purple',
  };
  return colorMap[status] || 'default';
};

// 获取验收状态名称
const getAcceptanceStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'not_started': '未开始',
    'in_progress': '验收中',
    'accepted': '已验收',
    'accepted_with_issues': '有条件通过',
    'rejected': '不通过',
  };
  return statusMap[status] || status;
};

// 获取验收状态颜色
const getAcceptanceStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'not_started': 'default',
    'in_progress': 'blue',
    'accepted': 'green',
    'accepted_with_issues': 'orange',
    'rejected': 'red',
  };
  return colorMap[status] || 'default';
};

// 获取文档完成率
const getDocumentCompletionRate = (record: any) => {
  if (!record.totalDocuments) return 0;
  return Math.round((record.completedDocuments / record.totalDocuments) * 100);
};

// 查看项目
const viewProject = (record: any) => {
  router.push(`/projects/detail/${record.id}`);
};

// 发起结项
const startCompletion = (record: any) => {
  selectedProject.value = record;
  
  // 重置表单
  completionForm.plannedCompletionDate = record.plannedCompletionDate ? dayjs(record.plannedCompletionDate) : null;
  completionForm.actualCompletionDate = record.actualCompletionDate ? dayjs(record.actualCompletionDate) : null;
  completionForm.status = record.status;
  completionForm.completionNotes = '';
  completionForm.acceptanceStatus = record.acceptanceStatus;
  completionForm.acceptanceDate = record.acceptanceDate ? dayjs(record.acceptanceDate) : null;
  completionForm.acceptancePersonnel = [];
  completionForm.acceptanceComments = '';
  completionForm.documents = [];
  completionForm.receivedAmount = Math.round(record.contractAmount * 0.7); // 假设已收款70%
  completionForm.paymentStatus = 'partial';
  completionForm.invoiceStatus = 'partial';
  
  activeCompletionTab.value = 'basic';
  completionModalVisible.value = true;
};

// 验收管理
const manageAcceptance = (record: any) => {
  selectedProject.value = record;
  
  // 重置表单 (同上，但切换到验收标签)
  completionForm.plannedCompletionDate = record.plannedCompletionDate ? dayjs(record.plannedCompletionDate) : null;
  completionForm.actualCompletionDate = record.actualCompletionDate ? dayjs(record.actualCompletionDate) : null;
  completionForm.status = record.status;
  completionForm.completionNotes = '';
  completionForm.acceptanceStatus = record.acceptanceStatus;
  completionForm.acceptanceDate = record.acceptanceDate ? dayjs(record.acceptanceDate) : null;
  completionForm.acceptancePersonnel = [];
  completionForm.acceptanceComments = '';
  completionForm.documents = [];
  completionForm.receivedAmount = Math.round(record.contractAmount * 0.7);
  completionForm.paymentStatus = 'partial';
  completionForm.invoiceStatus = 'partial';
  
  activeCompletionTab.value = 'acceptance';
  completionModalVisible.value = true;
};

// 归档项目
const archiveProject = (record: any) => {
  message.success(`项目 ${record.name} 已归档`);
  record.status = 'archived';
};

// 处理结项提交
const handleCompletionSubmit = () => {
  if (!selectedProject.value) return;
  
  // 模拟更新
  selectedProject.value.status = completionForm.status;
  selectedProject.value.plannedCompletionDate = completionForm.plannedCompletionDate ? dayjs(completionForm.plannedCompletionDate).format('YYYY-MM-DD') : null;
  selectedProject.value.actualCompletionDate = completionForm.actualCompletionDate ? dayjs(completionForm.actualCompletionDate).format('YYYY-MM-DD') : null;
  selectedProject.value.acceptanceStatus = completionForm.acceptanceStatus;
  selectedProject.value.acceptanceDate = completionForm.acceptanceDate ? dayjs(completionForm.acceptanceDate).format('YYYY-MM-DD') : null;
  
  // 更新文档完成数量
  if (completionForm.documents.length) {
    selectedProject.value.completedDocuments = completionForm.documents.length;
  }
  
  message.success(`项目 ${selectedProject.value.name} 结项信息已更新`);
  completionModalVisible.value = false;
};

// 导出结项报告
const exportCompletionReport = () => {
  message.success('结项报告导出中，请稍候...');
};

// 初始化
onMounted(() => {
  fetchProjectList();
});
</script>

<style scoped>
.project-completion-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 