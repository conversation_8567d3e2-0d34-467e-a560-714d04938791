<template>
  <div class="project-execution-container animate-fadeIn">
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">项目执行</h2>
          <p class="text-gray-500 mt-1">跟踪和管理进行中的项目执行情况</p>
        </div>
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新数据
          </a-button>
          <a-button type="primary" @click="exportExecutionReport">
            <template #icon><file-excel-outlined /></template>
            导出执行报告
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选条件 -->
    <a-card class="mb-6" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="项目名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入项目名称" allowClear />
        </a-form-item>
        
        <a-form-item label="项目编号">
          <a-input v-model:value="searchForm.id" placeholder="请输入项目编号" allowClear />
        </a-form-item>
        
        <a-form-item label="负责人">
          <a-select
            v-model:value="searchForm.manager"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="进度状态">
          <a-select
            v-model:value="searchForm.progressStatus"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="ahead">提前</a-select-option>
            <a-select-option value="on_track">正常</a-select-option>
            <a-select-option value="delayed">延迟</a-select-option>
            <a-select-option value="at_risk">风险中</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="完成度">
          <a-select
            v-model:value="searchForm.completionRange"
            style="width: 150px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="0-25">0-25%</a-select-option>
            <a-select-option value="25-50">25-50%</a-select-option>
            <a-select-option value="50-75">50-75%</a-select-option>
            <a-select-option value="75-100">75-100%</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <template #icon><reload-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">进行中项目</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-blue-500">{{ statistics.inProgress }}</div>
        <div class="text-sm text-gray-500">{{ statistics.inProgressPercentage }}% 的项目</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">按计划进行</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-green-500">{{ statistics.onTrack }}</div>
        <div class="text-sm text-gray-500">{{ statistics.onTrackPercentage }}% 的进行中项目</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">风险项目</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-orange-500">{{ statistics.atRisk }}</div>
        <div class="text-sm text-gray-500">{{ statistics.atRiskPercentage }}% 的进行中项目</div>
      </a-card>
      <a-card :bordered="false">
        <template #title>
          <div class="flex items-center">
            <span class="mr-2">延迟项目</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-red-500">{{ statistics.delayed }}</div>
        <div class="text-sm text-gray-500">{{ statistics.delayedPercentage }}% 的进行中项目</div>
      </a-card>
    </div>

    <!-- 项目执行列表 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="projectList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <!-- 项目信息 -->
          <template v-if="column.dataIndex === 'name'">
            <div>
              <a @click="viewProject(record)" class="font-medium">{{ record.name }}</a>
              <div class="text-xs text-gray-500">编号: {{ record.id }}</div>
            </div>
          </template>
          
          <!-- 进度 -->
          <template v-if="column.dataIndex === 'progress'">
            <div>
              <div class="flex justify-between items-center mb-1">
                <span>{{ record.progress }}%</span>
                <a-tag :color="getProgressStatusColor(record.progressStatus)">
                  {{ getProgressStatusName(record.progressStatus) }}
                </a-tag>
              </div>
              <a-progress 
                :percent="record.progress" 
                size="small" 
                :status="record.progressStatus === 'delayed' ? 'exception' : undefined"
              />
              <div class="text-xs text-gray-500">
                {{ record.progressUpdateTime }} 更新
              </div>
            </div>
          </template>
          
          <!-- 里程碑 -->
          <template v-if="column.dataIndex === 'milestone'">
            <div>
              <div>
                <span class="font-medium">{{ record.currentMilestone }}</span>
                <a-tag v-if="record.milestoneStatus" :color="getMilestoneStatusColor(record.milestoneStatus)">
                  {{ getMilestoneStatusName(record.milestoneStatus) }}
                </a-tag>
              </div>
              <div class="text-xs text-gray-500">
                计划完成: {{ record.milestoneDueDate }}
              </div>
            </div>
          </template>
          
          <!-- 执行情况 -->
          <template v-if="column.dataIndex === 'execution'">
            <div>
              <div>
                <span>计划: {{ record.plannedTasksCompleted }}/{{ record.totalPlannedTasks }}</span>
              </div>
              <div>
                <span>完成率: {{ Math.round((record.plannedTasksCompleted / record.totalPlannedTasks) * 100) }}%</span>
              </div>
            </div>
          </template>
          
          <!-- 资源使用 -->
          <template v-if="column.dataIndex === 'resources'">
            <div>
              <div>
                <span>人力: {{ record.resourceUsagePercentage }}%</span>
                <a-progress :percent="record.resourceUsagePercentage" size="small" />
              </div>
              <div>
                <span>预算: {{ record.budgetUsagePercentage }}%</span>
                <a-progress 
                  :percent="record.budgetUsagePercentage" 
                  size="small" 
                  :status="record.budgetUsagePercentage > 90 ? 'exception' : undefined"
                />
              </div>
            </div>
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="viewProject(record)"
              >
                详情
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="updateProgress(record)"
              >
                更新进度
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="manageRisks(record)">风险管理</a-menu-item>
                    <a-menu-item key="2" @click="manageIssues(record)">问题管理</a-menu-item>
                    <a-menu-item key="3" @click="viewMilestones(record)">里程碑详情</a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 更新进度弹窗 -->
    <a-modal
      v-model:visible="progressModalVisible"
      title="更新项目进度"
      @ok="handleProgressUpdate"
      @cancel="progressModalVisible = false"
    >
      <a-form :model="progressForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="项目" v-if="selectedProject">
          <span>{{ selectedProject.name }}</span>
        </a-form-item>
        
        <a-form-item label="当前进度" v-if="selectedProject">
          <span>{{ selectedProject.progress }}%</span>
        </a-form-item>
        
        <a-form-item label="新进度">
          <a-input-number
            v-model:value="progressForm.progress"
            :min="0"
            :max="100"
            style="width: 100%"
            addon-after="%"
          />
        </a-form-item>
        
        <a-form-item label="进度状态">
          <a-select v-model:value="progressForm.status">
            <a-select-option value="ahead">提前</a-select-option>
            <a-select-option value="on_track">正常</a-select-option>
            <a-select-option value="delayed">延迟</a-select-option>
            <a-select-option value="at_risk">风险中</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="进度说明">
          <a-textarea
            v-model:value="progressForm.comment"
            placeholder="请输入进度更新说明"
            :rows="4"
          />
        </a-form-item>
        
        <a-form-item label="相关附件">
          <a-upload
            name="file"
            :multiple="true"
            action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
            :headers="{ authorization: 'authorization-text' }"
          >
            <a-button>
              <upload-outlined /> 上传文件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  ReloadOutlined,
  SearchOutlined,
  FileExcelOutlined,
  DownOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';

// 路由
const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '项目信息',
    dataIndex: 'name',
    key: 'name',
    width: 180,
  },
  {
    title: '负责人',
    dataIndex: 'managerName',
    key: 'managerName',
    width: 100,
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 180,
  },
  {
    title: '当前里程碑',
    dataIndex: 'milestone',
    key: 'milestone',
    width: 180,
  },
  {
    title: '任务执行',
    dataIndex: 'execution',
    key: 'execution',
    width: 150,
  },
  {
    title: '资源使用',
    dataIndex: 'resources',
    key: 'resources',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
  },
];

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 统计信息
const statistics = reactive({
  inProgress: 48,
  inProgressPercentage: 38,
  onTrack: 32,
  onTrackPercentage: 67,
  atRisk: 9,
  atRiskPercentage: 19,
  delayed: 7,
  delayedPercentage: 14,
});

// 搜索表单
const searchForm = reactive({
  name: '',
  id: '',
  manager: undefined,
  progressStatus: undefined,
  completionRange: undefined as string | undefined,
});

// 项目列表数据
const projectList = ref<any[]>([]);

// 进度更新弹窗
const progressModalVisible = ref(false);
const selectedProject = ref<any>(null);
const progressForm = reactive({
  progress: 0,
  status: 'on_track',
  comment: '',
});

// 重置搜索
const resetSearch = () => {
  searchForm.name = '';
  searchForm.id = '';
  searchForm.manager = undefined;
  searchForm.progressStatus = undefined;
  searchForm.completionRange = undefined;
  handleSearch();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchProjectList();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchProjectList();
};

// 获取项目列表
const fetchProjectList = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    try {
      // 模拟数据
      const mockData = Array.from({ length: 30 }, (_, index) => {
        // 生成0-100之间的随机进度
        const progress = Math.floor(Math.random() * 101);
        
        // 根据进度确定状态
        let progressStatus = 'on_track';
        if (progress < 30) {
          progressStatus = Math.random() > 0.7 ? 'delayed' : 'on_track';
        } else if (progress < 70) {
          progressStatus = Math.random() > 0.8 ? 'at_risk' : 'on_track';
        } else {
          progressStatus = Math.random() > 0.9 ? 'delayed' : (Math.random() > 0.7 ? 'ahead' : 'on_track');
        }
        
        // 计算任务完成情况
        const totalPlannedTasks = Math.floor(Math.random() * 20) + 10;
        const plannedTasksCompleted = Math.floor(totalPlannedTasks * (progress / 100));
        
        // 资源使用情况
        const resourceUsagePercentage = Math.floor(Math.random() * 30) + 70; // 70-100%
        const budgetUsagePercentage = Math.floor(Math.random() * 20) + (progress - 10); // 根据进度的一定范围内
        
        // 里程碑信息
        const milestones = [
          '需求分析',
          '方案设计',
          '开发实施',
          '系统测试',
          '用户验收测试',
          '部署上线',
          '验收交付'
        ];
        const milestoneIndex = Math.min(Math.floor(progress / 15), 6);
        const currentMilestone = milestones[milestoneIndex];
        
        // 里程碑状态
        let milestoneStatus = 'in_progress';
        if (progressStatus === 'delayed') {
          milestoneStatus = 'delayed';
        } else if (progressStatus === 'at_risk') {
          milestoneStatus = 'at_risk';
        } else if (progressStatus === 'ahead') {
          milestoneStatus = 'ahead';
        }
        
        return {
          id: `P${String(10000 + index).padStart(5, '0')}`,
          name: `${['智能办公系统', 'ERP系统升级', '数据中心建设', '网络安全防护', 'CRM系统实施'][index % 5]}项目${index + 1}`,
          managerId: String(index % 3 + 1),
          managerName: ['张三', '李四', '王五'][index % 3],
          progress: progress,
          progressStatus: progressStatus,
          progressUpdateTime: dayjs().subtract(Math.floor(Math.random() * 7), 'day').format('YYYY-MM-DD'),
          currentMilestone: currentMilestone,
          milestoneStatus: milestoneStatus,
          milestoneDueDate: dayjs().add(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD'),
          totalPlannedTasks: totalPlannedTasks,
          plannedTasksCompleted: plannedTasksCompleted,
          resourceUsagePercentage: resourceUsagePercentage,
          budgetUsagePercentage: budgetUsagePercentage,
        };
      });
      
      // 根据搜索条件过滤
      let filteredData = [...mockData];
      
      if (searchForm.name) {
        filteredData = filteredData.filter(item => item.name.includes(searchForm.name));
      }
      
      if (searchForm.id) {
        filteredData = filteredData.filter(item => item.id.includes(searchForm.id));
      }
      
      if (searchForm.manager) {
        filteredData = filteredData.filter(item => item.managerId === searchForm.manager);
      }
      
      if (searchForm.progressStatus) {
        filteredData = filteredData.filter(item => item.progressStatus === searchForm.progressStatus);
      }
      
      if (searchForm.completionRange) {
        const [min, max] = searchForm.completionRange.split('-').map(Number);
        filteredData = filteredData.filter(item => item.progress >= min && item.progress <= max);
      }
      
      // 分页
      const start = (pagination.current - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      projectList.value = filteredData.slice(start, end);
      pagination.total = filteredData.length;
      
      // 更新统计信息
      statistics.inProgress = filteredData.length;
      statistics.onTrack = filteredData.filter(item => item.progressStatus === 'on_track').length;
      statistics.atRisk = filteredData.filter(item => item.progressStatus === 'at_risk').length;
      statistics.delayed = filteredData.filter(item => item.progressStatus === 'delayed').length;
      
      statistics.inProgressPercentage = 100;
      statistics.onTrackPercentage = Math.round((statistics.onTrack / statistics.inProgress) * 100) || 0;
      statistics.atRiskPercentage = Math.round((statistics.atRisk / statistics.inProgress) * 100) || 0;
      statistics.delayedPercentage = Math.round((statistics.delayed / statistics.inProgress) * 100) || 0;
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
      projectList.value = [];
    } finally {
      loading.value = false;
    }
  }, 500);
};

// 获取进度状态名称
const getProgressStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'ahead': '提前',
    'on_track': '正常',
    'delayed': '延迟',
    'at_risk': '风险',
  };
  return statusMap[status] || status;
};

// 获取进度状态颜色
const getProgressStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'ahead': 'green',
    'on_track': 'blue',
    'delayed': 'red',
    'at_risk': 'orange',
  };
  return colorMap[status] || 'default';
};

// 获取里程碑状态名称
const getMilestoneStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'completed': '已完成',
    'in_progress': '进行中',
    'delayed': '已延迟',
    'at_risk': '有风险',
    'ahead': '提前',
    'not_started': '未开始',
  };
  return statusMap[status] || status;
};

// 获取里程碑状态颜色
const getMilestoneStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'completed': 'green',
    'in_progress': 'blue',
    'delayed': 'red',
    'at_risk': 'orange',
    'ahead': 'green',
    'not_started': 'default',
  };
  return colorMap[status] || 'default';
};

// 查看项目
const viewProject = (record: any) => {
  router.push(`/projects/detail/${record.id}`);
};

// 更新进度
const updateProgress = (record: any) => {
  selectedProject.value = record;
  progressForm.progress = record.progress;
  progressForm.status = record.progressStatus;
  progressForm.comment = '';
  progressModalVisible.value = true;
};

// 处理进度更新
const handleProgressUpdate = () => {
  if (selectedProject.value) {
    // 模拟更新
    selectedProject.value.progress = progressForm.progress;
    selectedProject.value.progressStatus = progressForm.status;
    selectedProject.value.progressUpdateTime = dayjs().format('YYYY-MM-DD');
    
    message.success(`项目 ${selectedProject.value.name} 进度已更新为 ${progressForm.progress}%`);
    progressModalVisible.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  message.info('正在刷新数据...');
  fetchProjectList();
};

// 导出执行报告
const exportExecutionReport = () => {
  message.success('执行报告导出中，请稍候...');
};

// 风险管理
const manageRisks = (record: any) => {
  message.info(`打开风险管理: ${record.name}`);
};

// 问题管理
const manageIssues = (record: any) => {
  message.info(`打开问题管理: ${record.name}`);
};

// 查看里程碑
const viewMilestones = (record: any) => {
  message.info(`查看里程碑: ${record.name}`);
};

// 初始化
onMounted(() => {
  fetchProjectList();
});
</script>

<style scoped>
.project-execution-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 