<template>
  <div class="customer-list-container animate-fadeIn">
    <!-- 页面标题区域 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">客户管理</h2>
          <p class="text-gray-500 mt-1">管理所有客户信息和跟进状态</p>
        </div>
        <div class="flex space-x-3">
          <a-button type="primary" @click="handleAddCustomer">
            <template #icon><plus-outlined /></template>
            新增客户
          </a-button>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            导出
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6" :bordered="false">
      <div class="flex justify-between items-start flex-wrap">
        <div class="search-area w-full lg:w-auto flex-1">
          <a-input-search
            v-model:value="searchForm.keyword"
            placeholder="搜索客户名称、编号或纳税人识别号"
            enter-button
            @search="handleSearch"
            class="search-input mb-4 max-w-lg"
            allow-clear
          />
          
          <a-form 
            layout="inline" 
            :model="searchForm" 
            :class="['advanced-search', showAdvanced ? 'expanded' : '']" 
            ref="searchFormRef"
          >
            <div class="flex flex-wrap gap-3">
              <a-form-item label="客户类型" class="mb-2">
                <a-select v-model:value="searchForm.type" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option v-for="option in customerTypeOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="行业类型" class="mb-2">
                <a-select v-model:value="searchForm.industry" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option v-for="option in industryOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="负责人" class="mb-2">
                <a-select v-model:value="searchForm.owner" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option value="1">张三</a-select-option>
                  <a-select-option value="2">李四</a-select-option>
                  <a-select-option value="3">王五</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="创建日期" class="mb-2">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 240px"
                  format="YYYY-MM-DD"
                  :placeholder="['开始日期', '结束日期']"
                  allowClear />
              </a-form-item>
            </div>
            
            <div class="flex justify-end w-full mt-3">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
                <a-button type="link" @click="toggleAdvancedSearch">
                  {{ showAdvanced ? '收起筛选' : '展开筛选' }}
                  <template #icon>
                    <up-outlined v-if="showAdvanced" />
                    <down-outlined v-else />
                  </template>
                </a-button>
              </a-space>
            </div>
          </a-form>
        </div>
      </div>
    </a-card>
    
    <!-- 客户列表区域 -->
    <a-card :bordered="false">
      <div class="flex justify-end mb-4">
        <a-space>
          <a-button @click="showColumnConfig">
            <template #icon><setting-outlined /></template>
            字段配置
          </a-button>
          <a-button @click="fetchCustomerList">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
      <a-table
        :columns="visibleColumns"
        :data-source="customerList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 自定义列内容 -->
        <template #bodyCell="{ column, record }">
          <!-- 客户名称列 -->
          <template v-if="column.dataIndex === 'name'">
            <div class="flex items-center">
              <a-avatar :style="{ backgroundColor: getAvatarColor(record.name) }">
                {{ record.name.charAt(0) }}
              </a-avatar>
              <div class="ml-2">
                <a @click="() => viewCustomer(record)" class="font-medium text-blue-600 hover:text-blue-800">{{ record.name }}</a>
              </div>
            </div>
          </template>
          
          <!-- 客户类型列 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          
          <!-- 行业列 -->
          <template v-if="column.dataIndex === 'industry'">
            <a-tag :color="getIndustryColor(record.industry)">
            {{ getIndustryName(record.industry) }}
            </a-tag>
          </template>
          
          <!-- 客户地区列 -->
          <template v-if="column.dataIndex === 'region'">
            <div v-if="record.province || record.city">
              {{ getProvinceName(record.province) }}{{ record.city ? ' - ' + getCityName(record.province, record.city) : '' }}
            </div>
            <div v-else>-</div>
          </template>
          
          <!-- 跟进状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          
          <!-- 最近联系列 -->
          <template v-if="column.dataIndex === 'lastContactTime'">
            <div>{{ formatDate(record.lastContactTime) }}</div>
            <div v-if="isRecentContact(record.lastContactTime)" class="text-xs text-success-600">
              <check-circle-outlined /> 近期已跟进
            </div>
            <div v-else-if="needContact(record.lastContactTime)" class="text-xs text-error-600">
              <warning-outlined /> 需要跟进
            </div>
          </template>
          
          <!-- 负责人列 -->
          <template v-if="column.dataIndex === 'ownerName'">
            <div class="flex items-center">
              <a-avatar :size="24" class="mr-1">
                <template #icon><user-outlined /></template>
              </a-avatar>
              {{ record.ownerName }}
            </div>
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="() => editCustomer(record)">
                编辑
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="() => deleteCustomer(record)">
                      <delete-outlined /> 删除客户
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 字段配置弹框 -->
    <a-modal
      v-model:visible="columnConfigVisible"
      title="字段配置"
      @ok="handleColumnConfigSave"
      @cancel="handleColumnConfigCancel"
      width="600px"
    >
      <div class="column-config-content">
        <div class="mb-4">
          <a-alert
            message="拖拽调整字段顺序，勾选控制字段显示"
            type="info"
            show-icon
            banner
          />
        </div>
        
        <div class="column-list">
          <draggable
            v-model="configColumns"
            item-key="key"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <template #item="{ element }">
              <div class="column-item">
                <div class="column-item-content">
                  <div class="drag-handle">
                    <drag-outlined />
                  </div>
                  <a-checkbox 
                    v-model:checked="element.visible"
                    :disabled="element.key === 'action'"
                  >
                    {{ element.title }}
                  </a-checkbox>
                  <div class="column-width">
                    <span class="width-label">宽度:</span>
                    <a-input-number
                      v-model:value="element.width"
                      :min="80"
                      :max="500"
                      size="small"
                      style="width: 80px"
                    />
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
        
        <div class="mt-4">
          <a-space>
            <a-button @click="resetColumnConfig">重置默认</a-button>
            <a-button @click="saveColumnTemplate">保存模板</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 新增客户弹框 -->
    <a-drawer
      v-model:visible="customerModalVisible"
      :title="isEditCustomer ? '编辑客户' : '新增客户'"
      width="700px"
      placement="right"
      @close="handleCustomerModalCancel"
    >
      <a-form
        :model="customerForm"
        :rules="customerRules"
        ref="customerFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 客户信息 -->
        <a-divider>客户信息</a-divider>
        
        <a-form-item label="客户名称" name="name">
          <a-input v-model:value="customerForm.name" placeholder="请输入客户公司全称" />
        </a-form-item>
        
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="customerForm.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="客户类型" name="type">
          <a-select v-model:value="customerForm.type" placeholder="请选择客户类型">
            <a-select-option v-for="option in customerTypeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="所属行业" name="industry">
          <a-select v-model:value="customerForm.industry" placeholder="请选择所属行业">
            <a-select-option v-for="option in industryOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="客户地区" name="region">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-select 
                v-model:value="customerForm.province" 
                placeholder="请选择省份"
                @change="handleProvinceChange"
              >
                <a-select-option v-for="option in provinceOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-select 
                v-model:value="customerForm.city" 
                placeholder="请选择城市"
                :disabled="!customerForm.province"
              >
                <a-select-option v-for="option in currentCityOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-item>
        
        <a-form-item label="客户负责人" name="owner">
          <a-select v-model:value="customerForm.owner" placeholder="请选择负责人">
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 联系信息 -->
        <a-divider>联系信息</a-divider>
        
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="customerForm.contact" placeholder="请输入主要联系人姓名" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="customerForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="电子邮箱" name="email">
          <a-input v-model:value="customerForm.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        
        <a-form-item label="联系地址" name="address">
          <a-textarea v-model:value="customerForm.address" placeholder="请输入详细地址" :rows="3" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="customerForm.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
        
        <!-- 开票信息 -->
        <a-divider>开票信息</a-divider>
        
        <a-form-item label="开票公司名称" name="invoiceCompanyName">
          <a-input v-model:value="customerForm.invoiceCompanyName" placeholder="请输入开票公司名称" />
        </a-form-item>
        
        <a-form-item label="开票纳税人识别号" name="invoiceTaxId">
          <a-input v-model:value="customerForm.invoiceTaxId" placeholder="请输入开票纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="开户银行" name="invoiceBank">
          <a-input v-model:value="customerForm.invoiceBank" placeholder="请输入开户银行" />
        </a-form-item>
        
        <a-form-item label="银行账号" name="invoiceBankAccount">
          <a-input v-model:value="customerForm.invoiceBankAccount" placeholder="请输入银行账号" />
        </a-form-item>
        
        <a-form-item label="开票地址" name="invoiceAddress">
          <a-input v-model:value="customerForm.invoiceAddress" placeholder="请输入开票地址" />
        </a-form-item>
        
        <a-form-item label="开票电话" name="invoicePhone">
          <a-input v-model:value="customerForm.invoicePhone" placeholder="请输入开票电话" />
        </a-form-item>
        
        <a-form-item label="开票备注" name="invoiceRemark">
          <a-textarea v-model:value="customerForm.invoiceRemark" placeholder="请输入开票备注信息" :rows="2" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="text-right">
          <a-space>
            <a-button @click="handleCustomerModalCancel">取消</a-button>
            <a-button type="primary" @click="handleCustomerSubmit" :loading="customerSubmitting">
              {{ isEditCustomer ? '更新' : '保存' }}
            </a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>

    <!-- 添加跟进记录弹窗 -->
    <a-modal
      v-model:visible="trackModalVisible"
      title="添加跟进记录"
      @ok="handleTrackSubmit"
      @cancel="trackModalVisible = false"
    >
      <a-form
        :model="trackForm"
        :rules="trackRules"
        ref="trackFormRef"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 19 }"
      >
        <a-form-item label="客户名称">
          <span class="text-gray-600">{{ currentCustomer?.name }}</span>
        </a-form-item>
        
        <a-form-item label="跟进内容" name="content">
          <a-textarea v-model:value="trackForm.content" placeholder="请输入跟进内容" :rows="4" />
        </a-form-item>
        
        <a-form-item label="跟进方式" name="method">
          <a-select v-model:value="trackForm.method" placeholder="请选择跟进方式">
            <a-select-option value="phone">电话</a-select-option>
            <a-select-option value="visit">拜访</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="meeting">会议</a-select-option>
            <a-select-option value="wechat">微信</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="下次联系时间" name="nextContactTime">
          <a-date-picker v-model:value="trackForm.nextContactTime" style="width: 100%" />
        </a-form-item>
        
        <a-form-item label="更新状态" name="status">
          <a-select v-model:value="trackForm.status" placeholder="请选择客户状态">
            <a-select-option value="">保持不变</a-select-option>
            <a-select-option value="following">跟进中</a-select-option>
            <a-select-option value="negotiating">商务谈判</a-select-option>
            <a-select-option value="signed">已签约</a-select-option>
            <a-select-option value="lost">已流失</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import draggable from 'vuedraggable';
import dayjs from 'dayjs';
import { useCustomerStore } from '@/stores/customer';
import { CustomerType, CustomerStatus, IndustryType, type CustomerSource } from '@/types/customer';
import { 
  getCustomerTypeOptions, 
  getIndustryTypeOptions, 
  getCustomerTypeLabel, 
  getIndustryTypeLabel,
  PROVINCE_OPTIONS, 
  CITY_OPTIONS 
} from '@/constants/customer';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  UserOutlined,
  RiseOutlined,
  FallOutlined,
  FundOutlined,
  AuditOutlined,
  FileTextOutlined,
  DeleteOutlined,
  DownOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  UpOutlined,
  SettingOutlined,
  DragOutlined
} from '@ant-design/icons-vue';

// 路由实例
const router = useRouter();

// 客户数据仓库
const customerStore = useCustomerStore();

// 表格列定义
const columns = [
  {
    title: '客户编号',
    dataIndex: 'code',
    key: 'code',
    width: 140,
  },
  {
    title: '客户名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true,
  },
  {
    title: '客户类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    filters: [
      { text: '企业', value: 'C' },
      { text: '政府', value: 'G' },
    ],
  },
  {
    title: '纳税人识别号',
    dataIndex: 'taxId',
    key: 'taxId',
    width: 180,
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    key: 'contact',
    width: 120,
  },
  {
    title: '联系方式',
    dataIndex: 'phone',
    key: 'phone',
    width: 140,
  },
  {
    title: '行业',
    dataIndex: 'industry',
    key: 'industry',
    width: 140,
    filters: [
      { text: '政府机构', value: 'I01' },
      { text: '金融服务', value: 'I02' },
      { text: '信息技术/互联网', value: 'I03' },
      { text: '制造与工业', value: 'I04' },
      { text: '零售与消费品', value: 'I05' },
      { text: '能源与公用事业', value: 'I06' },
      { text: '交通与物流', value: 'I07' },
      { text: '医疗与健康', value: 'I08' },
      { text: '教育与科研', value: 'I09' },
      { text: '房地产与建筑', value: 'I10' },
      { text: '专业服务', value: 'I11' },
      { text: '农林牧渔', value: 'I12' },
      { text: '其他/未分类', value: 'I13' },
    ],
  },
  {
    title: '客户地区',
    dataIndex: 'region',
    key: 'region',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 150,
  },
];

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: undefined,
  industry: undefined,
  owner: undefined,
  dateRange: [],
});

// 客户列表
const customerList = ref<any[]>([]);

// 字段配置相关状态
const columnConfigVisible = ref(false);
const configColumns = ref([]);

// 初始化配置列
const initConfigColumns = () => {
  configColumns.value = columns.map(col => ({
    ...col,
    visible: true
  }));
};

// 可见列计算属性
const visibleColumns = computed(() => {
  return configColumns.value.filter(col => col.visible);
});

// 跟进记录相关
const trackModalVisible = ref(false);
const trackFormRef = ref();
const currentCustomer = ref<any>(null);

// 跟进表单
const trackForm = reactive({
  customerId: '',
  content: '',
  method: 'phone',
  nextContactTime: null as any,
  status: '',
});

// 表单验证规则
const trackRules = {
  content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
  method: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
  nextContactTime: [{ required: true, message: '请选择下次联系时间', trigger: 'change', type: 'object' }],
};

// 客户表单相关
const customerModalVisible = ref(false);
const customerFormRef = ref();
const isEditCustomer = ref(false);
const customerSubmitting = ref(false);

// 客户表单数据
const customerForm = reactive({
  id: '',
  name: '',
  taxId: '',
  type: 'C',
  industry: 'I13',
  owner: '1',
  province: '',
  city: '',
  contact: '',
  phone: '',
  email: '',
  address: '',
  remark: '',
  // 开票信息
  invoiceCompanyName: '',
  invoiceTaxId: '',
  invoiceBank: '',
  invoiceBankAccount: '',
  invoiceAddress: '',
  invoicePhone: '',
  invoiceRemark: '',
});

// 选项数据
const customerTypeOptions = ref(getCustomerTypeOptions());
const industryOptions = ref(getIndustryTypeOptions());
const provinceOptions = ref(PROVINCE_OPTIONS);
const currentCityOptions = ref<Array<{ label: string; value: string }>>([]);

// 处理省份变化
const handleProvinceChange = (province: string) => {
  customerForm.city = '';
  currentCityOptions.value = CITY_OPTIONS[province] || [];
};

// 表单验证规则
const customerRules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  // 开票信息验证（可选）
  invoiceCompanyName: [{ required: false, message: '请输入开票公司名称', trigger: 'blur' }],
  invoiceTaxId: [{ required: false, message: '请输入开票纳税人识别号', trigger: 'blur' }],
};

// 重置搜索条件
const resetSearch = () => {
  // 使用类型安全的方式重置表单
  searchForm.keyword = '';
  searchForm.type = undefined;
  searchForm.industry = undefined;
  searchForm.owner = undefined;
  searchForm.dateRange = [];
  
  handleSearch();
};

// 执行搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchCustomerList();
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchCustomerList();
};

// 获取客户列表
const fetchCustomerList = async () => {
  loading.value = true;
  
  try {
    // 准备查询参数
    const params = {
      keyword: searchForm.keyword,
      type: searchForm.type,
      industry: searchForm.industry,
      owner: searchForm.owner,
      dateRange: searchForm.dateRange
    };
    
    // 调用store获取客户列表
    const result = await customerStore.fetchCustomers(params);
    
    // 更新表格数据和分页信息
    customerList.value = result.data;
    pagination.total = result.total;
  } catch (error) {
    console.error('获取客户列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 字段配置相关方法
const showColumnConfig = () => {
  columnConfigVisible.value = true;
};

const handleColumnConfigSave = () => {
  // 保存配置到本地存储
  localStorage.setItem('customer-columns-config', JSON.stringify(configColumns.value));
  message.success('字段配置已保存');
  columnConfigVisible.value = false;
};

const handleColumnConfigCancel = () => {
  // 重新加载配置
  loadColumnConfig();
  columnConfigVisible.value = false;
};

const resetColumnConfig = () => {
  initConfigColumns();
  message.success('已重置为默认配置');
};

const saveColumnTemplate = () => {
  // 保存为模板
  localStorage.setItem('customer-columns-template', JSON.stringify(configColumns.value));
  message.success('已保存为模板');
};

const loadColumnConfig = () => {
  const saved = localStorage.getItem('customer-columns-config');
  if (saved) {
    try {
      const savedConfig = JSON.parse(saved);
      configColumns.value = columns.map(col => {
        const savedCol = savedConfig.find(saved => saved.key === col.key);
        return {
          ...col,
          visible: savedCol ? savedCol.visible : true,
          width: savedCol ? savedCol.width : col.width
        };
      });
    } catch (error) {
      console.error('加载字段配置失败:', error);
      initConfigColumns();
    }
  } else {
    initConfigColumns();
  }
};

const onDragStart = () => {
  // 拖拽开始
};

const onDragEnd = () => {
  // 拖拽结束，可以在这里保存顺序
};

// 初始化
onMounted(() => {
  // 初始化字段配置
  loadColumnConfig();
  fetchCustomerList();
});

// 获取头像颜色
const getAvatarColor = (name: string) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#f56a00', '#7265e6'];
  let total = 0;
  for (let i = 0; i < name.length; i++) {
    total += name.charCodeAt(i);
  }
  return colors[total % colors.length];
};

// 获取客户类型名称
const getTypeName = (type: string) => {
  return getCustomerTypeLabel(type);
};

// 获取客户类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'C': 'blue',   // 企业
    'G': 'green',  // 政府
  };
  return colorMap[type] || 'default';
};

// 获取行业名称
const getIndustryName = (industry: string) => {
  return getIndustryTypeLabel(industry);
};

// 获取行业颜色
const getIndustryColor = (industry: string) => {
  const colorMap: Record<string, string> = {
    'I01': 'red',      // 政府机构
    'I02': 'gold',     // 金融服务
    'I03': 'blue',     // 信息技术/互联网
    'I04': 'orange',   // 制造与工业
    'I05': 'cyan',     // 零售与消费品
    'I06': 'purple',   // 能源与公用事业
    'I07': 'geekblue', // 交通与物流
    'I08': 'green',    // 医疗与健康
    'I09': 'lime',     // 教育与科研
    'I10': 'volcano',  // 房地产与建筑
    'I11': 'magenta',  // 专业服务
    'I12': 'brown',    // 农林牧渔
    'I13': 'default',  // 其他/未分类
  };
  return colorMap[industry] || 'default';
};

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    new: '新建',
    following: '跟进中',
    negotiating: '商务谈判',
    signed: '已签约',
    lost: '已流失',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    new: 'blue',
    following: 'cyan',
    negotiating: 'orange',
    signed: 'green',
    lost: 'red',
  };
  return colorMap[status] || 'default';
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return dayjs(dateString).format('YYYY-MM-DD');
};

// 判断是否近期联系过（7天内）
const isRecentContact = (dateString: string) => {
  if (!dateString) return false;
  const contactDate = dayjs(dateString);
  const now = dayjs();
  return now.diff(contactDate, 'day') <= 7;
};

// 判断是否需要跟进（超过30天未联系）
const needContact = (dateString: string) => {
  if (!dateString) return true;
  const contactDate = dayjs(dateString);
  const now = dayjs();
  return now.diff(contactDate, 'day') > 30;
};

// 处理添加客户
const handleAddCustomer = () => {
  isEditCustomer.value = false;
  resetCustomerForm();
  customerModalVisible.value = true;
};

// 重置客户表单
const resetCustomerForm = () => {
  customerForm.id = '';
  customerForm.name = '';
  customerForm.taxId = '';
  customerForm.type = 'C';
  customerForm.industry = 'I13';
  customerForm.owner = '1';
  customerForm.province = '';
  customerForm.city = '';
  customerForm.contact = '';
  customerForm.phone = '';
  customerForm.email = '';
  customerForm.address = '';
  customerForm.remark = '';
  // 重置开票信息
  customerForm.invoiceCompanyName = '';
  customerForm.invoiceTaxId = '';
  customerForm.invoiceBank = '';
  customerForm.invoiceBankAccount = '';
  customerForm.invoiceAddress = '';
  customerForm.invoicePhone = '';
  customerForm.invoiceRemark = '';
};

// 客户弹框取消
const handleCustomerModalCancel = () => {
  customerModalVisible.value = false;
  resetCustomerForm();
};

// 客户表单提交
const handleCustomerSubmit = async () => {
  try {
    await customerFormRef.value.validate();
    customerSubmitting.value = true;
    
    const customerData = {
      name: customerForm.name,
      taxId: customerForm.taxId,
      type: customerForm.type as CustomerType,
      industry: customerForm.industry as IndustryType,
      owner: customerForm.owner,
      province: customerForm.province,
      city: customerForm.city,
      contact: customerForm.contact,
      phone: customerForm.phone,
      email: customerForm.email,
      address: customerForm.address,
      remark: customerForm.remark,
      // 开票信息
      invoiceCompanyName: customerForm.invoiceCompanyName,
      invoiceTaxId: customerForm.invoiceTaxId,
      invoiceBank: customerForm.invoiceBank,
      invoiceBankAccount: customerForm.invoiceBankAccount,
      invoiceAddress: customerForm.invoiceAddress,
      invoicePhone: customerForm.invoicePhone,
      invoiceRemark: customerForm.invoiceRemark,
    };
    
    let result;
    if (isEditCustomer.value) {
      result = await customerStore.updateCustomer(customerForm.id, customerData);
    } else {
      result = await customerStore.createCustomer(customerData);
    }
    
    if (result) {
      message.success(`客户${isEditCustomer.value ? '更新' : '创建'}成功`);
      customerModalVisible.value = false;
      fetchCustomerList();
    }
  } catch (error) {
    console.error('保存客户信息失败', error);
  } finally {
    customerSubmitting.value = false;
  }
};

// 处理导出
const handleExport = () => {
  message.success('客户数据导出成功');
};

// 查看客户详情
const viewCustomer = (record: any) => {
  router.push(`/customer/detail/${record.id}`);
};

// 编辑客户
const editCustomer = (record: any) => {
  isEditCustomer.value = true;
  
  // 填充表单数据
  customerForm.id = record.id;
  customerForm.name = record.name;
  customerForm.taxId = record.taxId;
  customerForm.type = record.type;
  customerForm.industry = record.industry;
  customerForm.owner = record.owner;
  customerForm.province = record.province || '';
  customerForm.city = record.city || '';
  customerForm.contact = record.contact;
  customerForm.phone = record.phone;
  customerForm.email = record.email;
  customerForm.address = record.address;
  customerForm.remark = record.remark;
  // 填充开票信息
  customerForm.invoiceCompanyName = record.invoiceCompanyName || '';
  customerForm.invoiceTaxId = record.invoiceTaxId || '';
  customerForm.invoiceBank = record.invoiceBank || '';
  customerForm.invoiceBankAccount = record.invoiceBankAccount || '';
  customerForm.invoiceAddress = record.invoiceAddress || '';
  customerForm.invoicePhone = record.invoicePhone || '';
  customerForm.invoiceRemark = record.invoiceRemark || '';
  
  // 如果有省份，加载对应的城市选项
  if (record.province) {
    handleProvinceChange(record.province);
  }
  
  customerModalVisible.value = true;
};

// 添加销售机会
const addOpportunity = (record: any) => {
  router.push('/crm/opportunities');
};

// 添加跟进记录
const trackCustomer = (record: any) => {
  currentCustomer.value = record;
  trackForm.customerId = record.id;
  trackForm.content = '';
  trackForm.method = 'phone';
  trackForm.nextContactTime = dayjs().add(7, 'day').valueOf();
  trackForm.status = '';
  
  trackModalVisible.value = true;
};

// 处理添加跟进记录提交
const handleTrackSubmit = async () => {
  // 表单验证
  trackFormRef.value.validate().then(async () => {
    if (currentCustomer.value) {
      // 使用store添加联系记录
      await customerStore.createCustomerContact({
        customerId: currentCustomer.value.id,
        content: trackForm.content,
        method: trackForm.method,
        nextContactTime: trackForm.nextContactTime ? dayjs(trackForm.nextContactTime).format('YYYY-MM-DD') : '',
        status: trackForm.status
      });
      
      // 刷新客户列表
      fetchCustomerList();
      trackModalVisible.value = false;
    }
  }).catch((err: Error) => {
    console.log('表单验证失败', err);
  });
};

// 添加合同
const addContract = (record: any) => {
  router.push('/contract/form');
};

// 删除客户
const deleteCustomer = (record: any) => {
  Modal.confirm({
    title: '删除确认',
    content: `确定要删除客户"${record.name}"吗？此操作不可恢复。`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await customerStore.deleteCustomer(record.id);
      if (success) {
      fetchCustomerList();
      }
    }
  });
};

// 切换高级搜索
const showAdvanced = ref(false);
const toggleAdvancedSearch = () => {
  showAdvanced.value = !showAdvanced.value;
};

// 获取省份名称
const getProvinceName = (province: string) => {
  const option = provinceOptions.value.find(item => item.value === province);
  return option?.label || province;
};

// 获取城市名称
const getCityName = (province: string, city: string) => {
  const cityOptions = CITY_OPTIONS[province] || [];
  const option = cityOptions.find(item => item.value === city);
  return option?.label || city;
};
</script>

<style scoped>
.customer-list-container {
  animation: fadeIn 0.5s ease-in-out;
}

.stats-cards .ant-card {
  margin-bottom: 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.search-input {
  width: 100%;
}

.advanced-search {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.advanced-search.expanded {
  max-height: 1000px;
}

@media (max-width: 768px) {
  .a-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* 字段配置样式 */
.column-config-content {
  .column-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 8px;
  }
  
  .column-item {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .column-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 4px;
    cursor: move;
    
    &:hover {
      background: #f0f0f0;
    }
  }
  
  .drag-handle {
    margin-right: 8px;
    color: #999;
    cursor: move;
  }
  
  .column-width {
    margin-left: auto;
    display: flex;
    align-items: center;
    
    .width-label {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
