<template>
  <div class="opportunity-container animate-fadeIn">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold">销售机会管理</h2>
        <a-button type="primary" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          新增销售机会
        </a-button>
      </div>
      <p class="text-gray-500 mt-1">管理潜在销售机会，跟进客户需求和商机转化</p>
    </div>
    
    <!-- 搜索和筛选 -->
    <a-card class="mb-6" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="客户名称">
          <a-input v-model:value="searchForm.customerName" placeholder="请输入客户名称" allowClear />
        </a-form-item>
        
        <a-form-item label="商机名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入商机名称" allowClear />
        </a-form-item>
        
        <a-form-item label="阶段">
          <a-select
            v-model:value="searchForm.stage"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="initial">初步接洽</a-select-option>
            <a-select-option value="qualified">需求确认</a-select-option>
            <a-select-option value="proposal">方案制定</a-select-option>
            <a-select-option value="negotiation">商务谈判</a-select-option>
            <a-select-option value="closed-won">成功签约</a-select-option>
            <a-select-option value="closed-lost">机会流失</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="负责人">
          <a-select
            v-model:value="searchForm.owner"
            style="width: 120px"
            placeholder="请选择"
            allowClear
          >
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><search-outlined /></template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <template #icon><reload-outlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 销售机会列表 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="opportunityList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 客户信息 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'customerName'">
            <div class="flex items-center">
              <a-avatar :style="{ backgroundColor: getAvatarColor(record.customerName) }">
                {{ record.customerName.charAt(0) }}
              </a-avatar>
              <div class="ml-2">
                <div class="font-medium">{{ record.customerName }}</div>
                <div class="text-gray-500 text-xs">{{ record.customerContact }}</div>
                <div class="text-gray-500 text-xs">客户ID: {{ record.customerId }}</div>
              </div>
            </div>
          </template>
          
          <!-- 商机名称 -->
          <template v-if="column.dataIndex === 'name'">
            <div>
              <a @click="viewOpportunity(record)">{{ record.name }}</a>
              <div class="text-gray-500 text-xs">创建时间: {{ formatDate(record.createTime) }}</div>
            </div>
          </template>
          
          <!-- 预计金额 -->
          <template v-if="column.dataIndex === 'expectedAmount'">
            <span class="text-primary-600 font-medium">¥{{ formatNumber(record.expectedAmount) }}</span>
          </template>
          
          <!-- 阶段 -->
          <template v-if="column.dataIndex === 'stage'">
            <a-progress 
              :percent="getStagePercent(record.stage)" 
              :status="getStageStatus(record.stage)"
              :stroke-color="getStageColor(record.stage)"
              size="small"
            />
            <div class="mt-1 text-xs">
              <a-tag :color="getStageColor(record.stage)">
                {{ getStageName(record.stage) }}
              </a-tag>
            </div>
          </template>
          
          <!-- 可能性 -->
          <template v-if="column.dataIndex === 'probability'">
            <div>{{ record.probability }}%</div>
          </template>
          
          <!-- 负责人 -->
          <template v-if="column.dataIndex === 'ownerName'">
            <a-avatar :size="24" class="mr-1">
              <template #icon><user-outlined /></template>
            </a-avatar>
            {{ record.ownerName }}
          </template>
          
          <!-- 下次联系 -->
          <template v-if="column.dataIndex === 'nextContactTime'">
            <div>{{ formatDate(record.nextContactTime, 'YYYY-MM-DD') }}</div>
            <div v-if="isOverdue(record.nextContactTime)" class="text-red-500 text-xs">已逾期</div>
            <div v-else-if="isToday(record.nextContactTime)" class="text-green-500 text-xs">今天</div>
          </template>
          
          <!-- 操作 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleTrack(record)">
                <template #icon><audit-outlined /></template>
                跟进
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <template #icon><edit-outlined /></template>
                编辑
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="handleUpdateStage(record, 'initial')">
                      初步接洽
                    </a-menu-item>
                    <a-menu-item key="2" @click="handleUpdateStage(record, 'qualified')">
                      需求确认
                    </a-menu-item>
                    <a-menu-item key="3" @click="handleUpdateStage(record, 'proposal')">
                      方案制定
                    </a-menu-item>
                    <a-menu-item key="4" @click="handleUpdateStage(record, 'negotiation')">
                      商务谈判
                    </a-menu-item>
                    <a-menu-item key="5" @click="handleUpdateStage(record, 'closed-won')">
                      成功签约
                    </a-menu-item>
                    <a-menu-item key="6" @click="handleUpdateStage(record, 'closed-lost')">
                      机会流失
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 新增/编辑销售机会弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑销售机会' : '新增销售机会'"
      width="700px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <a-divider>基本信息</a-divider>
        
        <a-form-item label="客户名称" name="customerId">
          <a-select
            v-model:value="formState.customerId"
            placeholder="请选择客户"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="item in customerOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="商机名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入商机名称" />
        </a-form-item>
        
        <a-form-item label="商机来源" name="source">
          <a-select v-model:value="formState.source" placeholder="请选择商机来源">
            <a-select-option value="website">官网</a-select-option>
            <a-select-option value="referral">转介绍</a-select-option>
            <a-select-option value="exhibition">展会</a-select-option>
            <a-select-option value="advertisement">广告</a-select-option>
            <a-select-option value="social">社交媒体</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="预计金额" name="expectedAmount">
          <a-input-number
            v-model:value="formState.expectedAmount"
            placeholder="请输入预计金额"
            style="width: 100%"
            :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\¥\s?|(,*)/g, '')"
          />
        </a-form-item>
        
        <a-form-item label="当前阶段" name="stage">
          <a-select v-model:value="formState.stage" placeholder="请选择当前阶段">
            <a-select-option value="initial">初步接洽</a-select-option>
            <a-select-option value="qualified">需求确认</a-select-option>
            <a-select-option value="proposal">方案制定</a-select-option>
            <a-select-option value="negotiation">商务谈判</a-select-option>
            <a-select-option value="closed-won">成功签约</a-select-option>
            <a-select-option value="closed-lost">机会流失</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="可能性" name="probability">
          <a-slider v-model:value="formState.probability" :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }" />
        </a-form-item>
        
        <a-form-item label="预计成交日期" name="expectedCloseDate">
          <a-date-picker v-model:value="formState.expectedCloseDate" style="width: 100%" />
        </a-form-item>
        
        <!-- 详细信息 -->
        <a-divider>详细信息</a-divider>
        
        <a-form-item label="负责人" name="owner">
          <a-select v-model:value="formState.owner" placeholder="请选择负责人">
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="竞争对手" name="competitor">
          <a-input v-model:value="formState.competitor" placeholder="请输入竞争对手" />
        </a-form-item>
        
        <a-form-item label="下一步计划" name="nextStep">
          <a-textarea v-model:value="formState.nextStep" placeholder="请输入下一步计划" :rows="2" />
        </a-form-item>
        
        <a-form-item label="下次联系时间" name="nextContactTime">
          <a-date-picker v-model:value="formState.nextContactTime" style="width: 100%" showTime />
        </a-form-item>
        
        <a-form-item label="需求描述" name="description">
          <a-textarea v-model:value="formState.description" placeholder="请输入需求描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 跟进记录弹窗 -->
    <a-modal
      v-model:visible="trackModalVisible"
      title="跟进记录"
      width="700px"
      @cancel="trackModalVisible = false"
    >
      <!-- 跟进记录列表 -->
      <div class="mb-4">
        <div class="flex justify-between items-center mb-3">
          <h3 class="text-lg font-medium">历史跟进记录</h3>
          <a-button type="primary" size="small" @click="showTrackForm">
            <template #icon><plus-outlined /></template>
            添加记录
          </a-button>
        </div>
        <a-table
          :columns="trackColumns"
          :data-source="trackRecords"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'method'">
              {{ getTrackMethodName(record.method) }}
            </template>
            <template v-if="column.dataIndex === 'createTime'">
              {{ formatDate(record.createTime) }}
            </template>
          </template>
        </a-table>
      </div>
      
      <!-- 添加跟进表单 -->
      <a-form
        v-if="showTrackFormFlag"
        :model="trackForm"
        :rules="trackRules"
        ref="trackFormRef"
      >
        <a-form-item label="跟进内容" name="content">
          <a-textarea v-model:value="trackForm.content" placeholder="请输入跟进内容" :rows="4" />
        </a-form-item>
        
        <a-form-item label="跟进方式" name="method">
          <a-select v-model:value="trackForm.method" placeholder="请选择跟进方式">
            <a-select-option value="phone">电话</a-select-option>
            <a-select-option value="visit">拜访</a-select-option>
            <a-select-option value="email">邮件</a-select-option>
            <a-select-option value="meeting">会议</a-select-option>
            <a-select-option value="wechat">微信</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="下次联系时间" name="nextContactTime">
          <a-date-picker v-model:value="trackForm.nextContactTime" style="width: 100%" showTime />
        </a-form-item>
        
        <a-form-item label="更新阶段" name="stage">
          <a-select v-model:value="trackForm.stage" placeholder="请选择当前阶段">
            <a-select-option value="">保持不变</a-select-option>
            <a-select-option value="initial">初步接洽</a-select-option>
            <a-select-option value="qualified">需求确认</a-select-option>
            <a-select-option value="proposal">方案制定</a-select-option>
            <a-select-option value="negotiation">商务谈判</a-select-option>
            <a-select-option value="closed-won">成功签约</a-select-option>
            <a-select-option value="closed-lost">机会流失</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" @click="handleTrackSubmit">保存</a-button>
          <a-button style="margin-left: 8px" @click="cancelTrackForm">取消</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  AuditOutlined,
  UserOutlined,
  DownOutlined
} from '@ant-design/icons-vue';

// 路由
const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '客户信息',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 200,
  },
  {
    title: '商机名称',
    dataIndex: 'name',
    key: 'name',
    width: 220,
    sorter: true,
  },
  {
    title: '预计金额',
    dataIndex: 'expectedAmount',
    key: 'expectedAmount',
    width: 120,
    sorter: true,
  },
  {
    title: '阶段',
    dataIndex: 'stage',
    key: 'stage',
    width: 180,
    filters: [
      { text: '初步接洽', value: 'initial' },
      { text: '需求确认', value: 'qualified' },
      { text: '方案制定', value: 'proposal' },
      { text: '商务谈判', value: 'negotiation' },
      { text: '成功签约', value: 'closed-won' },
      { text: '机会流失', value: 'closed-lost' },
    ],
  },
  {
    title: '可能性',
    dataIndex: 'probability',
    key: 'probability',
    width: 100,
    sorter: true,
  },
  {
    title: '负责人',
    dataIndex: 'ownerName',
    key: 'ownerName',
    width: 120,
  },
  {
    title: '下次联系',
    dataIndex: 'nextContactTime',
    key: 'nextContactTime',
    width: 120,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 180,
  },
];

// 加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  customerName: '',
  name: '',
  stage: undefined,
  owner: undefined,
});

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  handleSearch();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchOpportunityList();
};

// 表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchOpportunityList();
};

// 销售机会列表数据
const opportunityList = ref<any[]>([]);

// 客户选项
const customerOptions = ref([
  { label: '北京科技有限公司', value: '1', id: 'C100001' },
  { label: '上海数字科技有限公司', value: '2', id: 'C100002' },
  { label: '广州智能科技有限公司', value: '3', id: 'C100003' },
  { label: '深圳创新科技有限公司', value: '4', id: 'C100004' },
  { label: '杭州网络科技有限公司', value: '5', id: 'C100005' },
]);

// 获取销售机会列表
const fetchOpportunityList = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const mockData = Array.from({ length: 50 }, (_, index) => ({
      id: index + 1,
      customerId: `C${String(100000 + index).padStart(6, '0')}`,
      customerName: ['北京科技有限公司', '上海数字科技有限公司', '广州智能科技有限公司', '深圳创新科技有限公司', '杭州网络科技有限公司'][Math.floor(Math.random() * 5)],
      customerContact: ['张经理', '李总', '王董', '赵主任', '钱经理'][Math.floor(Math.random() * 5)],
      name: ['ERP系统升级项目', 'CRM系统实施项目', '数据中心建设项目', '网络安全解决方案', '智能办公系统', '企业数字化转型咨询'][Math.floor(Math.random() * 6)] + (index + 1),
      source: ['website', 'referral', 'exhibition', 'advertisement', 'social', 'other'][Math.floor(Math.random() * 6)],
      expectedAmount: Math.floor(Math.random() * 500 + 1) * 10000,
      stage: ['initial', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost'][Math.floor(Math.random() * 6)],
      probability: [10, 30, 50, 70, 90, 100][Math.floor(Math.random() * 6)],
      expectedCloseDate: dayjs().add(Math.floor(Math.random() * 90), 'day').format('YYYY-MM-DD'),
      ownerName: ['张三', '李四', '王五'][Math.floor(Math.random() * 3)],
      owner: String(Math.floor(Math.random() * 3 + 1)),
      competitor: ['竞争对手A', '竞争对手B', '竞争对手C', ''][Math.floor(Math.random() * 4)],
      nextStep: ['安排演示', '提供报价', '需求分析', '方案讨论', '合同谈判'][Math.floor(Math.random() * 5)],
      nextContactTime: dayjs().add(Math.floor(Math.random() * 14 - 7), 'day').format('YYYY-MM-DD HH:mm:ss'),
      description: '这是一个潜在的销售机会，客户对我们的产品/服务表现出浓厚兴趣。',
      remark: '需要准备详细的方案和报价。',
      createTime: dayjs().subtract(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs().subtract(Math.floor(Math.random() * 7), 'day').format('YYYY-MM-DD HH:mm:ss'),
    }));
    
    // 过滤数据
    let filteredData = [...mockData];
    if (searchForm.customerName) {
      filteredData = filteredData.filter(item => item.customerName.includes(searchForm.customerName));
    }
    if (searchForm.name) {
      filteredData = filteredData.filter(item => item.name.includes(searchForm.name));
    }
    if (searchForm.stage) {
      filteredData = filteredData.filter(item => item.stage === searchForm.stage);
    }
    if (searchForm.owner) {
      filteredData = filteredData.filter(item => item.owner === searchForm.owner);
    }
    
    // 分页
    const start = (pagination.current - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    opportunityList.value = filteredData.slice(start, end);
    pagination.total = filteredData.length;
    
    loading.value = false;
  }, 500);
};

// 初始化
onMounted(() => {
  fetchOpportunityList();
});

// 获取头像颜色
const getAvatarColor = (name: string) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#f56a00', '#7265e6'];
  let total = 0;
  for (let i = 0; i < name.length; i++) {
    total += name.charCodeAt(i);
  }
  return colors[total % colors.length];
};

// 获取阶段百分比
const getStagePercent = (stage: string) => {
  const stageMap: Record<string, number> = {
    'initial': 20,
    'qualified': 40,
    'proposal': 60,
    'negotiation': 80,
    'closed-won': 100,
    'closed-lost': 100,
  };
  return stageMap[stage] || 0;
};

// 获取阶段状态
const getStageStatus = (stage: string) => {
  if (stage === 'closed-won') return 'success';
  if (stage === 'closed-lost') return 'exception';
  return 'active';
};

// 获取阶段颜色
const getStageColor = (stage: string) => {
  const colorMap: Record<string, string> = {
    'initial': 'blue',
    'qualified': 'cyan',
    'proposal': 'purple',
    'negotiation': 'orange',
    'closed-won': 'green',
    'closed-lost': 'red',
  };
  return colorMap[stage] || 'default';
};

// 获取阶段名称
const getStageName = (stage: string) => {
  const stageMap: Record<string, string> = {
    'initial': '初步接洽',
    'qualified': '需求确认',
    'proposal': '方案制定',
    'negotiation': '商务谈判',
    'closed-won': '成功签约',
    'closed-lost': '机会流失',
  };
  return stageMap[stage] || stage;
};

// 获取来源名称
const getSourceName = (source: string) => {
  const sourceMap: Record<string, string> = {
    'website': '官网',
    'referral': '转介绍',
    'exhibition': '展会',
    'advertisement': '广告',
    'social': '社交媒体',
    'other': '其他',
  };
  return sourceMap[source] || source;
};

// 格式化日期
const formatDate = (dateString: string, format = 'YYYY-MM-DD HH:mm') => {
  if (!dateString) return '-';
  return dayjs(dateString).format(format);
};

// 格式化数字
const formatNumber = (num: number) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 判断是否今天
const isToday = (dateString: string) => {
  if (!dateString) return false;
  const date = dayjs(dateString);
  const today = dayjs();
  return date.format('YYYY-MM-DD') === today.format('YYYY-MM-DD');
};

// 判断是否过期
const isOverdue = (dateString: string) => {
  if (!dateString) return false;
  const date = dayjs(dateString);
  const today = dayjs();
  return date.isBefore(today, 'day');
};

// 弹窗相关
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 表单数据
const formState = reactive({
  id: '',
  customerId: '',
  name: '',
  source: 'website',
  expectedAmount: 0,
  stage: 'initial',
  probability: 20,
  expectedCloseDate: null as dayjs.Dayjs | null,
  owner: '1',
  competitor: '',
  nextStep: '',
  nextContactTime: null as dayjs.Dayjs | null,
  description: '',
  remark: '',
});

// 表单验证规则
const rules = {
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  name: [
    { required: true, message: '请输入商机名称', trigger: 'blur' },
    { max: 100, message: '商机名称不能超过100个字符', trigger: 'blur' },
  ],
  source: [{ required: true, message: '请选择商机来源', trigger: 'change' }],
  expectedAmount: [{ required: true, message: '请输入预计金额', trigger: 'blur' }],
  stage: [{ required: true, message: '请选择当前阶段', trigger: 'change' }],
  probability: [{ required: true, message: '请选择可能性', trigger: 'change' }],
  expectedCloseDate: [{ required: true, message: '请选择预计成交日期', trigger: 'change', type: 'object' }],
  owner: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  nextContactTime: [{ required: true, message: '请选择下次联系时间', trigger: 'change', type: 'object' }],
};

// 跟进记录相关
const trackModalVisible = ref(false);
const trackFormRef = ref();
const currentOpportunity = ref<any>(null);

// 跟进表单
const trackForm = reactive({
  opportunityId: '',
  content: '',
  method: 'phone',
  nextContactTime: null as dayjs.Dayjs | null,
  stage: '',
});

// 跟进表单验证规则
const trackRules = {
  content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
  method: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
  nextContactTime: [{ required: true, message: '请选择下次联系时间', trigger: 'change', type: 'object' }],
};

// 跟进记录列表相关
const trackColumns = [
  { title: '跟进内容', dataIndex: 'content', key: 'content' },
  { title: '跟进方式', dataIndex: 'method', key: 'method', width: 120 },
  { title: '创建人', dataIndex: 'creator', key: 'creator', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 180 },
];

// 跟进记录数据
const trackRecords = ref<any[]>([]);
const showTrackFormFlag = ref(false);

// 显示新增弹窗
const showAddModal = () => {
  isEdit.value = false;
  
  // 重置表单
  Object.keys(formState).forEach(key => {
    if (key === 'source') {
      formState[key] = 'website';
    } else if (key === 'stage') {
      formState[key] = 'initial';
    } else if (key === 'probability') {
      formState[key] = 20;
    } else if (key === 'expectedAmount') {
      formState[key] = 0;
    } else if (key === 'owner') {
      formState[key] = '1';
    } else if (key === 'expectedCloseDate' || key === 'nextContactTime') {
      formState[key] = null;
    } else {
      formState[key] = '';
    }
  });
  
  modalVisible.value = true;
};

// 查看销售机会
const viewOpportunity = (record: any) => {
  message.info(`查看销售机会: ${record.name}`);
  // 如果有详情页，可以跳转到详情页
  // router.push(`/customer/opportunity/detail/${record.id}`);
};

// 编辑销售机会
const handleEdit = (record: any) => {
  isEdit.value = true;
  
  // 填充表单数据
  Object.keys(formState).forEach(key => {
    if (key === 'expectedCloseDate') {
      formState[key] = record[key] ? dayjs(record[key]) : null;
    } else if (key === 'nextContactTime') {
      formState[key] = record[key] ? dayjs(record[key]) : null;
    } else if (key in record) {
      formState[key] = record[key];
    }
  });
  
  modalVisible.value = true;
};

// 跟进销售机会
const handleTrack = (record: any) => {
  currentOpportunity.value = record;
  
  // 生成随机的跟进记录
  trackRecords.value = Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, index) => ({
    id: index + 1,
    content: ['电话沟通需求细节', '发送产品资料', '客户需求讨论', '方案调整', '价格商议'][Math.floor(Math.random() * 5)],
    method: ['phone', 'email', 'visit', 'meeting', 'wechat'][Math.floor(Math.random() * 5)],
    creator: ['张三', '李四', '王五'][Math.floor(Math.random() * 3)],
    createTime: dayjs().subtract(Math.floor(Math.random() * 30), 'day').format('YYYY-MM-DD HH:mm:ss'),
  }));
  
  // 隐藏表单，显示列表
  showTrackFormFlag.value = false;
  
  // 重置跟进表单
  trackForm.opportunityId = record.id;
  trackForm.content = '';
  trackForm.method = 'phone';
  trackForm.nextContactTime = dayjs().add(7, 'day');
  trackForm.stage = '';
  
  trackModalVisible.value = true;
};

// 提交跟进记录
const handleTrackSubmit = () => {
  trackFormRef.value.validate().then(() => {
    // 添加新的跟进记录
    trackRecords.value.unshift({
      id: trackRecords.value.length + 1,
      content: trackForm.content,
      method: trackForm.method,
      creator: '当前用户',
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });
    
    // 模拟提交
    message.success('跟进记录添加成功');
    
    // 如果需要更新阶段
    if (trackForm.stage && currentOpportunity.value) {
      currentOpportunity.value.stage = trackForm.stage;
    }
    
    // 更新下次联系时间
    if (currentOpportunity.value && trackForm.nextContactTime) {
      currentOpportunity.value.nextContactTime = trackForm.nextContactTime.format('YYYY-MM-DD HH:mm:ss');
    }
    
    // 隐藏表单
    showTrackFormFlag.value = false;
    
    // 刷新列表
    fetchOpportunityList();
    
  }).catch((error: any) => {
    console.log('验证失败', error);
  });
};

// 更新销售机会阶段
const handleUpdateStage = (record: any, stage: string) => {
  // 模拟请求
  record.stage = stage;
  
  // 根据阶段更新可能性
  const probabilityMap: Record<string, number> = {
    'initial': 10,
    'qualified': 30,
    'proposal': 50,
    'negotiation': 70,
    'closed-won': 100,
    'closed-lost': 0,
  };
  record.probability = probabilityMap[stage] || record.probability;
  
  message.success(`阶段已更新为: ${getStageName(stage)}`);
};

// 弹窗确认
const handleModalOk = () => {
  formRef.value.validate().then(() => {
    // 模拟保存
    if (isEdit.value) {
      message.success('编辑成功');
    } else {
      message.success('添加成功');
    }
    
    modalVisible.value = false;
    fetchOpportunityList();
  }).catch((error: any) => {
    console.log('验证失败', error);
  });
};

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 显示跟进表单
const showTrackForm = () => {
  showTrackFormFlag.value = true;
};

// 取消跟进表单
const cancelTrackForm = () => {
  showTrackFormFlag.value = false;
};

// 获取跟进方式名称
const getTrackMethodName = (method: string) => {
  const methodMap: Record<string, string> = {
    'phone': '电话',
    'visit': '拜访',
    'email': '邮件',
    'meeting': '会议',
    'wechat': '微信',
    'other': '其他',
  };
  return methodMap[method] || method;
};

// select筛选方法
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
</script>

<style scoped>
.opportunity-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
