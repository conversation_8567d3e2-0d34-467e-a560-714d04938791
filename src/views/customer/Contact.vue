<template>
  <div class="contact-form-container animate-fadeIn">
    <!-- 页面标题区域 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">添加联系记录</h2>
          <p class="text-gray-500 mt-1">记录客户沟通和跟进情况</p>
        </div>
        <a-button @click="router.back()">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
      </div>
    </div>
    
    <!-- 联系记录表单 -->
    <a-card :bordered="false" class="mb-6">
      <a-form
        :model="contactForm"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="客户信息">
          <div class="flex items-center">
            <a-avatar :style="{ backgroundColor: getAvatarColor(customerName) }">
              {{ customerName.charAt(0) }}
            </a-avatar>
            <span class="ml-2 font-medium">{{ customerName }}</span>
          </div>
        </a-form-item>
        
        <a-form-item label="联系方式" name="method">
          <a-select v-model:value="contactForm.method" placeholder="请选择联系方式" style="max-width: 300px">
            <a-select-option v-for="method in contactMethods" :key="method.value" :value="method.value">
              {{ method.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="跟进内容" name="content">
          <a-textarea 
            v-model:value="contactForm.content" 
            placeholder="请详细描述沟通内容、客户需求和跟进情况" 
            :rows="5" 
          />
        </a-form-item>
        
        <a-form-item label="联系时间" name="contactTime">
          <a-date-picker 
            v-model:value="contactForm.contactTime" 
            show-time 
            format="YYYY-MM-DD HH:mm:ss" 
            style="max-width: 300px" 
          />
        </a-form-item>
        
        <a-form-item label="下次联系时间" name="nextContactTime">
          <a-date-picker 
            v-model:value="contactForm.nextContactTime" 
            format="YYYY-MM-DD" 
            style="max-width: 300px" 
          />
        </a-form-item>
        
        <a-form-item label="更新状态" name="status">
          <a-select v-model:value="contactForm.status" placeholder="是否更新客户状态" style="max-width: 300px">
            <a-select-option value="">保持不变</a-select-option>
            <a-select-option value="following">跟进中</a-select-option>
            <a-select-option value="negotiating">商务谈判</a-select-option>
            <a-select-option value="signed">已签约</a-select-option>
            <a-select-option value="lost">已流失</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item :wrapper-col="{ span: 18, offset: 4 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit">
              <template #icon><check-outlined /></template>
              保存记录
            </a-button>
            <a-button @click="router.back()">
              <template #icon><close-outlined /></template>
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 历史联系记录 -->
    <a-card title="历史联系记录" :bordered="false" v-if="contactList.length > 0">
      <a-timeline mode="left">
        <a-timeline-item v-for="item in contactList" :key="item.id">
          <template #dot>
            <a-avatar size="small">{{ item.creator[0] }}</a-avatar>
          </template>
          <div class="flex justify-between mb-2">
            <span class="font-medium">{{ contactMethodLabel(item.method) }}</span>
            <span class="text-gray-400">{{ formatDate(item.createTime) }}</span>
          </div>
          <div class="mb-2">{{ item.content }}</div>
          <div class="text-sm text-gray-500">
            <div v-if="item.status" class="mt-1">
              更新状态: <a-tag :color="getStatusColor(item.status)">{{ getStatusName(item.status) }}</a-tag>
            </div>
            <div v-if="item.nextContactTime" class="mt-1">
              下次联系: {{ formatDate(item.nextContactTime) }}
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>
    
    <a-empty v-else description="暂无历史联系记录" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  LeftOutlined, 
  CheckOutlined, 
  CloseOutlined
} from '@ant-design/icons-vue';
import { useCustomerStore } from '@/stores/customer';
import type { CustomerContact, ContactMethod } from '@/types/customer';

const router = useRouter();
const route = useRoute();
const customerStore = useCustomerStore();

// 获取路由参数
const customerId = computed(() => route.query.id as string);
const customerName = computed(() => route.query.name as string || '未知客户');

// 联系记录列表
const contactList = ref<CustomerContact[]>([]);

// 联系方式选项
const contactMethods = [
  { label: '电话沟通', value: 'phone' },
  { label: '现场拜访', value: 'visit' },
  { label: '邮件往来', value: 'email' },
  { label: '会议', value: 'meeting' },
  { label: '微信沟通', value: 'wechat' },
  { label: '其他方式', value: 'other' },
];

// 表单实例
const formRef = ref();

// 表单数据
const contactForm = reactive({
  customerId: '',
  method: 'phone' as ContactMethod,
  content: '',
  contactTime: dayjs(),
  nextContactTime: dayjs().add(7, 'day'),
  status: '',
});

// 表单验证规则
const rules = {
  method: [{ required: true, message: '请选择联系方式', trigger: 'change' }],
  content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
  contactTime: [{ required: true, message: '请选择联系时间', trigger: 'change', type: 'object' }],
};

// 获取头像颜色
const getAvatarColor = (name: string) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#f56a00', '#7265e6'];
  let total = 0;
  for (let i = 0; i < name.length; i++) {
    total += name.charCodeAt(i);
  }
  return colors[total % colors.length];
};

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    new: '新建',
    following: '跟进中',
    negotiating: '商务谈判',
    signed: '已签约',
    lost: '已流失',
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    new: 'blue',
    following: 'cyan',
    negotiating: 'orange',
    signed: 'green',
    lost: 'red',
  };
  return colorMap[status] || 'default';
};

// 获取联系方式标签
const contactMethodLabel = (method: string) => {
  const found = contactMethods.find(m => m.value === method);
  return found ? found.label : method;
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm');
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate().then(async () => {
    // 准备提交数据
    const data: Partial<CustomerContact> = {
      customerId: customerId.value,
      method: contactForm.method,
      content: contactForm.content,
      contactTime: contactForm.contactTime.format('YYYY-MM-DD HH:mm:ss'),
      nextContactTime: contactForm.nextContactTime ? contactForm.nextContactTime.format('YYYY-MM-DD') : '',
      status: contactForm.status,
    };
    
    try {
      // 提交到服务器
      const result = await customerStore.createCustomerContact(data);
      
      if (result) {
        message.success('联系记录添加成功');
        // 刷新列表
        fetchContactList();
        // 重置表单
        resetForm();
      }
    } catch (error) {
      console.error('添加联系记录失败', error);
    }
  }).catch((err: Error) => {
    console.log('表单验证失败', err);
  });
};

// 重置表单
const resetForm = () => {
  contactForm.method = 'phone';
  contactForm.content = '';
  contactForm.contactTime = dayjs();
  contactForm.nextContactTime = dayjs().add(7, 'day');
  contactForm.status = '';
  
  formRef.value.resetFields();
};

// 获取联系记录列表
const fetchContactList = async () => {
  if (!customerId.value) return;
  
  try {
    const contacts = await customerStore.fetchCustomerContacts(customerId.value);
    contactList.value = contacts;
  } catch (error) {
    console.error('获取联系记录列表失败', error);
  }
};

// 初始化
onMounted(() => {
  if (customerId.value) {
    contactForm.customerId = customerId.value;
    fetchContactList();
  } else {
    message.error('缺少客户ID参数');
    router.replace('/customer/list');
  }
});
</script>

<style scoped>
.contact-form-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 