<template>
  <div class="customer-detail">
    <!-- 页面头部 -->
    <div class="mb-4 flex items-center justify-between">
      <div class="flex items-center gap-4">
        <a-button @click="router.back()">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
        <h2 class="text-xl font-bold m-0">{{ customerInfo.name }}</h2>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 基本信息 -->
      <a-card class="lg:col-span-2">
        <template #title>基本信息</template>
        <template #extra>
          <a-button type="link" @click="handleEditBasicInfo" size="small">
            <template #icon><edit-outlined /></template>
            编辑
          </a-button>
        </template>
        <a-descriptions :column="2">
          <a-descriptions-item label="客户编号">
            {{ customerInfo.code || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户名称">
            {{ customerInfo.name }}
          </a-descriptions-item>
          <a-descriptions-item label="所属行业">
            <a-tag :color="getIndustryColor(customerInfo.industry)">
            {{ getIndustryName(customerInfo.industry) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="客户类型">
            <a-tag :color="getTypeColor(customerInfo.type)">
            {{ getTypeName(customerInfo.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="联系人">
            {{ customerInfo.contact }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ customerInfo.phone }}
          </a-descriptions-item>
          <a-descriptions-item label="电子邮箱">
            {{ customerInfo.email }}
          </a-descriptions-item>
          <a-descriptions-item label="详细地址" :span="2">
            {{ customerInfo.address }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(customerInfo.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDate(customerInfo.updateTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            {{ customerInfo.remark || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 统计信息 -->
      <a-card title="统计信息">
        <div class="grid grid-cols-1 gap-4">
          <div class="text-center">
            <div class="text-gray-500">项目数量</div>
            <div class="text-2xl font-bold mt-2">{{ statistics.projectCount }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500">销售合同</div>
            <div class="text-2xl font-bold mt-2">{{ statistics.contractCount }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500">销售总额</div>
            <div class="text-2xl font-bold mt-2">¥{{ formatMoney(statistics.totalSales) }}</div>
          </div>
        </div>
      </a-card>

      <!-- 开票信息 -->
      <a-card title="开票信息" :bordered="true" class="lg:col-span-3">
        <template #extra>
          <a-button type="link" @click="handleEditInvoiceInfo" size="small">
            <template #icon><edit-outlined /></template>
            编辑
          </a-button>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <div class="text-gray-500 mb-1">公司名称</div>
            <div class="font-medium">{{ invoiceInfo.companyName || customerInfo.name }}</div>
          </div>
          <div>
            <div class="text-gray-500 mb-1">纳税人识别号</div>
            <div class="font-medium">{{ invoiceInfo.taxId || '-' }}</div>
          </div>
          <div>
            <div class="text-gray-500 mb-1">开户银行</div>
            <div class="font-medium">{{ invoiceInfo.bank || '-' }}</div>
          </div>
          <div>
            <div class="text-gray-500 mb-1">银行账号</div>
            <div class="font-medium">{{ invoiceInfo.bankAccount || '-' }}</div>
          </div>
          <div>
            <div class="text-gray-500 mb-1">开票地址</div>
            <div class="font-medium">{{ invoiceInfo.address || customerInfo.address }}</div>
          </div>
          <div>
            <div class="text-gray-500 mb-1">联系电话</div>
            <div class="font-medium">{{ invoiceInfo.phone || customerInfo.phone }}</div>
          </div>
          <div class="md:col-span-2">
            <div class="text-gray-500 mb-1">备注</div>
            <div class="font-medium">{{ invoiceInfo.remark || '-' }}</div>
          </div>
        </div>
      </a-card>

      <!-- 相关项目 -->
      <a-card title="相关项目" class="lg:col-span-3">
        <template #extra>
          <a-button type="link" size="small" @click="handleAddProject">
            <template #icon><plus-outlined /></template>
            新建项目
          </a-button>
        </template>
        <a-table
          :columns="projectColumns"
          :data-source="projectList"
          :pagination="{ pageSize: 5 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a @click="handleViewProject(record)" class="text-blue-600 hover:text-blue-800">{{ record.name }}</a>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getProjectStatusColor(record.status)">
                {{ getProjectStatusName(record.status) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 编辑基本信息抽屉 -->
    <a-drawer
      v-model:visible="basicInfoDrawerVisible"
      title="编辑基本信息"
      width="700px"
      placement="right"
      @close="basicInfoDrawerVisible = false"
    >
      <a-form
        :model="editingBasicInfo"
        :rules="basicInfoRules"
        ref="basicInfoFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="客户名称" name="name">
          <a-input v-model:value="editingBasicInfo.name" placeholder="请输入客户公司全称" />
        </a-form-item>
        
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="editingBasicInfo.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="客户类型" name="type">
          <a-select v-model:value="editingBasicInfo.type" placeholder="请选择客户类型">
            <a-select-option :value="CustomerType.ENTERPRISE">企业</a-select-option>
            <a-select-option :value="CustomerType.GOVERNMENT">政府</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="所属行业" name="industry">
          <a-select v-model:value="editingBasicInfo.industry" placeholder="请选择所属行业">
            <a-select-option value="I01">政府机构</a-select-option>
            <a-select-option value="I02">金融服务</a-select-option>
            <a-select-option value="I03">信息技术/互联网</a-select-option>
            <a-select-option value="I04">制造与工业</a-select-option>
            <a-select-option value="I05">零售与消费品</a-select-option>
            <a-select-option value="I06">能源与公用事业</a-select-option>
            <a-select-option value="I07">交通与物流</a-select-option>
            <a-select-option value="I08">医疗与健康</a-select-option>
            <a-select-option value="I09">教育与科研</a-select-option>
            <a-select-option value="I10">房地产与建筑</a-select-option>
            <a-select-option value="I11">专业服务</a-select-option>
            <a-select-option value="I12">农林牧渔</a-select-option>
            <a-select-option value="I13">其他/未分类</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="editingBasicInfo.contact" placeholder="请输入主要联系人姓名" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="editingBasicInfo.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="电子邮箱" name="email">
          <a-input v-model:value="editingBasicInfo.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        
        <a-form-item label="联系地址" name="address">
          <a-textarea v-model:value="editingBasicInfo.address" placeholder="请输入详细地址" :rows="3" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="editingBasicInfo.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="text-right">
          <a-space>
            <a-button @click="basicInfoDrawerVisible = false">取消</a-button>
            <a-button type="primary" @click="handleSaveBasicInfo" :loading="savingBasicInfo">
              保存
            </a-button>
          </a-space>
      </div>
      </template>
    </a-drawer>

    <!-- 编辑开票信息抽屉 -->
    <a-drawer
      v-model:visible="invoiceDrawerVisible"
      title="编辑开票信息"
      width="700px"
      placement="right"
      @close="invoiceDrawerVisible = false"
    >
      <a-form
        :model="editingInvoiceInfo"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="公司名称">
          <a-input v-model:value="editingInvoiceInfo.companyName" placeholder="请输入开票公司名称" />
        </a-form-item>
        <a-form-item label="纳税人识别号">
          <a-input v-model:value="editingInvoiceInfo.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        <a-form-item label="开户银行">
          <a-input v-model:value="editingInvoiceInfo.bank" placeholder="请输入开户银行" />
        </a-form-item>
        <a-form-item label="银行账号">
          <a-input v-model:value="editingInvoiceInfo.bankAccount" placeholder="请输入银行账号" />
        </a-form-item>
        <a-form-item label="公司地址">
          <a-input v-model:value="editingInvoiceInfo.address" placeholder="请输入公司地址" />
        </a-form-item>
        <a-form-item label="联系电话">
          <a-input v-model:value="editingInvoiceInfo.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea v-model:value="editingInvoiceInfo.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="text-right">
          <a-space>
            <a-button @click="invoiceDrawerVisible = false">取消</a-button>
            <a-button type="primary" @click="handleSaveInvoiceInfo" :loading="savingInvoiceInfo">
              保存
            </a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  LeftOutlined,
  EditOutlined,
  PlusOutlined,
  UserOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useCustomerStore } from '@/stores/customer';
import type { CustomerInvoiceInfo } from '@/types/customer';
import { CustomerType, IndustryType } from '@/constants/customer';

const router = useRouter();
const route = useRoute();
const customerStore = useCustomerStore();

// 客户信息
const customerInfo = ref<any>({
  id: '',
  name: '',
  code: '',
  taxId: '',
  type: CustomerType.ENTERPRISE,
  industry: IndustryType.GOVERNMENT_AGENCIES,
  contact: '',
  phone: '',
  email: '',
  address: '',
  createTime: '',
  updateTime: '',
  remark: '',
});

// 开票信息
const invoiceInfo = ref<CustomerInvoiceInfo>({
  id: '',
  customerId: '',
  companyName: '',
  taxId: '',
  bank: '',
  bankAccount: '',
  address: '',
  phone: '',
  remark: '',
});

// 编辑开票信息
const invoiceDrawerVisible = ref(false);
const editingInvoiceInfo = reactive({...invoiceInfo.value});
const savingInvoiceInfo = ref(false);

// 编辑基本信息抽屉
const basicInfoDrawerVisible = ref(false);
const editingBasicInfo = reactive({
  name: '',
  taxId: '',
  type: CustomerType.ENTERPRISE,
  industry: IndustryType.GOVERNMENT_AGENCIES,
  contact: '',
  phone: '',
  email: '',
  address: '',
  remark: '',
});
const savingBasicInfo = ref(false);
const basicInfoFormRef = ref();

// 基本信息表单验证规则
const basicInfoRules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
};

// 统计信息
const statistics = ref({
  projectCount: 0,
  contractCount: 0,
  totalSales: 0,
});

// 项目列表
const projectColumns = [
  { title: '项目编号', dataIndex: 'code', key: 'code', width: 180 },
  { title: '项目名称', dataIndex: 'name', key: 'name' },
  { title: '状态', dataIndex: 'status', key: 'status', width: 120 },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime', width: 120 },
  { title: '预计结束', dataIndex: 'endTime', key: 'endTime', width: 120 },
];

const projectList = ref<any[]>([]);

// 获取行业名称
function getIndustryName(industry: string) {
  const industryMap: Record<string, string> = {
    'I01': '政府机构',
    'I02': '金融服务',
    'I03': '信息技术/互联网',
    'I04': '制造与工业',
    'I05': '零售与消费品',
    'I06': '能源与公用事业',
    'I07': '交通与物流',
    'I08': '医疗与健康',
    'I09': '教育与科研',
    'I10': '房地产与建筑',
    'I11': '专业服务',
    'I12': '农林牧渔',
    'I13': '其他/未分类',
  };
  return industryMap[industry] || '-';
}

// 获取行业颜色
function getIndustryColor(industry: string) {
  const colorMap: Record<string, string> = {
    'I01': 'red',      // 政府机构 - 红色
    'I02': 'gold',     // 金融服务 - 金色
    'I03': 'blue',     // 信息技术/互联网 - 蓝色
    'I04': 'orange',   // 制造与工业 - 橙色
    'I05': 'cyan',     // 零售与消费品 - 青色
    'I06': 'purple',   // 能源与公用事业 - 紫色
    'I07': 'green',    // 交通与物流 - 绿色
    'I08': 'pink',     // 医疗与健康 - 粉色
    'I09': 'lime',     // 教育与科研 - 青柠色
    'I10': 'brown',    // 房地产与建筑 - 棕色
    'I11': 'gray',     // 专业服务 - 灰色
    'I12': 'olive',    // 农林牧渔 - 橄榄色
    'I13': 'default',  // 其他/未分类 - 默认色
  };
  return colorMap[industry] || 'default';
}

// 获取客户类型名称
function getTypeName(type: string) {
  const typeMap: Record<string, string> = {
    'C': '企业',
    'G': '政府',
  };
  return typeMap[type] || '-';
}

// 获取客户类型颜色
function getTypeColor(type: string) {
  const colorMap: Record<string, string> = {
    'C': 'blue',   // 企业 - 蓝色
    'G': 'green',  // 政府 - 绿色
  };
  return colorMap[type] || 'default';
}

// 获取项目状态名称
function getProjectStatusName(status: string) {
  const statusMap: Record<string, string> = {
    pending: '准备中',
    progress: '进行中',
    completed: '已完成',
    suspended: '暂停',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
}

// 获取项目状态颜色
function getProjectStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    pending: 'default',
    progress: 'processing',
    completed: 'success',
    suspended: 'warning',
    cancelled: 'error',
  };
  return colorMap[status] || 'default';
}

// 格式化日期
function formatDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD');
}

// 格式化金额
function formatMoney(amount: number) {
  return amount.toLocaleString();
}

// 编辑基本信息
function handleEditBasicInfo() {
  // 将当前客户信息复制到编辑表单
  Object.assign(editingBasicInfo, {
    name: customerInfo.value.name,
    taxId: customerInfo.value.taxId,
    type: customerInfo.value.type || CustomerType.ENTERPRISE,
    industry: customerInfo.value.industry || IndustryType.GOVERNMENT_AGENCIES,
    contact: customerInfo.value.contact,
    phone: customerInfo.value.phone,
    email: customerInfo.value.email,
    address: customerInfo.value.address,
    remark: customerInfo.value.remark,
  });
  basicInfoDrawerVisible.value = true;
}

// 保存基本信息
async function handleSaveBasicInfo() {
  try {
    await basicInfoFormRef.value.validate();
    savingBasicInfo.value = true;
    
    // 这里应该调用API保存基本信息
    await customerStore.updateCustomer(customerInfo.value.id, editingBasicInfo);
    
    // 更新本地数据
    Object.assign(customerInfo.value, editingBasicInfo);
    
    message.success('保存成功');
    basicInfoDrawerVisible.value = false;
  } catch (error) {
    console.error('保存基本信息失败', error);
    message.error('保存失败');
  } finally {
    savingBasicInfo.value = false;
  }
}

// 查看项目
function handleViewProject(record: any) {
  router.push(`/projects/detail/${record.id}`);
}

// 添加项目
function handleAddProject() {
  // 跳转到新建项目页面，并传递客户ID
  router.push(`/projects?customerId=${customerInfo.value.id}`);
}

// 编辑开票信息
function handleEditInvoiceInfo() {
  Object.assign(editingInvoiceInfo, invoiceInfo.value);
  invoiceDrawerVisible.value = true;
}

// 保存开票信息
async function handleSaveInvoiceInfo() {
  try {
    savingInvoiceInfo.value = true;
    await customerStore.updateCustomerInvoiceInfo(customerInfo.value.id, editingInvoiceInfo);
    // 重新获取更新后的开票信息
    const updatedInfo = await customerStore.fetchCustomerInvoiceInfo(customerInfo.value.id);
    if (updatedInfo) {
      invoiceInfo.value = updatedInfo;
    }
    message.success('保存成功');
    invoiceDrawerVisible.value = false;
  } catch (error) {
    console.error('保存开票信息失败', error);
    message.error('保存失败');
  } finally {
    savingInvoiceInfo.value = false;
  }
}

// 加载客户详情数据
async function loadCustomerDetail(id: string) {
  try {
    // 获取客户基本信息
    const customer = await customerStore.fetchCustomerById(id);
    if (customer) {
      customerInfo.value = {
        ...customer,
        type: customer.type || CustomerType.ENTERPRISE, // 确保有默认值
        industry: customer.industry || IndustryType.GOVERNMENT_AGENCIES, // 确保有默认值
      };
    }
    
    // 获取客户开票信息
    const invoice = await customerStore.fetchCustomerInvoiceInfo(id);
    if (invoice) {
      invoiceInfo.value = invoice;
    }
    
    // 模拟获取统计数据和相关项目
    statistics.value = {
      projectCount: 5,
      contractCount: 3,
      totalSales: 1000000,
    };
    
    projectList.value = [
      {
        id: '1',
        code: 'P001-C250624001',
        name: '企业信息化建设项目',
        status: 'progress',
        startTime: '2024-01-01',
        endTime: '2024-06-30',
      },
      {
        id: '2',
        code: 'P002-C250624001',
        name: '数字化转型咨询服务',
        status: 'completed',
        startTime: '2023-07-01',
        endTime: '2023-12-31',
      },
    ];
  } catch (error) {
    console.error('加载客户详情失败', error);
    message.error('加载客户详情失败');
  }
}

// 初始化
onMounted(() => {
  const id = route.params.id;
  if (id) {
    loadCustomerDetail(id as string);
  }
});
</script>

<style scoped>
.customer-detail {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
