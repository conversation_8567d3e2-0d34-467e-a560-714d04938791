<template>
  <div class="customer-form-container animate-fadeIn">
    <!-- 页面标题区域 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">{{ isEdit ? '编辑客户' : '新增客户' }}</h2>
          <p class="text-gray-500 mt-1">{{ isEdit ? '修改客户资料和联系信息' : '添加新的客户信息到系统' }}</p>
        </div>
        <a-button @click="router.back()">
          <template #icon><left-outlined /></template>
            返回
          </a-button>
      </div>
    </div>
    
    <a-card :bordered="false">
      <a-form
        :model="formData" 
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-divider>基本信息</a-divider>
          
          <a-form-item label="客户名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入客户公司全称" />
          </a-form-item>
          
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="formData.taxId" placeholder="请输入纳税人识别号" />
          </a-form-item>
          
          <a-form-item label="客户类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择客户类型">
              <a-select-option value="potential">潜在客户</a-select-option>
              <a-select-option value="formal">正式客户</a-select-option>
              <a-select-option value="vip">VIP客户</a-select-option>
            </a-select>
          </a-form-item>
        
        <a-form-item label="客户状态" name="status">
          <a-select v-model:value="formData.status" placeholder="请选择客户状态">
            <a-select-option value="new">新建</a-select-option>
            <a-select-option value="following">跟进中</a-select-option>
            <a-select-option value="negotiating">商务谈判</a-select-option>
            <a-select-option value="signed">已签约</a-select-option>
            <a-select-option value="lost">已流失</a-select-option>
          </a-select>
        </a-form-item>
          
          <a-form-item label="所属行业" name="industry">
          <a-select v-model:value="formData.industry" placeholder="请选择所属行业">
              <a-select-option value="internet">互联网</a-select-option>
              <a-select-option value="manufacturing">制造业</a-select-option>
              <a-select-option value="finance">金融</a-select-option>
              <a-select-option value="education">教育</a-select-option>
              <a-select-option value="medical">医疗</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="客户来源" name="source">
          <a-select v-model:value="formData.source" placeholder="请选择客户来源">
              <a-select-option value="website">官网</a-select-option>
              <a-select-option value="referral">转介绍</a-select-option>
              <a-select-option value="exhibition">展会</a-select-option>
              <a-select-option value="advertisement">广告</a-select-option>
              <a-select-option value="social">社交媒体</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
          
        <a-form-item label="客户负责人" name="owner">
          <a-select v-model:value="formData.owner" placeholder="请选择负责人">
              <a-select-option value="1">张三</a-select-option>
              <a-select-option value="2">李四</a-select-option>
              <a-select-option value="3">王五</a-select-option>
            </a-select>
          </a-form-item>
        
        <a-divider>联系信息</a-divider>
          
          <a-form-item label="联系人" name="contact">
          <a-input v-model:value="formData.contact" placeholder="请输入主要联系人姓名" />
          </a-form-item>
          
          <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
          </a-form-item>
          
          <a-form-item label="电子邮箱" name="email">
          <a-input v-model:value="formData.email" placeholder="请输入电子邮箱" />
          </a-form-item>
        
        <a-form-item label="联系地址" name="address">
          <a-textarea v-model:value="formData.address" placeholder="请输入详细地址" :rows="3" />
          </a-form-item>
          
          <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="3" />
          </a-form-item>
        
        <a-form-item :wrapper-col="{ span: 18, offset: 4 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="submitting">
              <template #icon><save-outlined /></template>
              保存
            </a-button>
            <a-button @click="router.back()">
              <template #icon><close-outlined /></template>
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  LeftOutlined, 
  SaveOutlined, 
  CloseOutlined 
} from '@ant-design/icons-vue';
import { useCustomerStore } from '@/stores/customer';
import { CustomerType, CustomerStatus, IndustryType, type Customer, type CustomerSource } from '@/types/customer';

const router = useRouter();
const route = useRoute();
const customerStore = useCustomerStore();

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id);

// 表单引用
const formRef = ref();
// 提交状态
const submitting = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  code: '',
  taxId: '',
  type: CustomerType.ENTERPRISE,
  status: CustomerStatus.NEW,
  industry: IndustryType.OTHER_UNCATEGORIZED,
  contact: '',
  phone: '',
  email: '',
  address: '',
  source: 'website' as CustomerSource,
  owner: '1',
  remark: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择客户状态', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
};

// 加载客户数据
const loadCustomerData = async (id: string) => {
  try {
    const customer = await customerStore.fetchCustomerById(id);
    if (customer) {
      // 填充表单数据
      Object.assign(formData, customer);
  } else {
      message.error('未找到客户信息');
      router.replace('/customer/list');
    }
  } catch (error) {
    console.error('加载客户数据失败', error);
    message.error('加载客户数据失败');
  }
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate().then(async () => {
    submitting.value = true;
    
    try {
      // 准备提交数据
      const data: Partial<Customer> = {
        name: formData.name,
        taxId: formData.taxId,
        type: formData.type as CustomerType,
        status: formData.status as CustomerStatus,
        industry: formData.industry as IndustryType,
        contact: formData.contact,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        source: formData.source,
        owner: formData.owner,
        remark: formData.remark,
      };
      
      let result;
      
      if (isEdit.value) {
        // 更新现有客户
        result = await customerStore.updateCustomer(formData.id, data);
      } else {
        // 创建新客户
        result = await customerStore.createCustomer(data);
      }
      
      if (result) {
        message.success(`客户${isEdit.value ? '更新' : '创建'}成功`);
        router.push('/customer/list');
      }
    } catch (error) {
      console.error('保存客户信息失败', error);
    } finally {
      submitting.value = false;
    }
  }).catch((err: Error) => {
    console.log('表单验证失败', err);
  });
};

// 初始化
onMounted(() => {
  if (isEdit.value && route.params.id) {
    loadCustomerData(route.params.id as string);
  }
});
</script>

<style scoped>
.customer-form-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
