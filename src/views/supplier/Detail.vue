<template>
  <div class="supplier-detail">
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="supplier" class="supplier-detail-content">
      <!-- 页面标题和操作 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center gap-4">
          <a-button @click="$router.back()">
            <template #icon><ArrowLeftOutlined /></template>
            返回
          </a-button>
          <h2 class="text-xl font-bold m-0">{{ supplier.name }}</h2>
        </div>
      </div>

      <!-- 供应商概览和统计 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- 基本信息 -->
        <a-card class="lg:col-span-2">
          <template #title>
            <div class="flex items-center gap-3">
              <a-avatar 
                :size="48" 
                :style="{ backgroundColor: getAvatarColor(supplier.name) }"
              >
                {{ supplier.name?.charAt(0) }}
              </a-avatar>
              <div>
                <div class="font-semibold text-base">{{ supplier.name }}</div>
                <div class="flex gap-2 mt-1">
                  <a-tag :color="getTypeColor(supplier.type)" size="small">
                    {{ supplier.type === 'government' ? '政府机构' : '企业客户' }}
                  </a-tag>
                  <a-tag :color="getStatusColor(supplier.status)" size="small">
                    {{ getStatusText(supplier.status) }}
                  </a-tag>
                </div>
              </div>
            </div>
          </template>
          <template #extra>
            <a-button type="link" @click="handleEditBasicInfo" size="small">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
          </template>
          <a-descriptions :column="2">
            <a-descriptions-item label="供应商编号">
              {{ supplier.code }}
            </a-descriptions-item>
            <a-descriptions-item label="供应商名称">
              {{ supplier.name }}
            </a-descriptions-item>
            <a-descriptions-item label="所属行业">
              <a-tag color="blue">
                {{ getIndustryText(supplier.industry) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="供应商类型">
              <a-tag :color="getTypeColor(supplier.type)">
                {{ supplier.type === 'government' ? '政府机构' : '企业客户' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="联系人">
              {{ supplier.contact }}
            </a-descriptions-item>
            <a-descriptions-item label="联系电话">
              <a :href="`tel:${supplier.phone}`">{{ supplier.phone }}</a>
            </a-descriptions-item>
            <a-descriptions-item label="电子邮箱">
              <a v-if="supplier.email" :href="`mailto:${supplier.email}`">{{ supplier.email }}</a>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="纳税人识别号">
              {{ supplier.taxId || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="详细地址" :span="2">
              {{ supplier.address }}
            </a-descriptions-item>
            <a-descriptions-item label="负责人">
              {{ supplier.ownerName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ supplier.createTime }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ supplier.updateTime }}
            </a-descriptions-item>
            <a-descriptions-item label="最近联系">
              {{ supplier.lastContactTime || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="备注" :span="2">
              {{ supplier.remark || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 统计信息 -->
        <a-card title="统计信息">
          <div class="grid grid-cols-1 gap-4">
            <div class="text-center">
              <div class="text-gray-500">参与项目</div>
              <div class="text-2xl font-bold mt-2">{{ statistics.projectCount }}</div>
            </div>
            <div class="text-center">
              <div class="text-gray-500">合同数量</div>
              <div class="text-2xl font-bold mt-2">{{ statistics.contractCount }}</div>
            </div>
            <div class="text-center">
              <div class="text-gray-500">合作总金额</div>
              <div class="text-2xl font-bold mt-2">¥{{ formatAmount(statistics.totalAmount) }}</div>
            </div>
            <div class="text-center">
              <div class="text-gray-500">质量评分</div>
              <div class="text-2xl font-bold mt-2">{{ statistics.qualityScore }}</div>
            </div>
          </div>
        </a-card>

        <!-- 开票信息 -->
        <a-card title="开票信息" :bordered="true" class="lg:col-span-3">
          <template #extra>
            <a-button type="link" @click="handleEditInvoiceInfo" size="small">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
          </template>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div class="text-gray-500 mb-1">公司名称</div>
              <div class="font-medium">{{ invoiceInfo.companyName }}</div>
            </div>
            <div>
              <div class="text-gray-500 mb-1">纳税人识别号</div>
              <div class="font-medium">{{ invoiceInfo.taxId }}</div>
            </div>
            <div>
              <div class="text-gray-500 mb-1">开户银行</div>
              <div class="font-medium">{{ invoiceInfo.bank }}</div>
            </div>
            <div>
              <div class="text-gray-500 mb-1">银行账号</div>
              <div class="font-medium">{{ invoiceInfo.bankAccount }}</div>
            </div>
            <div>
              <div class="text-gray-500 mb-1">开票地址</div>
              <div class="font-medium">{{ invoiceInfo.address }}</div>
            </div>
            <div>
              <div class="text-gray-500 mb-1">联系电话</div>
              <div class="font-medium">
                <a :href="`tel:${invoiceInfo.phone}`">{{ invoiceInfo.phone }}</a>
              </div>
            </div>
            <div class="md:col-span-2">
              <div class="text-gray-500 mb-1">备注</div>
              <div class="font-medium">{{ invoiceInfo.remark || '-' }}</div>
            </div>
          </div>
        </a-card>

        <!-- 相关项目 -->
        <a-card title="相关项目" class="lg:col-span-3">
          <template #extra>
            <a-button type="link" size="small" @click="handleAddProject">
              <template #icon><ProjectOutlined /></template>
              新增项目
            </a-button>
          </template>
          <a-table 
            :columns="projectColumns" 
            :data-source="projects" 
            :pagination="{ pageSize: 5 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <a @click="handleViewProject(record)" class="text-blue-600 hover:text-blue-800">{{ record.name }}</a>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getProjectStatusColor(record.status)">
                  {{ getProjectStatusText(record.status) }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>

    <!-- 编辑基本信息抽屉 -->
    <a-drawer
      v-model:open="editBasicDrawerVisible"
      title="编辑基本信息"
      :width="700"
      :mask-closable="false"
      placement="right"
    >
      <a-form
        ref="basicInfoFormRef"
        :model="editingBasicInfo"
        :rules="basicInfoRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        layout="horizontal"
      >
        <a-divider orientation="left">基本信息</a-divider>
        
        <a-form-item label="供应商名称" name="name">
          <a-input v-model:value="editingBasicInfo.name" placeholder="请输入供应商名称" />
        </a-form-item>
        
        <a-form-item label="供应商类型" name="type">
          <a-select v-model:value="editingBasicInfo.type" placeholder="请选择供应商类型">
            <a-select-option value="enterprise">企业客户</a-select-option>
            <a-select-option value="government">政府机构</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="所属行业" name="industry">
          <a-select v-model:value="editingBasicInfo.industry" placeholder="请选择所属行业">
            <a-select-option value="information_technology">信息技术</a-select-option>
            <a-select-option value="manufacturing_industrial">制造工业</a-select-option>
            <a-select-option value="financial_insurance">金融保险</a-select-option>
            <a-select-option value="real_estate_construction">房地产建筑</a-select-option>
            <a-select-option value="wholesale_retail">批发零售</a-select-option>
            <a-select-option value="transportation_logistics">交通物流</a-select-option>
            <a-select-option value="education_training">教育培训</a-select-option>
            <a-select-option value="healthcare_medical">医疗健康</a-select-option>
            <a-select-option value="culture_entertainment">文化娱乐</a-select-option>
            <a-select-option value="energy_environment">能源环保</a-select-option>
            <a-select-option value="agriculture_forestry">农林牧渔</a-select-option>
            <a-select-option value="accommodation_catering">住宿餐饮</a-select-option>
            <a-select-option value="scientific_research">科学研究</a-select-option>
            <a-select-option value="public_administration">公共管理</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>

        <a-divider orientation="left">联系信息</a-divider>
        
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="editingBasicInfo.contact" placeholder="请输入联系人姓名" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="editingBasicInfo.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="电子邮箱" name="email">
          <a-input v-model:value="editingBasicInfo.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        
        <a-form-item label="详细地址" name="address">
          <a-textarea v-model:value="editingBasicInfo.address" placeholder="请输入详细地址" :rows="3" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="editingBasicInfo.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="drawer-footer">
          <a-button @click="editBasicDrawerVisible = false">取消</a-button>
          <a-button type="primary" :loading="savingBasicInfo" @click="handleSaveBasicInfo">
            保存
          </a-button>
        </div>
      </template>
    </a-drawer>

    <!-- 编辑开票信息抽屉 -->
    <a-drawer
      v-model:open="editInvoiceDrawerVisible"
      title="编辑开票信息"
      :width="700"
      :mask-closable="false"
      placement="right"
    >
      <a-form
        ref="invoiceInfoFormRef"
        :model="editingInvoiceInfo"
        :rules="invoiceInfoRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        layout="horizontal"
      >
        <a-form-item label="公司名称" name="companyName">
          <a-input v-model:value="editingInvoiceInfo.companyName" placeholder="请输入公司名称" />
        </a-form-item>
        
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="editingInvoiceInfo.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="开户银行" name="bank">
          <a-input v-model:value="editingInvoiceInfo.bank" placeholder="请输入开户银行" />
        </a-form-item>
        
        <a-form-item label="银行账号" name="bankAccount">
          <a-input v-model:value="editingInvoiceInfo.bankAccount" placeholder="请输入银行账号" />
        </a-form-item>
        
        <a-form-item label="开票地址" name="address">
          <a-textarea v-model:value="editingInvoiceInfo.address" placeholder="请输入开票地址" :rows="3" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="editingInvoiceInfo.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="editingInvoiceInfo.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="drawer-footer">
          <a-button @click="editInvoiceDrawerVisible = false">取消</a-button>
          <a-button type="primary" :loading="savingInvoiceInfo" @click="handleSaveInvoiceInfo">
            保存
          </a-button>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  EditOutlined,
  PlusOutlined,
  ProjectOutlined,
  FileTextOutlined,
  DollarOutlined,
  StarOutlined,
  PhoneOutlined,
  MailOutlined,
} from '@ant-design/icons-vue';
import { useSupplierStore } from '@/stores/supplier';
import { CustomerType, SupplierStatus, IndustryType } from '@/constants/customer';
import type { SupplierDetail, SupplierStatistics, SupplierInvoiceInfo } from '@/types/supplier';
import {
  getSupplierTypeOptions,
  getSupplierStatusOptions,
  getIndustryTypeOptions,
  getSupplierTypeLabel,
  getSupplierStatusLabel,
  getIndustryTypeLabel,
  getProvinceName,
  getCityName,
  PROVINCE_OPTIONS
} from '@/constants/customer';
import dayjs from 'dayjs';

const route = useRoute();
const router = useRouter();
const supplierStore = useSupplierStore();

// 响应式数据
const loading = ref(false);
const supplier = ref<SupplierDetail | null>(null);
const activeTab = ref('basic');

// 统计数据
const statistics = computed(() => supplier.value?.statistics || {
  projectCount: 0,
  contractCount: 0,
  totalAmount: 0,
  qualityScore: 0,
});

// 开票信息
const invoiceInfo = computed(() => supplier.value?.invoiceInfo || {
  companyName: '',
  taxId: '',
  bank: '',
  bankAccount: '',
  address: '',
  phone: '',
  remark: '',
});

// 编辑状态
const editBasicDrawerVisible = ref(false);
const editInvoiceDrawerVisible = ref(false);
const savingBasicInfo = ref(false);
const savingInvoiceInfo = ref(false);

// 表单引用
const basicInfoFormRef = ref();
const invoiceInfoFormRef = ref();

// 编辑表单数据
const editingBasicInfo = reactive({
  name: '',
  type: CustomerType.ENTERPRISE,
  industry: IndustryType.INFORMATION_TECHNOLOGY,
  contact: '',
  phone: '',
  email: '',
  address: '',
  remark: '',
});

const editingInvoiceInfo = reactive({
  companyName: '',
  taxId: '',
  bank: '',
  bankAccount: '',
  address: '',
  phone: '',
  remark: '',
});

// 表单验证规则
const basicInfoRules = {
  name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
};

const invoiceInfoRules = {
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  taxId: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
  bank: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
  bankAccount: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
  address: [{ required: true, message: '请输入开票地址', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
};

// 项目列表
const projects = ref([
  {
    id: '1',
    code: 'P001-C250624001',
    name: '企业信息化建设项目',
    status: 'progress',
    startTime: '2024-01-01',
    endTime: '2024-06-30',
  },
  {
    id: '2',
    code: 'P002-C250624001',
    name: '数字化转型咨询服务',
    status: 'completed',
    startTime: '2023-07-01',
    endTime: '2023-12-31',
  },
]);

// 项目表格列配置
const projectColumns = [
  { title: '项目编号', dataIndex: 'code', key: 'code' },
  { title: '项目名称', dataIndex: 'name', key: 'name' },
  { title: '项目状态', dataIndex: 'status', key: 'status' },
  { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
  { title: '结束时间', dataIndex: 'endTime', key: 'endTime' },
  { title: '操作', key: 'action', width: 150 },
];

// 辅助函数
function formatAmount(amount: number): string {
  return amount.toLocaleString();
}

function getTypeColor(type?: string): string {
  return type === CustomerType.GOVERNMENT ? 'blue' : 'green';
}

function getStatusColor(status?: string): string {
  const colors: Record<string, string> = {
    [SupplierStatus.NEW]: 'default',
    [SupplierStatus.COOPERATING]: 'processing',
    [SupplierStatus.QUALIFIED]: 'success',
    [SupplierStatus.SUSPENDED]: 'warning',
    [SupplierStatus.TERMINATED]: 'error',
  };
  return colors[status || ''] || 'default';
}

function getStatusText(status?: string): string {
  const texts: Record<string, string> = {
    [SupplierStatus.NEW]: '新供应商',
    [SupplierStatus.COOPERATING]: '合作中',
    [SupplierStatus.QUALIFIED]: '已认证',
    [SupplierStatus.SUSPENDED]: '已暂停',
    [SupplierStatus.TERMINATED]: '已终止',
  };
  return texts[status || ''] || '未知状态';
}

function getIndustryText(industry?: string): string {
  const texts: Record<string, string> = {
    [IndustryType.INFORMATION_TECHNOLOGY]: '信息技术',
    [IndustryType.MANUFACTURING_INDUSTRIAL]: '制造工业',
    [IndustryType.FINANCIAL_INSURANCE]: '金融保险',
    [IndustryType.REAL_ESTATE_CONSTRUCTION]: '房地产建筑',
    [IndustryType.WHOLESALE_RETAIL]: '批发零售',
    [IndustryType.TRANSPORTATION_LOGISTICS]: '交通物流',
    [IndustryType.EDUCATION_TRAINING]: '教育培训',
    [IndustryType.HEALTHCARE_MEDICAL]: '医疗健康',
    [IndustryType.CULTURE_ENTERTAINMENT]: '文化娱乐',
    [IndustryType.ENERGY_ENVIRONMENT]: '能源环保',
    [IndustryType.AGRICULTURE_FORESTRY]: '农林牧渔',
    [IndustryType.ACCOMMODATION_CATERING]: '住宿餐饮',
    [IndustryType.SCIENTIFIC_RESEARCH]: '科学研究',
    [IndustryType.PUBLIC_ADMINISTRATION]: '公共管理',
    [IndustryType.OTHER]: '其他',
  };
  return texts[industry || ''] || '未知行业';
}

function getProjectStatusColor(status: string): string {
  const colors: Record<string, string> = {
    pending: 'default',
    progress: 'processing',
    completed: 'success',
    suspended: 'warning',
    cancelled: 'error',
  };
  return colors[status] || 'default';
}

function getProjectStatusText(status: string): string {
  const texts: Record<string, string> = {
    pending: '待启动',
    progress: '进行中',
    completed: '已完成',
    suspended: '已暂停',
    cancelled: '已取消',
  };
  return texts[status] || '未知状态';
}

function getAvatarColor(name: string): string {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae'];
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
}

// 事件处理
function handleEdit() {
  if (supplier.value) {
    router.push(`/supplier/form/${supplier.value.id}`);
  }
}

function handleEditBasicInfo() {
  if (supplier.value) {
    Object.assign(editingBasicInfo, {
      name: supplier.value.name,
      type: supplier.value.type,
      industry: supplier.value.industry,
      contact: supplier.value.contact,
      phone: supplier.value.phone,
      email: supplier.value.email || '',
      address: supplier.value.address,
      remark: supplier.value.remark || '',
    });
    editBasicDrawerVisible.value = true;
  }
}

async function handleSaveBasicInfo() {
  try {
    await basicInfoFormRef.value.validate();
    savingBasicInfo.value = true;
    
    if (supplier.value) {
      await supplierStore.updateSupplier(supplier.value.id, editingBasicInfo);
      
      // 更新本地数据
      Object.assign(supplier.value, editingBasicInfo);
      
      message.success('保存成功');
      editBasicDrawerVisible.value = false;
    }
  } catch (error) {
    console.error('保存基本信息失败', error);
    message.error('保存失败');
  } finally {
    savingBasicInfo.value = false;
  }
}

function handleEditInvoiceInfo() {
  if (supplier.value?.invoiceInfo) {
    Object.assign(editingInvoiceInfo, supplier.value.invoiceInfo);
    editInvoiceDrawerVisible.value = true;
  }
}

async function handleSaveInvoiceInfo() {
  try {
    await invoiceInfoFormRef.value.validate();
    savingInvoiceInfo.value = true;
    
    if (supplier.value) {
      // 更新开票信息
      Object.assign(supplier.value.invoiceInfo, editingInvoiceInfo);
      
      message.success('保存成功');
      editInvoiceDrawerVisible.value = false;
    }
  } catch (error) {
    console.error('保存开票信息失败', error);
    message.error('保存失败');
  } finally {
    savingInvoiceInfo.value = false;
  }
}

function handleAddProject() {
  router.push('/projects/initiation');
}

function handleViewProject(record: any) {
  router.push(`/projects/detail/${record.id}`);
}

function handleEditProject(record: any) {
  router.push(`/projects/form/${record.id}`);
}

// 数据加载
async function loadSupplierDetail(id: string) {
  loading.value = true;
  try {
    const data = await supplierStore.fetchSupplierDetail(id);
    supplier.value = data;
  } catch (error) {
    console.error('加载供应商详情失败', error);
    message.error('加载供应商详情失败');
  } finally {
    loading.value = false;
  }
}

// 初始化
onMounted(() => {
  const id = route.params.id;
  if (id) {
    loadSupplierDetail(id as string);
  }
});
</script>

<style scoped>
.supplier-detail {
  animation: fadeIn 0.5s ease-in-out;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 抽屉样式 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0;
  border-top: 1px solid #f0f0f0;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 链接样式 */
a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #40a9ff;
}
</style>
