<template>
  <div class="supplier-list-container animate-fadeIn">
    <!-- 页面标题区域 -->
    <div class="page-header mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold">供应商管理</h2>
          <p class="text-gray-500 mt-1">管理所有供应商信息和合作状态</p>
        </div>
        <div class="flex space-x-3">
          <a-button type="primary" @click="handleAddSupplier">
            <template #icon><plus-outlined /></template>
            新增供应商
          </a-button>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            导出
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6" :bordered="false">
      <div class="flex justify-between items-start flex-wrap">
        <div class="search-area w-full lg:w-auto flex-1">
          <a-input-search
            v-model:value="searchForm.keyword"
            placeholder="搜索供应商名称、编号或纳税人识别号"
            enter-button
            @search="handleSearch"
            class="search-input mb-4 max-w-lg"
            allow-clear
          />
          
          <a-form 
            layout="inline" 
            :model="searchForm" 
            :class="['advanced-search', showAdvanced ? 'expanded' : '']" 
            ref="searchFormRef"
          >
            <div class="flex flex-wrap gap-3">
              <a-form-item label="供应商类型" class="mb-2">
                <a-select v-model:value="searchForm.type" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option v-for="option in supplierTypeOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="合作状态" class="mb-2">
                <a-select v-model:value="searchForm.status" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option v-for="option in supplierStatusOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="所属行业" class="mb-2">
                <a-select v-model:value="searchForm.industry" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option v-for="option in industryOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="负责人" class="mb-2">
                <a-select v-model:value="searchForm.owner" style="width: 120px" placeholder="请选择" allowClear>
                  <a-select-option value="1">张三</a-select-option>
                  <a-select-option value="2">李四</a-select-option>
                  <a-select-option value="3">王五</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="创建日期" class="mb-2">
                <a-range-picker 
                  v-model:value="searchForm.dateRange" 
                  style="width: 240px"
                  format="YYYY-MM-DD"
                  :placeholder="['开始日期', '结束日期']"
                  allowClear />
              </a-form-item>
            </div>
            
            <div class="flex justify-end w-full mt-3">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
                <a-button type="link" @click="toggleAdvancedSearch">
                  {{ showAdvanced ? '收起筛选' : '展开筛选' }}
                  <template #icon>
                    <up-outlined v-if="showAdvanced" />
                    <down-outlined v-else />
                  </template>
                </a-button>
              </a-space>
            </div>
          </a-form>
        </div>
      </div>
    </a-card>
    
    <!-- 供应商列表区域 -->
    <a-card :bordered="false">
      <div class="flex justify-end mb-4">
        <a-space>
          <a-button @click="showColumnConfig">
            <template #icon><setting-outlined /></template>
            字段配置
          </a-button>
          <a-button @click="fetchSuppliers">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
      <a-table
        :columns="visibleColumns"
        :data-source="suppliers"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 自定义列内容 -->
        <template #bodyCell="{ column, record }">
          <!-- 供应商名称列 -->
          <template v-if="column.dataIndex === 'name'">
            <div class="flex items-center">
              <a-avatar :style="{ backgroundColor: getAvatarColor(record.name) }">
                {{ record.name.charAt(0) }}
              </a-avatar>
              <div class="ml-2">
                <a @click="() => handleView(record)" class="font-medium text-blue-600 hover:text-blue-800">{{ record.name }}</a>
              </div>
            </div>
          </template>
          
          <!-- 供应商编号列 -->
          <template v-if="column.dataIndex === 'code'">
            <a @click="() => handleView(record)" class="text-blue-600 hover:text-blue-800">{{ record.code }}</a>
          </template>
          
          <!-- 供应商类型列 -->
          <template v-if="column.dataIndex === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getSupplierTypeLabel(record.type) }}
            </a-tag>
          </template>
          
          <!-- 合作状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getSupplierStatusLabel(record.status) }}
            </a-tag>
          </template>
          
          <!-- 所属行业列 -->
          <template v-if="column.dataIndex === 'industry'">
            <a-tag :color="getIndustryColor(record.industry)">
              {{ getIndustryTypeLabel(record.industry) }}
            </a-tag>
          </template>
          
          <!-- 供应商地区列 -->
          <template v-if="column.dataIndex === 'region'">
            <div v-if="record.province || record.city">
              {{ getProvinceName(record.province) }}{{ record.city ? ' - ' + getCityName(record.province, record.city) : '' }}
            </div>
            <div v-else>-</div>
          </template>
          
          <!-- 负责人列 -->
          <template v-if="column.dataIndex === 'ownerName'">
            <div class="flex items-center">
              <a-avatar :size="24" class="mr-1">
                <template #icon><user-outlined /></template>
              </a-avatar>
              {{ record.ownerName }}
            </div>
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="() => handleEdit(record)">
                编辑
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="() => handleDelete(record)">
                      <delete-outlined /> 删除供应商
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 字段配置弹框 -->
    <a-modal
      v-model:visible="columnConfigVisible"
      title="字段配置"
      @ok="handleColumnConfigSave"
      @cancel="handleColumnConfigCancel"
      width="600px"
    >
      <div class="column-config-content">
        <div class="mb-4">
          <a-alert
            message="拖拽调整字段顺序，勾选控制字段显示"
            type="info"
            show-icon
            banner
          />
        </div>
        
        <div class="column-list">
          <draggable
            v-model="configColumns"
            item-key="key"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <template #item="{ element }">
              <div class="column-item">
                <div class="column-item-content">
                  <div class="drag-handle">
                    <drag-outlined />
                  </div>
                  <a-checkbox 
                    v-model:checked="element.visible"
                    :disabled="element.key === 'action'"
                  >
                    {{ element.title }}
                  </a-checkbox>
                  <div class="column-width">
                    <span class="width-label">宽度:</span>
                    <a-input-number
                      v-model:value="element.width"
                      :min="80"
                      :max="500"
                      size="small"
                      style="width: 80px"
                    />
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
        
        <div class="mt-4">
          <a-space>
            <a-button @click="resetColumnConfig">重置默认</a-button>
            <a-button @click="saveColumnTemplate">保存模板</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 新增供应商抽屉 -->
    <a-drawer
      v-model:open="supplierModalVisible"
      :title="isEditSupplier ? '编辑供应商' : '新增供应商'"
      width="700px"
      placement="right"
      @close="handleSupplierModalCancel"
    >
      <a-form
        :model="supplierForm"
        :rules="supplierFormRules"
        ref="supplierFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 供应商信息 -->
        <a-divider>供应商信息</a-divider>
        
        <a-form-item label="供应商名称" name="name">
          <a-input v-model:value="supplierForm.name" placeholder="请输入供应商公司全称" />
        </a-form-item>
        
        <a-form-item label="纳税人识别号" name="taxId">
          <a-input v-model:value="supplierForm.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="供应商类型" name="type">
          <a-select v-model:value="supplierForm.type" placeholder="请选择供应商类型">
            <a-select-option v-for="option in supplierTypeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="所属行业" name="industry">
          <a-select v-model:value="supplierForm.industry" placeholder="请选择所属行业">
            <a-select-option v-for="option in industryOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="合作状态" name="status">
          <a-select v-model:value="supplierForm.status" placeholder="请选择合作状态">
            <a-select-option v-for="option in supplierStatusOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="供应商地区" name="region">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-select 
                v-model:value="supplierForm.province" 
                placeholder="请选择省份"
                @change="handleProvinceChange"
              >
                <a-select-option v-for="option in provinceOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-select 
                v-model:value="supplierForm.city" 
                placeholder="请选择城市"
                :disabled="!supplierForm.province"
              >
                <a-select-option v-for="option in cityOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-item>
        
        <a-form-item label="供应商负责人" name="owner">
          <a-select v-model:value="supplierForm.owner" placeholder="请选择负责人">
            <a-select-option value="1">张三</a-select-option>
            <a-select-option value="2">李四</a-select-option>
            <a-select-option value="3">王五</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 联系信息 -->
        <a-divider>联系信息</a-divider>
        
        <a-form-item label="联系人" name="contact">
          <a-input v-model:value="supplierForm.contact" placeholder="请输入主要联系人姓名" />
        </a-form-item>
        
        <a-form-item label="联系电话" name="phone">
          <a-input v-model:value="supplierForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item label="电子邮箱" name="email">
          <a-input v-model:value="supplierForm.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        
        <a-form-item label="联系地址" name="address">
          <a-textarea v-model:value="supplierForm.address" placeholder="请输入详细地址" :rows="3" />
        </a-form-item>
        
        <a-form-item label="官网地址" name="website">
          <a-input v-model:value="supplierForm.website" placeholder="请输入官网地址" />
        </a-form-item>
        
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="supplierForm.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
        
        <!-- 开票信息 -->
        <a-divider>开票信息</a-divider>
        
        <a-form-item label="开票公司名称" name="invoiceCompanyName">
          <a-input v-model:value="supplierForm.invoiceCompanyName" placeholder="请输入开票公司名称" />
        </a-form-item>
        
        <a-form-item label="开票纳税人识别号" name="invoiceTaxId">
          <a-input v-model:value="supplierForm.invoiceTaxId" placeholder="请输入开票纳税人识别号" />
        </a-form-item>
        
        <a-form-item label="开户银行" name="invoiceBank">
          <a-input v-model:value="supplierForm.invoiceBank" placeholder="请输入开户银行" />
        </a-form-item>
        
        <a-form-item label="银行账号" name="invoiceBankAccount">
          <a-input v-model:value="supplierForm.invoiceBankAccount" placeholder="请输入银行账号" />
        </a-form-item>
        
        <a-form-item label="开票地址" name="invoiceAddress">
          <a-input v-model:value="supplierForm.invoiceAddress" placeholder="请输入开票地址" />
        </a-form-item>
        
        <a-form-item label="开票电话" name="invoicePhone">
          <a-input v-model:value="supplierForm.invoicePhone" placeholder="请输入开票电话" />
        </a-form-item>
        
        <a-form-item label="开票备注" name="invoiceRemark">
          <a-textarea v-model:value="supplierForm.invoiceRemark" placeholder="请输入开票备注信息" :rows="2" />
        </a-form-item>
      </a-form>
      
      <template #footer>
        <div class="text-right">
          <a-space>
            <a-button @click="handleSupplierModalCancel">取消</a-button>
            <a-button type="primary" @click="handleSupplierSubmit" :loading="supplierSubmitting">
              {{ isEditSupplier ? '更新' : '保存' }}
            </a-button>
          </a-space>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import draggable from 'vuedraggable';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined, 
  UserOutlined, 
  DeleteOutlined, 
  UpOutlined, 
  DownOutlined,
  ExportOutlined,
  SettingOutlined,
  DragOutlined
} from '@ant-design/icons-vue';
import { useSupplierStore } from '@/stores/supplier';
import type { SupplierQueryParams, SupplierFormData } from '@/types/supplier';
import { CustomerType, IndustryType, SupplierStatus } from '@/constants/customer';
import { 
  getSupplierTypeOptions, 
  getSupplierStatusOptions, 
  getIndustryTypeOptions,
  getSupplierTypeLabel,
  getSupplierStatusLabel,
  getIndustryTypeLabel,
  PROVINCE_OPTIONS,
  CITY_OPTIONS
} from '@/constants/customer';

const router = useRouter();
const supplierStore = useSupplierStore();

// 搜索表单
const searchForm = reactive<SupplierQueryParams>({
  keyword: '',
  type: undefined,
  status: undefined,
  industry: undefined,
  current: 1,
  pageSize: 10,
});

// 表格列配置
const columns = [
  {
    title: '供应商编号',
    dataIndex: 'code',
    key: 'code',
    width: 140,
  },
  {
    title: '供应商名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true,
  },
  {
    title: '供应商类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    filters: [
      { text: '企业', value: 'ENTERPRISE' },
      { text: '个体工商户', value: 'INDIVIDUAL' },
      { text: '政府机构', value: 'GOVERNMENT' },
      { text: '其他', value: 'OTHER' },
    ],
  },
  {
    title: '合作状态',
    dataIndex: 'status',
    key: 'status',
    width: 120,
    filters: [
      { text: '新建', value: 'NEW' },
      { text: '合格', value: 'QUALIFIED' },
      { text: '合作中', value: 'COOPERATING' },
      { text: '暂停', value: 'SUSPENDED' },
      { text: '终止', value: 'TERMINATED' },
    ],
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    key: 'contact',
    width: 120,
  },
  {
    title: '联系方式',
    dataIndex: 'phone',
    key: 'phone',
    width: 140,
  },
  {
    title: '所属行业',
    dataIndex: 'industry',
    key: 'industry',
    width: 140,
    filters: [
      { text: '政府机构', value: 'I01' },
      { text: '金融服务', value: 'I02' },
      { text: '信息技术/互联网', value: 'I03' },
      { text: '制造与工业', value: 'I04' },
      { text: '零售与消费品', value: 'I05' },
      { text: '能源与公用事业', value: 'I06' },
      { text: '交通与物流', value: 'I07' },
      { text: '医疗与健康', value: 'I08' },
      { text: '教育与科研', value: 'I09' },
      { text: '房地产与建筑', value: 'I10' },
      { text: '专业服务', value: 'I11' },
      { text: '农林牧渔', value: 'I12' },
      { text: '其他/未分类', value: 'I13' },
    ],
  },
  {
    title: '供应商地区',
    dataIndex: 'region',
    key: 'region',
    width: 150,
  },
  {
    title: '负责人',
    dataIndex: 'ownerName',
    key: 'ownerName',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 150,
  },
];

// 状态和数据
const loading = computed(() => supplierStore.loading);
const suppliers = computed(() => supplierStore.suppliers);

// 字段配置相关状态
const columnConfigVisible = ref(false);
const configColumns = ref([]);

// 初始化配置列
const initConfigColumns = () => {
  configColumns.value = columns.map(col => ({
    ...col,
    visible: true
  }));
};

// 可见列计算属性
const visibleColumns = computed(() => {
  return configColumns.value.filter(col => col.visible);
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 供应商表单相关
const supplierModalVisible = ref(false);
const supplierFormRef = ref();
const isEditSupplier = ref(false);
const supplierSubmitting = ref(false);

// 供应商表单数据
const supplierForm = reactive<SupplierFormData>({
  id: '',
  name: '',
  taxId: '',
  type: CustomerType.ENTERPRISE,
  industry: IndustryType.INFORMATION_TECHNOLOGY,
  status: SupplierStatus.NEW,
  owner: '',
  province: '',
  city: '',
  contact: '',
  phone: '',
  email: '',
  address: '',
  website: '',
  remark: '',
  invoiceCompanyName: '',
  invoiceTaxId: '',
  invoiceBank: '',
  invoiceBankAccount: '',
  invoiceAddress: '',
  invoicePhone: '',
  invoiceRemark: '',
});

// 供应商表单验证规则
const supplierFormRules = {
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { min: 2, max: 100, message: '供应商名称长度为2-100个字符', trigger: 'blur' }
  ],
  taxId: [
    { pattern: /^[0-9A-Z]{15,20}$/, message: '请输入正确的纳税人识别号', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择供应商类型', trigger: 'change' }
  ],
  industry: [
    { required: true, message: '请选择所属行业', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择合作状态', trigger: 'change' }
  ],
  contact: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  owner: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
};

// 选项数据
const supplierTypeOptions = ref(getSupplierTypeOptions());
const supplierStatusOptions = ref(getSupplierStatusOptions());
const industryOptions = ref(getIndustryTypeOptions());
const provinceOptions = ref(PROVINCE_OPTIONS);
const cityOptions = ref<Array<{ label: string; value: string }>>([]);

// 重置供应商表单
const resetSupplierForm = () => {
  Object.assign(supplierForm, {
    id: '',
    name: '',
    code: '',
    type: CustomerType.ENTERPRISE,
    status: SupplierStatus.NEW,
    industry: IndustryType.INFORMATION_TECHNOLOGY,
    owner: '',
    province: '',
    city: '',
    contact: '',
    phone: '',
    email: '',
    address: '',
    website: '',
    remark: '',
    taxId: '',
    invoiceCompanyName: '',
    invoiceTaxId: '',
    invoiceBank: '',
    invoiceBankAccount: '',
    invoiceAddress: '',
    invoicePhone: '',
    invoiceRemark: '',
  });
  cityOptions.value = [];
};

// 供应商抽屉取消
const handleSupplierModalCancel = () => {
  supplierModalVisible.value = false;
  resetSupplierForm();
};

// 省份变化处理
const handleProvinceChange = (province: string) => {
  supplierForm.city = '';
  cityOptions.value = CITY_OPTIONS[province] || [];
};

// 搜索功能
const handleSearch = () => {
  pagination.current = 1;
  fetchSuppliers();
};

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = '';
  searchForm.type = undefined;
  searchForm.status = undefined;
  searchForm.industry = undefined;
  searchForm.owner = undefined;
  searchForm.dateRange = undefined;
  handleSearch();
};

// 高级搜索展开/收起
const showAdvanced = ref(false);
const toggleAdvancedSearch = () => {
  showAdvanced.value = !showAdvanced.value;
};

// 获取头像颜色
const getAvatarColor = (name: string) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#1890ff', '#722ed1', '#fa541c'];
  const index = name.charCodeAt(0) % colors.length;
  return colors[index];
};

// 获取供应商类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'ENTERPRISE': 'blue',
    'INDIVIDUAL': 'green',
    'GOVERNMENT': 'purple',
    'OTHER': 'orange'
  };
  return colorMap[type] || 'default';
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'NEW': 'blue',
    'QUALIFIED': 'green',
    'COOPERATING': 'cyan',
    'SUSPENDED': 'orange',
    'TERMINATED': 'red'
  };
  return colorMap[status] || 'default';
};

// 获取行业颜色
const getIndustryColor = (industry: string) => {
  const colorMap: Record<string, string> = {
    'I01': 'red',
    'I02': 'orange',
    'I03': 'yellow',
    'I04': 'green',
    'I05': 'cyan',
    'I06': 'blue',
    'I07': 'purple',
    'I08': 'magenta',
    'I09': 'pink',
    'I10': 'lime',
    'I11': 'gold',
    'I12': 'volcano',
    'I13': 'geekblue'
  };
  return colorMap[industry] || 'default';
};

// 获取省份名称
const getProvinceName = (province: string) => {
  const option = provinceOptions.value.find(item => item.value === province);
  return option?.label || province;
};

// 获取城市名称
const getCityName = (province: string, city: string) => {
  const cityOptions = CITY_OPTIONS[province] || [];
  const option = cityOptions.find(item => item.value === city);
  return option?.label || city;
};

// 新增供应商
const handleAddSupplier = () => {
  isEditSupplier.value = false;
  resetSupplierForm();
  supplierModalVisible.value = true;
};

// 导出供应商数据
const handleExport = () => {
  message.success('供应商数据导出成功');
};

// 供应商表单提交
const handleSupplierSubmit = async () => {
  try {
    supplierSubmitting.value = true;
    
    // 表单验证
    await supplierFormRef.value.validate();
    
    const supplierData = {
      ...supplierForm,
    };
    
    let result;
    if (isEditSupplier.value) {
      result = await supplierStore.updateSupplier(supplierForm.id, supplierData);
    } else {
      result = await supplierStore.createSupplier(supplierData);
    }
    
    if (result) {
      message.success(`供应商${isEditSupplier.value ? '更新' : '创建'}成功`);
      supplierModalVisible.value = false;
      fetchSuppliers();
    }
  } catch (error) {
    console.error('保存供应商信息失败', error);
  } finally {
    supplierSubmitting.value = false;
  }
};

// 获取供应商列表
const fetchSuppliers = async () => {
  try {
    const params = {
      ...searchForm,
      current: pagination.current,
      pageSize: pagination.pageSize,
    };
    
    const result = await supplierStore.fetchSuppliers(params);
    pagination.total = result.total;
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    message.error('获取供应商列表失败');
  }
};

// 查看供应商
const handleView = (record: any) => {
  router.push(`/supplier/detail/${record.id}`);
};

// 编辑供应商
const handleEdit = (record: any) => {
  isEditSupplier.value = true;
  
  // 填充表单数据
  supplierForm.id = record.id;
  supplierForm.name = record.name;
  supplierForm.taxId = record.taxId;
  supplierForm.type = record.type;
  supplierForm.industry = record.industry;
  supplierForm.status = record.status;
  supplierForm.owner = record.owner;
  supplierForm.province = record.province || '';
  supplierForm.city = record.city || '';
  supplierForm.contact = record.contact;
  supplierForm.phone = record.phone;
  supplierForm.email = record.email;
  supplierForm.address = record.address;
  supplierForm.website = record.website || '';
  supplierForm.remark = record.remark;
  supplierForm.invoiceCompanyName = record.invoiceCompanyName || '';
  supplierForm.invoiceTaxId = record.invoiceTaxId || '';
  supplierForm.invoiceBank = record.invoiceBank || '';
  supplierForm.invoiceBankAccount = record.invoiceBankAccount || '';
  supplierForm.invoiceAddress = record.invoiceAddress || '';
  supplierForm.invoicePhone = record.invoicePhone || '';
  supplierForm.invoiceRemark = record.invoiceRemark || '';
  
  // 如果有省份，加载对应的城市选项
  if (record.province) {
    handleProvinceChange(record.province);
  }
  
  supplierModalVisible.value = true;
};

// 删除供应商
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '删除确认',
    content: `确定要删除供应商"${record.name}"吗？此操作不可恢复。`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const success = await supplierStore.deleteSupplier(record.id);
      if (success) {
        fetchSuppliers();
      }
    }
  });
};

// 处理表格变化
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchSuppliers();
};

// 字段配置相关方法
const showColumnConfig = () => {
  columnConfigVisible.value = true;
};

const handleColumnConfigSave = () => {
  // 保存配置到本地存储
  localStorage.setItem('supplier-columns-config', JSON.stringify(configColumns.value));
  message.success('字段配置已保存');
  columnConfigVisible.value = false;
};

const handleColumnConfigCancel = () => {
  // 重新加载配置
  loadColumnConfig();
  columnConfigVisible.value = false;
};

const resetColumnConfig = () => {
  initConfigColumns();
  message.success('已重置为默认配置');
};

const saveColumnTemplate = () => {
  // 保存为模板
  localStorage.setItem('supplier-columns-template', JSON.stringify(configColumns.value));
  message.success('已保存为模板');
};

const loadColumnConfig = () => {
  const saved = localStorage.getItem('supplier-columns-config');
  if (saved) {
    try {
      const savedConfig = JSON.parse(saved);
      configColumns.value = columns.map(col => {
        const savedCol = savedConfig.find(saved => saved.key === col.key);
        return {
          ...col,
          visible: savedCol ? savedCol.visible : true,
          width: savedCol ? savedCol.width : col.width
        };
      });
    } catch (error) {
      console.error('加载字段配置失败:', error);
      initConfigColumns();
    }
  } else {
    initConfigColumns();
  }
};

const onDragStart = () => {
  // 拖拽开始
};

const onDragEnd = () => {
  // 拖拽结束，可以在这里保存顺序
};

// 初始化
onMounted(() => {
  // 初始化字段配置
  loadColumnConfig();
  fetchSuppliers();
});
</script>

<style scoped>
.supplier-list-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.search-input {
  width: 100%;
}

.advanced-search {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.advanced-search.expanded {
  max-height: 1000px;
}

.form-section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #1890ff;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

@media (max-width: 768px) {
  .a-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* 字段配置样式 */
.column-config-content {
  .column-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 8px;
  }
  
  .column-item {
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .column-item-content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 4px;
    cursor: move;
    
    &:hover {
      background: #f0f0f0;
    }
  }
  
  .drag-handle {
    margin-right: 8px;
    color: #999;
    cursor: move;
  }
  
  .column-width {
    margin-left: auto;
    display: flex;
    align-items: center;
    
    .width-label {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }
}
</style>
