<template>
  <div class="supplier-form">
    <a-card>
      <template #title>
        <div class="form-header">
          <ArrowLeftOutlined @click="handleBack" class="back-icon" />
          <span>{{ isEdit ? '编辑供应商' : '新增供应商' }}</span>
        </div>
      </template>

      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="handleSubmit"
      >
        <a-row :gutter="32">
          <!-- 基本信息 -->
          <a-col :span="12">
            <a-divider orientation="left">基本信息</a-divider>
            
            <a-form-item label="供应商名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入供应商名称" />
            </a-form-item>

            <a-form-item label="供应商类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择供应商类型">
                <a-select-option
                  v-for="option in supplierTypeOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="合作状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择合作状态">
                <a-select-option
                  v-for="option in supplierStatusOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="所属行业" name="industry">
              <a-select v-model:value="formData.industry" placeholder="请选择所属行业">
                <a-select-option
                  v-for="option in industryOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="纳税人识别号" name="taxId">
              <a-input v-model:value="formData.taxId" placeholder="请输入纳税人识别号" />
            </a-form-item>

            <a-form-item label="官网地址" name="website">
              <a-input v-model:value="formData.website" placeholder="请输入官网地址" />
            </a-form-item>
          </a-col>

          <!-- 联系信息 -->
          <a-col :span="12">
            <a-divider orientation="left">联系信息</a-divider>
            
            <a-form-item label="联系人" name="contact">
              <a-input v-model:value="formData.contact" placeholder="请输入联系人姓名" />
            </a-form-item>

            <a-form-item label="联系电话" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
            </a-form-item>

            <a-form-item label="电子邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入电子邮箱" />
            </a-form-item>

            <a-form-item label="所在省份" name="province">
              <a-select 
                v-model:value="formData.province" 
                placeholder="请选择省份"
                @change="handleProvinceChange"
              >
                <a-select-option
                  v-for="province in provinceOptions"
                  :key="province.value"
                  :value="province.value"
                >
                  {{ province.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="所在城市" name="city">
              <a-select 
                v-model:value="formData.city" 
                placeholder="请选择城市"
                :disabled="!formData.province"
              >
                <a-select-option
                  v-for="city in cityOptions"
                  :key="city.value"
                  :value="city.value"
                >
                  {{ city.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="详细地址" name="address">
              <a-textarea
                v-model:value="formData.address"
                placeholder="请输入详细地址"
                :rows="2"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 其他信息 -->
        <a-divider orientation="left">其他信息</a-divider>
        <a-row :gutter="32">
          <a-col :span="12">
            <a-form-item label="负责人" name="owner">
              <a-input v-model:value="formData.owner" placeholder="请输入负责人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商来源" name="source">
              <a-select v-model:value="formData.source" placeholder="请选择供应商来源">
                <a-select-option
                  v-for="option in supplierSourceOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="32">
          <a-col :span="12">
            <a-form-item label="供应商评级" name="rating">
              <a-select v-model:value="formData.rating" placeholder="请选择供应商评级">
                <a-select-option
                  v-for="option in supplierRatingOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商能力" name="capability">
              <a-select
                v-model:value="formData.capability"
                mode="multiple"
                placeholder="请选择供应商能力"
              >
                <a-select-option
                  v-for="option in supplierCapabilityOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>

        <!-- 表单操作 -->
        <a-form-item :wrapper-col="{ offset: 3, span: 21 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              {{ isEdit ? '更新' : '创建' }}
            </a-button>
            <a-button @click="handleBack">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { useSupplierStore } from '@/stores/supplier';
import type { SupplierFormData } from '@/types/supplier';
import { SupplierSource, SupplierRating, SupplierCapability } from '@/types/supplier';
import {
  getSupplierTypeOptions,
  getSupplierStatusOptions,
  getIndustryTypeOptions,
  PROVINCE_OPTIONS,
  getCitiesByProvince,
  CustomerType,
  SupplierStatus,
  IndustryType
} from '@/constants/customer';

const router = useRouter();
const route = useRoute();
const supplierStore = useSupplierStore();

const formRef = ref();
const loading = ref(false);

// 是否为编辑模式
const isEdit = computed(() => !!route.params.id);
const supplierId = computed(() => route.params.id as string);

// 表单数据
const formData = reactive<SupplierFormData>({
  name: '',
  type: CustomerType.ENTERPRISE,
  status: SupplierStatus.NEW,
  industry: IndustryType.INFORMATION_TECHNOLOGY,
  taxId: '',
  contact: '',
  phone: '',
  email: '',
  address: '',
  province: '',
  city: '',
  website: '',
  remark: '',
  owner: '',
  source: SupplierSource.OTHER,
  rating: SupplierRating.B,
  capability: [],
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择合作状态', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  contact: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  owner: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
};

// 选项数据
const supplierTypeOptions = getSupplierTypeOptions();
const supplierStatusOptions = getSupplierStatusOptions();
const industryOptions = getIndustryTypeOptions();
const provinceOptions = PROVINCE_OPTIONS;

// 供应商来源选项
const supplierSourceOptions = [
  { label: '推荐', value: SupplierSource.REFERRAL },
  { label: '官网', value: SupplierSource.WEBSITE },
  { label: '展会', value: SupplierSource.EXHIBITION },
  { label: '合作伙伴', value: SupplierSource.PARTNER },
  { label: '广告', value: SupplierSource.ADVERTISEMENT },
  { label: '其他', value: SupplierSource.OTHER },
];

// 供应商评级选项
const supplierRatingOptions = [
  { label: 'A级 - 优秀', value: SupplierRating.A },
  { label: 'B级 - 良好', value: SupplierRating.B },
  { label: 'C级 - 一般', value: SupplierRating.C },
  { label: 'D级 - 较差', value: SupplierRating.D },
];

// 供应商能力选项
const supplierCapabilityOptions = [
  { label: '生产制造', value: SupplierCapability.MANUFACTURING },
  { label: '服务提供', value: SupplierCapability.SERVICE },
  { label: '技术支持', value: SupplierCapability.TECHNOLOGY },
  { label: '物流配送', value: SupplierCapability.LOGISTICS },
  { label: '咨询服务', value: SupplierCapability.CONSULTING },
  { label: '其他', value: SupplierCapability.OTHER },
];

// 城市选项
const cityOptions = computed(() => {
  return formData.province ? getCitiesByProvince(formData.province) : [];
});

// 省份变化处理
const handleProvinceChange = () => {
  formData.city = '';
};

// 返回上一页
const handleBack = () => {
  router.back();
};

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true;
    if (isEdit.value) {
      await supplierStore.updateSupplier(supplierId.value, formData);
      message.success('供应商信息更新成功');
    } else {
      await supplierStore.createSupplier(formData);
      message.success('供应商创建成功');
    }
    router.push('/supplier');
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '创建失败');
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取数据
onMounted(async () => {
  if (isEdit.value) {
    try {
      loading.value = true;
      await supplierStore.fetchSupplierDetail(supplierId.value);
      const supplier = supplierStore.supplierDetail;
      if (supplier) {
        Object.assign(formData, {
          name: supplier.name,
          type: supplier.type,
          status: supplier.status,
          industry: supplier.industry,
          taxId: supplier.taxId,
          contact: supplier.contact,
          phone: supplier.phone,
          email: supplier.email,
          address: supplier.address,
          province: supplier.province,
          city: supplier.city,
          website: supplier.website,
          remark: supplier.remark,
          owner: supplier.owner,
        });
      }
    } catch (error) {
      message.error('获取供应商信息失败');
    } finally {
      loading.value = false;
    }
  }
});
</script>

<style scoped>
.supplier-form {
  padding: 24px;
}

.form-header {
  display: flex;
  align-items: center;
}

.back-icon {
  margin-right: 8px;
  cursor: pointer;
  font-size: 16px;
}

.back-icon:hover {
  color: #1890ff;
}
</style> 