<!-- src/components/project/MilestoneFormModal.vue -->
<template>
  <a-modal
    :visible="visible"
    :title="milestoneId ? '编辑里程碑' : '新建里程碑'"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="里程碑名称" name="name">
        <a-input v-model:value="formState.name" placeholder="请输入里程碑名称" />
      </a-form-item>

      <a-form-item label="里程碑描述" name="description">
        <a-textarea
          v-model:value="formState.description"
          :rows="4"
          placeholder="请输入里程碑描述"
        />
      </a-form-item>

      <a-form-item label="计划完成日期" name="dueDate">
        <a-date-picker
          v-model:value="formState.dueDate"
          style="width: 100%"
          :disabledDate="disabledDate"
        />
      </a-form-item>

      <a-form-item label="当前状态" name="status">
        <a-select
          v-model:value="formState.status"
          placeholder="请选择状态"
          :options="statusOptions"
        />
      </a-form-item>

      <a-form-item label="完成进度" name="progress">
        <a-slider v-model:value="formState.progress" :step="5" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import {
  MilestoneStatus,
  type ProjectMilestone,
} from '@/types/project';
import {
  createProjectMilestone,
} from '@/api/project';

const props = defineProps<{
  visible: boolean;
  projectId: string;
  milestoneId?: string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 表单相关
const formRef = ref<FormInstance>();
const loading = ref(false);
const formState = reactive<{
  name: string;
  description: string;
  dueDate: Dayjs | null;
  status: MilestoneStatus;
  progress: number;
}>({
  name: '',
  description: '',
  dueDate: null,
  status: MilestoneStatus.PENDING,
  progress: 0,
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入里程碑名称', trigger: 'blur' }],
  dueDate: [{ required: true, message: '请选择计划完成日期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
};

// 状态选项
const statusOptions = [
  { label: '未开始', value: MilestoneStatus.PENDING },
  { label: '进行中', value: MilestoneStatus.IN_PROGRESS },
  { label: '已完成', value: MilestoneStatus.COMPLETED },
  { label: '已延期', value: MilestoneStatus.DELAYED },
];

// 日期选择限制
function disabledDate(current: Dayjs) {
  return current && current < dayjs().startOf('day');
}

// 处理取消
function handleCancel() {
  formRef.value?.resetFields();
  emit('update:visible', false);
}

// 处理提交
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;

    const milestoneData = {
      name: formState.name,
      description: formState.description,
      dueDate: formState.dueDate?.format('YYYY-MM-DD'),
      status: formState.status,
      progress: formState.progress,
    };

    await createProjectMilestone(props.projectId, milestoneData);
    message.success('里程碑创建成功');
    handleCancel();
    emit('success');
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message);
    } else {
      message.error('表单验证失败');
    }
  } finally {
    loading.value = false;
  }
}
</script> 