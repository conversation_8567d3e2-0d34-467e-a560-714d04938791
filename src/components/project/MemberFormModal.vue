<!-- src/components/project/MemberFormModal.vue -->
<template>
  <a-modal
    :visible="visible"
    title="添加项目成员"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="成员" name="userId">
        <a-select
          v-model:value="formState.userId"
          placeholder="请选择成员"
          :options="userOptions"
          :loading="loadingUsers"
        />
      </a-form-item>

      <a-form-item label="角色" name="role">
        <a-select
          v-model:value="formState.role"
          placeholder="请选择角色"
          :options="roleOptions"
        />
      </a-form-item>

      <a-form-item label="工作量" name="workload">
        <a-input-number
          v-model:value="formState.workload"
          :min="0"
          :max="100"
          :step="5"
          :formatter="value => `${value}%`"
          :parser="value => value!.replace('%', '')"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="加入日期" name="joinDate">
        <a-date-picker
          v-model:value="formState.joinDate"
          style="width: 100%"
          :disabledDate="disabledDate"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import {
  MemberRole,
  type ProjectMember,
} from '@/types/project';
import {
  addProjectMember,
  getAvailableUsers,
} from '@/api/project';

const props = defineProps<{
  visible: boolean;
  projectId: string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 表单相关
const formRef = ref<FormInstance>();
const loading = ref(false);
const formState = reactive<{
  userId: string;
  role: MemberRole;
  workload: number;
  joinDate: Dayjs | null;
}>({
  userId: '',
  role: MemberRole.DEVELOPER,
  workload: 100,
  joinDate: dayjs(),
});

// 表单验证规则
const rules = {
  userId: [{ required: true, message: '请选择成员', trigger: 'change' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  workload: [{ required: true, message: '请输入工作量', trigger: 'change' }],
  joinDate: [{ required: true, message: '请选择加入日期', trigger: 'change' }],
};

// 角色选项
const roleOptions = [
  { label: '项目经理', value: MemberRole.MANAGER },
  { label: '开发人员', value: MemberRole.DEVELOPER },
  { label: '设计师', value: MemberRole.DESIGNER },
  { label: '测试人员', value: MemberRole.TESTER },
  { label: '顾问', value: MemberRole.CONSULTANT },
];

// 用户选项
const loadingUsers = ref(false);
const userOptions = ref<{ label: string; value: string; }[]>([]);

// 获取可用用户
async function fetchUsers() {
  loadingUsers.value = true;
  try {
    const users = await getAvailableUsers();
    userOptions.value = users.map(user => ({
      label: user.name,
      value: user.id,
    }));
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    loadingUsers.value = false;
  }
}

// 日期选择限制
function disabledDate(current: Dayjs) {
  return current && current < dayjs().startOf('day');
}

// 处理取消
function handleCancel() {
  formRef.value?.resetFields();
  emit('update:visible', false);
}

// 处理提交
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;

    const memberData = {
      userId: formState.userId,
      role: formState.role,
      workload: formState.workload,
      joinDate: formState.joinDate?.format('YYYY-MM-DD'),
    };

    await addProjectMember(props.projectId, memberData);
    message.success('成员添加成功');
    handleCancel();
    emit('success');
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message);
    } else {
      message.error('表单验证失败');
    }
  } finally {
    loading.value = false;
  }
}
</script> 