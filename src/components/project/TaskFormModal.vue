<template>
  <a-modal
    :visible="visible"
    :title="taskId ? '编辑任务' : '新建任务'"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="任务名称" name="name">
        <a-input v-model:value="formState.name" placeholder="请输入任务名称" />
      </a-form-item>

      <a-form-item label="任务描述" name="description">
        <a-textarea
          v-model:value="formState.description"
          :rows="4"
          placeholder="请输入任务描述"
        />
      </a-form-item>

      <a-form-item label="负责人" name="assigneeId">
        <a-select
          v-model:value="formState.assigneeId"
          placeholder="请选择负责人"
          :options="memberOptions"
          :loading="loadingMembers"
        />
      </a-form-item>

      <a-form-item label="优先级" name="priority">
        <a-select
          v-model:value="formState.priority"
          placeholder="请选择优先级"
          :options="priorityOptions"
        />
      </a-form-item>

      <a-form-item label="开始日期" name="startDate">
        <a-date-picker
          v-model:value="formState.startDate"
          style="width: 100%"
          :disabledDate="disabledStartDate"
        />
      </a-form-item>

      <a-form-item label="截止日期" name="dueDate">
        <a-date-picker
          v-model:value="formState.dueDate"
          style="width: 100%"
          :disabledDate="disabledDueDate"
        />
      </a-form-item>

      <a-form-item label="进度" name="progress">
        <a-slider v-model:value="formState.progress" :step="5" />
      </a-form-item>

      <a-form-item v-if="taskId" label="状态" name="status">
        <a-select
          v-model:value="formState.status"
          placeholder="请选择任务状态"
          :options="statusOptions"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import {
  ProjectTask,
  TaskPriority,
  TaskStatus,
  type ProjectMember,
} from '@/types/project';
import {
  createProjectTask,
  updateProjectTask,
  getProjectMembers,
  getProjectTaskDetail,
} from '@/api/project';

const props = defineProps<{
  visible: boolean;
  projectId: string;
  taskId?: string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 表单相关
const formRef = ref<FormInstance>();
const loading = ref(false);
const formState = reactive<{
  name: string;
  description: string;
  assigneeId: string;
  priority: TaskPriority;
  startDate: Dayjs | null;
  dueDate: Dayjs | null;
  progress: number;
  status?: TaskStatus;
}>({
  name: '',
  description: '',
  assigneeId: '',
  priority: TaskPriority.MEDIUM,
  startDate: null,
  dueDate: null,
  progress: 0,
  status: undefined,
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  assigneeId: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  dueDate: [{ required: true, message: '请选择截止日期', trigger: 'change' }],
  status: [{ required: true, message: '请选择任务状态', trigger: 'change' }],
};

// 优先级选项
const priorityOptions = [
  { label: '低', value: TaskPriority.LOW },
  { label: '中', value: TaskPriority.MEDIUM },
  { label: '高', value: TaskPriority.HIGH },
  { label: '紧急', value: TaskPriority.URGENT },
];

// 状态选项
const statusOptions = [
  { label: '待办', value: TaskStatus.TODO },
  { label: '进行中', value: TaskStatus.IN_PROGRESS },
  { label: '已完成', value: TaskStatus.COMPLETED },
];

// 成员选项
const loadingMembers = ref(false);
const members = ref<ProjectMember[]>([]);
const memberOptions = computed(() => 
  members.value.map(member => ({
    label: member.userName,
    value: member.userId,
  }))
);

// 获取项目成员
async function fetchMembers() {
  loadingMembers.value = true;
  try {
    members.value = await getProjectMembers(props.projectId);
  } catch (error) {
    console.error('获取项目成员失败:', error);
    message.error('获取项目成员失败');
  } finally {
    loadingMembers.value = false;
  }
}

// 获取任务详情
async function fetchTaskDetail() {
  if (!props.taskId) return;
  
  loading.value = true;
  try {
    const task = await getProjectTaskDetail(props.projectId, props.taskId);
    
    // 填充表单数据
    formState.name = task.name;
    formState.description = task.description;
    formState.assigneeId = task.assigneeId;
    formState.priority = task.priority;
    formState.startDate = task.startDate ? dayjs(task.startDate) : null;
    formState.dueDate = task.dueDate ? dayjs(task.dueDate) : null;
    formState.progress = task.progress;
    formState.status = task.status;
  } catch (error) {
    console.error('获取任务详情失败:', error);
    message.error('获取任务详情失败');
  } finally {
    loading.value = false;
  }
}

// 日期选择限制
function disabledStartDate(current: Dayjs) {
  return current && current < dayjs().startOf('day');
}

function disabledDueDate(current: Dayjs) {
  return (
    current &&
    (current < dayjs().startOf('day') ||
      (formState.startDate && current < formState.startDate))
  );
}

// 监听对话框显示状态
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      await fetchMembers();
      if (props.taskId) {
        await fetchTaskDetail();
      } else {
        // 重置表单
        formRef.value?.resetFields();
        formState.name = '';
        formState.description = '';
        formState.assigneeId = '';
        formState.priority = TaskPriority.MEDIUM;
        formState.startDate = null;
        formState.dueDate = null;
        formState.progress = 0;
        formState.status = undefined;
      }
    }
  }
);

// 处理取消
function handleCancel() {
  formRef.value?.resetFields();
  emit('update:visible', false);
}

// 处理提交
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;

    const taskData = {
      name: formState.name,
      description: formState.description,
      assigneeId: formState.assigneeId,
      priority: formState.priority,
      startDate: formState.startDate?.format('YYYY-MM-DD'),
      dueDate: formState.dueDate?.format('YYYY-MM-DD'),
      progress: formState.progress,
      status: props.taskId ? formState.status : TaskStatus.TODO,
    };

    if (props.taskId) {
      // 更新任务
      await updateProjectTask(props.projectId, props.taskId, taskData);
      message.success('任务更新成功');
    } else {
      // 创建任务
      await createProjectTask(props.projectId, taskData);
      message.success('任务创建成功');
    }
    
    handleCancel();
    emit('success');
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message);
    } else {
      message.error('表单验证失败');
    }
  } finally {
    loading.value = false;
  }
}
</script> 