<template>
  <a-modal
    :visible="visible"
    :title="riskId ? '编辑风险' : '新建风险'"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="风险名称" name="name">
        <a-input v-model:value="formState.name" placeholder="请输入风险名称" />
      </a-form-item>

      <a-form-item label="风险描述" name="description">
        <a-textarea
          v-model:value="formState.description"
          :rows="4"
          placeholder="请输入风险描述"
        />
      </a-form-item>

      <a-form-item label="风险等级" name="level">
        <a-select
          v-model:value="formState.level"
          placeholder="请选择风险等级"
          :options="levelOptions"
        />
      </a-form-item>

      <a-form-item label="当前状态" name="status">
        <a-select
          v-model:value="formState.status"
          placeholder="请选择状态"
          :options="statusOptions"
        />
      </a-form-item>

      <a-form-item label="负责人" name="ownerId">
        <a-select
          v-model:value="formState.ownerId"
          placeholder="请选择负责人"
          :options="memberOptions"
          :loading="loadingMembers"
        />
      </a-form-item>

      <a-form-item label="应对措施" name="mitigation">
        <a-textarea
          v-model:value="formState.mitigation"
          :rows="4"
          placeholder="请输入应对措施"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import {
  RiskLevel,
  RiskStatus,
  type ProjectMember,
} from '@/types/project';
import {
  createProjectRisk,
  getProjectMembers,
} from '@/api/project';

const props = defineProps<{
  visible: boolean;
  projectId: string;
  riskId?: string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 表单相关
const formRef = ref<FormInstance>();
const loading = ref(false);
const formState = reactive<{
  name: string;
  description: string;
  level: RiskLevel;
  status: RiskStatus;
  ownerId: string;
  mitigation: string;
}>({
  name: '',
  description: '',
  level: RiskLevel.MEDIUM,
  status: RiskStatus.IDENTIFIED,
  ownerId: '',
  mitigation: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入风险名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入风险描述', trigger: 'blur' }],
  level: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  ownerId: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  mitigation: [{ required: true, message: '请输入应对措施', trigger: 'blur' }],
};

// 风险等级选项
const levelOptions = [
  { label: '低风险', value: RiskLevel.LOW },
  { label: '中风险', value: RiskLevel.MEDIUM },
  { label: '高风险', value: RiskLevel.HIGH },
  { label: '严重风险', value: RiskLevel.CRITICAL },
];

// 状态选项
const statusOptions = [
  { label: '已识别', value: RiskStatus.IDENTIFIED },
  { label: '分析中', value: RiskStatus.ANALYZING },
  { label: '缓解中', value: RiskStatus.MITIGATING },
  { label: '已解决', value: RiskStatus.RESOLVED },
  { label: '已接受', value: RiskStatus.ACCEPTED },
];

// 成员选项
const loadingMembers = ref(false);
const members = ref<ProjectMember[]>([]);
const memberOptions = computed(() => 
  members.value.map(member => ({
    label: member.userName,
    value: member.userId,
  }))
);

// 获取项目成员
async function fetchMembers() {
  loadingMembers.value = true;
  try {
    members.value = await getProjectMembers(props.projectId);
  } catch (error) {
    console.error('获取项目成员失败:', error);
    message.error('获取项目成员失败');
  } finally {
    loadingMembers.value = false;
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      fetchMembers();
    }
  }
);

// 处理取消
function handleCancel() {
  formRef.value?.resetFields();
  emit('update:visible', false);
}

// 处理提交
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;

    const riskData = {
      name: formState.name,
      description: formState.description,
      level: formState.level,
      status: formState.status,
      ownerId: formState.ownerId,
      mitigation: formState.mitigation,
    };

    await createProjectRisk(props.projectId, riskData);
    message.success('风险创建成功');
    handleCancel();
    emit('success');
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message);
    } else {
      message.error('表单验证失败');
    }
  } finally {
    loading.value = false;
  }
}
</script> 