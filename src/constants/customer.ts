// 客户类型枚举（基于文档规范）
export enum CustomerType {
  ENTERPRISE = 'C', // 企业客户
  GOVERNMENT = 'G', // 政府客户
}

// 行业分类枚举（基于文档规范）
export enum IndustryType {
  GOVERNMENT_AGENCIES = 'I01', // 政府机构
  FINANCIAL_SERVICES = 'I02', // 金融服务
  INFORMATION_TECHNOLOGY = 'I03', // 信息技术/互联网
  MANUFACTURING_INDUSTRIAL = 'I04', // 制造与工业
  RETAIL_CONSUMER_GOODS = 'I05', // 零售与消费品
  ENERGY_UTILITIES = 'I06', // 能源与公用事业
  TRANSPORTATION_LOGISTICS = 'I07', // 交通与物流
  HEALTHCARE_PHARMACEUTICALS = 'I08', // 医疗与健康
  EDUCATION_RESEARCH = 'I09', // 教育与科研
  REAL_ESTATE_CONSTRUCTION = 'I10', // 房地产与建筑
  PROFESSIONAL_SERVICES = 'I11', // 专业服务
  AGRICULTURE_FORESTRY = 'I12', // 农林牧渔
  OTHER_UNCATEGORIZED = 'I13', // 其他/未分类
}

// 选项映射结构接口
export interface OptionMap {
  label: string;
  value: string;
}

// 客户类型选项映射
export const CUSTOMER_TYPE_MAP: Record<CustomerType, OptionMap> = {
  [CustomerType.ENTERPRISE]: { label: '企业', value: 'C' },
  [CustomerType.GOVERNMENT]: { label: '政府', value: 'G' },
};

// 行业分类选项映射
export const INDUSTRY_TYPE_MAP: Record<IndustryType, OptionMap> = {
  [IndustryType.GOVERNMENT_AGENCIES]: { label: '政府机构', value: 'I01' },
  [IndustryType.FINANCIAL_SERVICES]: { label: '金融服务', value: 'I02' },
  [IndustryType.INFORMATION_TECHNOLOGY]: { label: '信息技术/互联网', value: 'I03' },
  [IndustryType.MANUFACTURING_INDUSTRIAL]: { label: '制造与工业', value: 'I04' },
  [IndustryType.RETAIL_CONSUMER_GOODS]: { label: '零售与消费品', value: 'I05' },
  [IndustryType.ENERGY_UTILITIES]: { label: '能源与公用事业', value: 'I06' },
  [IndustryType.TRANSPORTATION_LOGISTICS]: { label: '交通与物流', value: 'I07' },
  [IndustryType.HEALTHCARE_PHARMACEUTICALS]: { label: '医疗与健康', value: 'I08' },
  [IndustryType.EDUCATION_RESEARCH]: { label: '教育与科研', value: 'I09' },
  [IndustryType.REAL_ESTATE_CONSTRUCTION]: { label: '房地产与建筑', value: 'I10' },
  [IndustryType.PROFESSIONAL_SERVICES]: { label: '专业服务', value: 'I11' },
  [IndustryType.AGRICULTURE_FORESTRY]: { label: '农林牧渔', value: 'I12' },
  [IndustryType.OTHER_UNCATEGORIZED]: { label: '其他/未分类', value: 'I13' },
};

// 获取客户类型选项数组
export const getCustomerTypeOptions = (): OptionMap[] => {
  return Object.values(CUSTOMER_TYPE_MAP);
};

// 获取行业分类选项数组
export const getIndustryTypeOptions = (): OptionMap[] => {
  return Object.values(INDUSTRY_TYPE_MAP);
};

// 根据值获取客户类型标签
export const getCustomerTypeLabel = (value: string): string => {
  const option = Object.values(CUSTOMER_TYPE_MAP).find(item => item.value === value);
  return option?.label || value;
};

// 根据值获取行业分类标签
export const getIndustryTypeLabel = (value: string): string => {
  const option = Object.values(INDUSTRY_TYPE_MAP).find(item => item.value === value);
  return option?.label || value;
};

// 兼容性导出（保持向后兼容）
export const CUSTOMER_TYPES = {
  ENTERPRISE: CustomerType.ENTERPRISE,
  GOVERNMENT: CustomerType.GOVERNMENT,
} as const;

export const INDUSTRY_TYPES = {
  GOVERNMENT_AGENCIES: IndustryType.GOVERNMENT_AGENCIES,
  FINANCIAL_SERVICES: IndustryType.FINANCIAL_SERVICES,
  INFORMATION_TECHNOLOGY: IndustryType.INFORMATION_TECHNOLOGY,
  MANUFACTURING_INDUSTRIAL: IndustryType.MANUFACTURING_INDUSTRIAL,
  RETAIL_CONSUMER_GOODS: IndustryType.RETAIL_CONSUMER_GOODS,
  ENERGY_UTILITIES: IndustryType.ENERGY_UTILITIES,
  TRANSPORTATION_LOGISTICS: IndustryType.TRANSPORTATION_LOGISTICS,
  HEALTHCARE_PHARMACEUTICALS: IndustryType.HEALTHCARE_PHARMACEUTICALS,
  EDUCATION_RESEARCH: IndustryType.EDUCATION_RESEARCH,
  REAL_ESTATE_CONSTRUCTION: IndustryType.REAL_ESTATE_CONSTRUCTION,
  PROFESSIONAL_SERVICES: IndustryType.PROFESSIONAL_SERVICES,
  AGRICULTURE_FORESTRY: IndustryType.AGRICULTURE_FORESTRY,
  OTHER_UNCATEGORIZED: IndustryType.OTHER_UNCATEGORIZED,
} as const;

export const CUSTOMER_TYPE_OPTIONS = getCustomerTypeOptions();
export const INDUSTRY_OPTIONS = getIndustryTypeOptions();

// 省份选项
export const PROVINCE_OPTIONS = [
  { label: '北京市', value: 'beijing' },
  { label: '上海市', value: 'shanghai' },
  { label: '天津市', value: 'tianjin' },
  { label: '重庆市', value: 'chongqing' },
  { label: '河北省', value: 'hebei' },
  { label: '山西省', value: 'shanxi' },
  { label: '辽宁省', value: 'liaoning' },
  { label: '吉林省', value: 'jilin' },
  { label: '黑龙江省', value: 'heilongjiang' },
  { label: '江苏省', value: 'jiangsu' },
  { label: '浙江省', value: 'zhejiang' },
  { label: '安徽省', value: 'anhui' },
  { label: '福建省', value: 'fujian' },
  { label: '江西省', value: 'jiangxi' },
  { label: '山东省', value: 'shandong' },
  { label: '河南省', value: 'henan' },
  { label: '湖北省', value: 'hubei' },
  { label: '湖南省', value: 'hunan' },
  { label: '广东省', value: 'guangdong' },
  { label: '海南省', value: 'hainan' },
  { label: '四川省', value: 'sichuan' },
  { label: '贵州省', value: 'guizhou' },
  { label: '云南省', value: 'yunnan' },
  { label: '陕西省', value: 'shaanxi' },
  { label: '甘肃省', value: 'gansu' },
  { label: '青海省', value: 'qinghai' },
  { label: '台湾省', value: 'taiwan' },
  { label: '内蒙古自治区', value: 'neimenggu' },
  { label: '广西壮族自治区', value: 'guangxi' },
  { label: '西藏自治区', value: 'xizang' },
  { label: '宁夏回族自治区', value: 'ningxia' },
  { label: '新疆维吾尔自治区', value: 'xinjiang' },
  { label: '香港特别行政区', value: 'hongkong' },
  { label: '澳门特别行政区', value: 'macao' },
];

// 城市选项（简化版，实际应用中可以根据省份动态加载）
export const CITY_OPTIONS: Record<string, Array<{ label: string; value: string }>> = {
  beijing: [
    { label: '东城区', value: 'dongcheng' },
    { label: '西城区', value: 'xicheng' },
    { label: '朝阳区', value: 'chaoyang' },
    { label: '丰台区', value: 'fengtai' },
    { label: '石景山区', value: 'shijingshan' },
    { label: '海淀区', value: 'haidian' },
    { label: '门头沟区', value: 'mentougou' },
    { label: '房山区', value: 'fangshan' },
    { label: '通州区', value: 'tongzhou' },
    { label: '顺义区', value: 'shunyi' },
    { label: '昌平区', value: 'changping' },
    { label: '大兴区', value: 'daxing' },
    { label: '怀柔区', value: 'huairou' },
    { label: '平谷区', value: 'pinggu' },
    { label: '密云区', value: 'miyun' },
    { label: '延庆区', value: 'yanqing' },
  ],
  shanghai: [
    { label: '黄浦区', value: 'huangpu' },
    { label: '徐汇区', value: 'xuhui' },
    { label: '长宁区', value: 'changning' },
    { label: '静安区', value: 'jingan' },
    { label: '普陀区', value: 'putuo' },
    { label: '虹口区', value: 'hongkou' },
    { label: '杨浦区', value: 'yangpu' },
    { label: '闵行区', value: 'minhang' },
    { label: '宝山区', value: 'baoshan' },
    { label: '嘉定区', value: 'jiading' },
    { label: '浦东新区', value: 'pudong' },
    { label: '金山区', value: 'jinshan' },
    { label: '松江区', value: 'songjiang' },
    { label: '青浦区', value: 'qingpu' },
    { label: '奉贤区', value: 'fengxian' },
    { label: '崇明区', value: 'chongming' },
  ],
  guangdong: [
    { label: '广州市', value: 'guangzhou' },
    { label: '深圳市', value: 'shenzhen' },
    { label: '珠海市', value: 'zhuhai' },
    { label: '汕头市', value: 'shantou' },
    { label: '佛山市', value: 'foshan' },
    { label: '韶关市', value: 'shaoguan' },
    { label: '湛江市', value: 'zhanjiang' },
    { label: '肇庆市', value: 'zhaoqing' },
    { label: '江门市', value: 'jiangmen' },
    { label: '茂名市', value: 'maoming' },
    { label: '惠州市', value: 'huizhou' },
    { label: '梅州市', value: 'meizhou' },
    { label: '汕尾市', value: 'shanwei' },
    { label: '河源市', value: 'heyuan' },
    { label: '阳江市', value: 'yangjiang' },
    { label: '清远市', value: 'qingyuan' },
    { label: '东莞市', value: 'dongguan' },
    { label: '中山市', value: 'zhongshan' },
    { label: '潮州市', value: 'chaozhou' },
    { label: '揭阳市', value: 'jieyang' },
    { label: '云浮市', value: 'yunfu' },
  ],
  // 可以继续添加其他省份的城市...
};

// 供应商相关（复用客户相关定义）
export type SupplierType = CustomerType;

// 供应商状态枚举
export enum SupplierStatus {
  NEW = 'new', // 新建
  COOPERATING = 'cooperating', // 合作中
  QUALIFIED = 'qualified', // 已认证
  SUSPENDED = 'suspended', // 暂停合作
  TERMINATED = 'terminated', // 终止合作
}

// 供应商状态选项映射
export const SUPPLIER_STATUS_MAP: Record<SupplierStatus, OptionMap> = {
  [SupplierStatus.NEW]: { label: '新建', value: 'new' },
  [SupplierStatus.COOPERATING]: { label: '合作中', value: 'cooperating' },
  [SupplierStatus.QUALIFIED]: { label: '已认证', value: 'qualified' },
  [SupplierStatus.SUSPENDED]: { label: '暂停合作', value: 'suspended' },
  [SupplierStatus.TERMINATED]: { label: '终止合作', value: 'terminated' },
};

// 供应商类型相关函数
export const getSupplierTypeOptions = (): OptionMap[] => {
  return getCustomerTypeOptions();
};

export const getSupplierTypeLabel = (value: string): string => {
  return getCustomerTypeLabel(value);
};

// 获取供应商状态选项数组
export const getSupplierStatusOptions = (): OptionMap[] => {
  return Object.values(SUPPLIER_STATUS_MAP);
};

// 根据值获取供应商状态标签
export const getSupplierStatusLabel = (value: string): string => {
  const option = Object.values(SUPPLIER_STATUS_MAP).find(item => item.value === value);
  return option?.label || value;
};

// 供应商编码生成函数（基于编码规范：V + 两位年份 + 月日 + 流水号）
export const generateSupplierCode = (type: SupplierType): string => {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2); // 获取两位年份
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 999) + 1; // 生成1-999的随机数
  const serial = random.toString().padStart(3, '0'); // 补零至3位
  
  return `V${year}${month}${day}${serial}`;
};

// 根据省份获取城市选项
export const getCitiesByProvince = (provinceValue: string): Array<{ label: string; value: string }> => {
  return CITY_OPTIONS[provinceValue] || [];
};

// 根据省份值获取省份名称
export const getProvinceName = (provinceValue: string): string => {
  const province = PROVINCE_OPTIONS.find(p => p.value === provinceValue);
  return province?.label || provinceValue;
};

// 根据城市值获取城市名称
export const getCityName = (provinceValue: string, cityValue: string): string => {
  const cities = getCitiesByProvince(provinceValue);
  const city = cities.find(c => c.value === cityValue);
  return city?.label || cityValue;
}; 