@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  --primary-color-outline: rgba(24, 144, 255, 0.2);
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --heading-color: rgba(0, 0, 0, 0.85);
  --text-color: rgba(0, 0, 0, 0.65);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --disabled-color: rgba(0, 0, 0, 0.25);
  --border-color: #d9d9d9;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  --font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 全局样式 */
body {
  font-family: var(--font-family);
  color: var(--text-color);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--primary-color-hover);
}

a:active {
  color: var(--primary-color-active);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 自定义选中样式 */
::selection {
  background: var(--primary-color-outline);
}

/* 自定义工具类 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multi-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 主题相关自定义变量 */
.bg-primary-700 {
  background-color: #1890ff;
}

.text-primary-200 {
  color: #bae7ff;
}

.text-primary-600 {
  color: #1890ff;
}

.hover\:text-primary-500:hover {
  color: #40a9ff;
}

/* 自定义卡片效果 */
.card-hover-effect {
  transition: all 0.3s;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 自定义动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(40px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease-out;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-block {
    display: block !important;
  }
  
  .mobile-flex {
    display: flex !important;
  }
}

@layer components {
  .container-fluid {
    @apply w-full px-4 mx-auto;
  }
  
  .btn {
    @apply px-4 py-2 rounded-md cursor-pointer font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
  
  .btn-default {
    @apply bg-gray-100 hover:bg-gray-200;
  }
  
  .form-input {
    @apply w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
} 