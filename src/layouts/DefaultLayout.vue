<template>
  <a-layout class="min-h-screen">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="bg-[#001529]"
    >
      <div class="h-16 flex items-center px-4 border-b border-[#ffffff1a]">
        <div class="flex items-center gap-2">
          <span class="text-2xl font-bold text-white">ERP</span>
          <span v-if="!collapsed" class="text-gray-300 text-sm">业财一体化系统</span>
        </div>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        mode="inline"
        theme="dark"
        :items="menuItems"
        @click="handleMenuClick"
      />
    </a-layout-sider>

    <a-layout>
      <!-- 头部 -->
      <a-layout-header class="bg-[#001529] px-4 flex items-center justify-between h-16">
        <menu-unfold-outlined
          v-if="collapsed"
          class="text-lg cursor-pointer text-white"
          @click="() => (collapsed = false)"
        />
        <menu-fold-outlined
          v-else
          class="text-lg cursor-pointer text-white"
          @click="() => (collapsed = true)"
        />

        <div class="flex items-center gap-4">
          <a-badge :count="5">
            <bell-outlined class="text-lg cursor-pointer text-white" />
          </a-badge>
          <a-dropdown>
            <div class="flex items-center gap-2 cursor-pointer">
              <a-avatar>{{ userInfo.name?.[0] || 'U' }}</a-avatar>
              <span class="text-white">{{ userInfo.name || '用户' }}</span>
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <user-outlined />
                  <span>个人信息</span>
                </a-menu-item>
                <a-menu-item key="settings">
                  <setting-outlined />
                  <span>系统设置</span>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <logout-outlined />
                  <span>退出登录</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区 -->
      <a-layout-content class="p-6 overflow-y-auto" style="height: calc(100vh - 64px);">
        <router-view></router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import { ref, computed, h, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import * as Icons from '@ant-design/icons-vue';
import { 
  MenuUnfoldOutlined, 
  MenuFoldOutlined, 
  UserOutlined, 
  SettingOutlined, 
  LogoutOutlined,
  BellOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { routes } from '@/router';

const router = useRouter();
const route = useRoute();

// 侧边栏折叠状态
const collapsed = ref(false);

// 用户信息
const userInfo = ref({
  name: '管理员',
  avatar: '',
});

// 根据路由生成菜单项
const generateMenuItems = (routes: any[], parentPath = '') => {
  return routes
    .filter(route => route.meta && !route.meta.hidden)
    .map(route => {
      // 构建完整路径
      const fullPath = parentPath 
        ? (route.path.startsWith('/') ? route.path : `${parentPath}/${route.path}`)
        : route.path;
        
      const menuItem: any = {
        key: fullPath,
        label: route.meta.title,
        title: route.meta.title,
      };

      // 添加图标
      if (route.meta.icon && Icons[route.meta.icon]) {
        menuItem.icon = () => h(Icons[route.meta.icon]);
      }

      // 处理子菜单
      if (route.children && route.children.length > 0) {
        const children = generateMenuItems(route.children, fullPath);
        if (children.length > 0) {
          menuItem.children = children;
        }
      }

      return menuItem;
    });
};

// 菜单项配置
const menuItems = computed(() => {
  const mainRoute = routes.find(route => route.path === '/');
  return mainRoute ? generateMenuItems(mainRoute.children || []) : [];
});

// 选中的菜单项
const selectedKeys = ref([]);

// 监听路由变化，更新选中的菜单项
watch(
  () => route.path,
  (path) => {
    if (path === '/') {
      selectedKeys.value = [''];
      return;
    }
    
    // 对于嵌套路由，需要找到完整的路径
    const matched = route.matched;
    if (matched.length > 1) {
      selectedKeys.value = [matched[matched.length - 1].path];
      return;
    }
    
    selectedKeys.value = [path];
  },
  { immediate: true }
);

// 处理菜单点击
function handleMenuClick({ key }: { key: string }) {
  // 如果是完整路径（以/开头），直接跳转
  if (key.startsWith('/')) {
    router.push(key);
  } else {
    // 否则拼接路径
    router.push(`/${key}`);
  }
}

// 处理退出登录
async function handleLogout() {
  try {
    // 清除用户信息和 token
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    
    // 提示成功
    message.success('退出登录成功');
    
    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    message.error('退出登录失败');
  }
}
</script>

<style scoped>
:deep(.ant-layout-sider) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

:deep(.ant-layout-header) {
  padding: 0 16px;
  line-height: 64px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}

:deep(.ant-menu-dark) {
  background: #001529;
}

:deep(.ant-menu-dark .ant-menu-item-selected) {
  background-color: #1890ff;
}
</style> 