<template>
  <a-layout class="min-h-screen">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="site-layout-sider"
    >
      <div class="logo p-4 h-16 flex items-center justify-center">
        <h1 class="text-white text-lg font-bold truncate" v-if="!collapsed">业财一体化系统</h1>
        <h1 class="text-white text-lg font-bold" v-else>业财</h1>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="dark"
        mode="inline"
      >
        <a-menu-item key="dashboard">
          <template #icon>
            <dashboard-outlined />
          </template>
          <span>仪表盘</span>
          <router-link to="/dashboard" />
        </a-menu-item>
        
        <a-menu-item key="customer-list">
          <template #icon>
            <team-outlined />
          </template>
          <span>客户信息管理</span>
          <router-link to="/customer/list" />
        </a-menu-item>
        
        <a-sub-menu key="project">
          <template #icon>
            <project-outlined />
          </template>
          <template #title>项目管理</template>
          <a-menu-item key="project-list">
            <router-link to="/project/list">项目列表</router-link>
          </a-menu-item>
          <a-menu-item key="project-task">
            <router-link to="/project/task">任务管理</router-link>
          </a-menu-item>
        </a-sub-menu>
        
        <a-sub-menu key="contract">
          <template #icon>
            <file-text-outlined />
          </template>
          <template #title>合同管理</template>
          <a-menu-item key="contract-list">
            <router-link to="/contract/list">合同列表</router-link>
          </a-menu-item>
          <a-menu-item key="contract-review">
            <router-link to="/contract/review">合同审批</router-link>
          </a-menu-item>
        </a-sub-menu>
        
        <a-sub-menu key="finance">
          <template #icon>
            <account-book-outlined />
          </template>
          <template #title>财务管理</template>
          <a-menu-item key="finance-invoice">
            <router-link to="/finance/invoice">发票管理</router-link>
          </a-menu-item>
          <a-menu-item key="finance-payment">
            <router-link to="/finance/payment">收付款管理</router-link>
          </a-menu-item>
        </a-sub-menu>
        
        <a-sub-menu key="report">
          <template #icon>
            <bar-chart-outlined />
          </template>
          <template #title>报表分析</template>
          <a-menu-item key="report-sales">
            <router-link to="/report/sales">销售报表</router-link>
          </a-menu-item>
          <a-menu-item key="report-finance">
            <router-link to="/report/finance">财务报表</router-link>
          </a-menu-item>
        </a-sub-menu>
        
        <a-menu-item key="settings">
          <template #icon>
            <setting-outlined />
          </template>
          <span>系统设置</span>
          <router-link to="/settings" />
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    
    <!-- 主体内容区 -->
    <a-layout>
      <!-- 头部 -->
      <a-layout-header class="bg-white p-0 flex justify-between items-center">
        <div class="flex items-center">
          <!-- 折叠按钮 -->
          <menu-unfold-outlined
            v-if="collapsed"
            class="trigger px-6 text-lg"
            @click="() => (collapsed = !collapsed)"
          />
          <menu-fold-outlined
            v-else
            class="trigger px-6 text-lg"
            @click="() => (collapsed = !collapsed)"
          />
          
          <!-- 面包屑 -->
          <a-breadcrumb class="ml-4">
            <a-breadcrumb-item>首页</a-breadcrumb-item>
            <a-breadcrumb-item>{{ currentRoute }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <!-- 用户信息 -->
        <div class="mr-6 flex items-center">
          <a-badge count="5">
            <a-button type="text" shape="circle">
              <template #icon><bell-outlined /></template>
            </a-button>
          </a-badge>
          
          <a-dropdown class="ml-4">
            <div class="flex items-center cursor-pointer">
              <a-avatar class="mr-2">
                <template #icon><user-outlined /></template>
              </a-avatar>
              <span>管理员</span>
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <user-outlined />
                  个人中心
                </a-menu-item>
                <a-menu-item key="settings">
                  <setting-outlined />
                  账户设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <logout-outlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 内容区 -->
      <a-layout-content class="p-6 bg-gray-100">
        <div class="bg-white p-6 min-h-full rounded-md shadow-sm">
          <router-view></router-view>
        </div>
      </a-layout-content>
      
      <!-- 页脚 -->
      <a-layout-footer class="text-center">
        业财一体化系统 ©2023 Created by Your Company
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  TeamOutlined,
  ProjectOutlined,
  FileTextOutlined,
  AccountBookOutlined,
  BarChartOutlined,
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue';

// 侧边栏折叠状态
const collapsed = ref(false);

// 当前选中的菜单项
const route = useRoute();
const selectedKeys = computed(() => {
  const path = route.path;
  if (path.startsWith('/dashboard')) return ['dashboard'];
  if (path.startsWith('/customer')) return ['customer-list'];
  if (path.startsWith('/project/list')) return ['project-list'];
  if (path.startsWith('/project/task')) return ['project-task'];
  if (path.startsWith('/contract/list')) return ['contract-list'];
  if (path.startsWith('/contract/review')) return ['contract-review'];
  if (path.startsWith('/finance/invoice')) return ['finance-invoice'];
  if (path.startsWith('/finance/payment')) return ['finance-payment'];
  if (path.startsWith('/report/sales')) return ['report-sales'];
  if (path.startsWith('/report/finance')) return ['report-finance'];
  if (path.startsWith('/settings')) return ['settings'];
  return ['dashboard'];
});

// 当前路由名称
const currentRoute = computed(() => {
  const path = route.path;
  if (path.startsWith('/dashboard')) return '仪表盘';
  if (path.startsWith('/customer')) return '客户信息管理';
  if (path.startsWith('/project/list')) return '项目列表';
  if (path.startsWith('/project/task')) return '任务管理';
  if (path.startsWith('/contract/list')) return '合同列表';
  if (path.startsWith('/contract/review')) return '合同审批';
  if (path.startsWith('/finance/invoice')) return '发票管理';
  if (path.startsWith('/finance/payment')) return '收付款管理';
  if (path.startsWith('/report/sales')) return '销售报表';
  if (path.startsWith('/report/finance')) return '财务报表';
  if (path.startsWith('/settings')) return '系统设置';
  return '仪表盘';
});
</script>

<style scoped>
.site-layout-sider {
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
}

.logo {
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 4px;
}

.trigger {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}
</style>
