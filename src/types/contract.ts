export interface Contract {
  id: string;
  name: string;
  code: string;
  type: ContractType;
  status: ContractStatus;
  category: 'sales' | 'procurement'; // 合同类别
  customerId?: string;
  customerName?: string;
  supplierId?: string; // 供应商ID
  supplierName?: string; // 供应商名称
  partnerName?: string; // 合作方名称
  amount: number;
  signDate: string;
  startDate: string;
  endDate: string;
  ownerId: string;
  ownerName: string;
  description: string;
  createTime: string;
  updateTime: string;
}

export enum ContractType {
  SALES = 'sales',
  PURCHASE = 'purchase',
  SERVICE = 'service',
  COOPERATION = 'cooperation',
  OTHER = 'other',
}

export enum ContractStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  APPROVED = 'approved',
  SIGNED = 'signed',
  EXPIRED = 'expired',
  TERMINATED = 'terminated',
}

export interface ContractAmendment {
  id: string;
  contractId: string;
  contractName: string;
  contractCode: string;
  type: AmendmentType;
  title: string;
  description: string;
  oldValue: string;
  newValue: string;
  applicantId: string;
  applicant: string;
  approverId: string;
  approver: string;
  amendmentDate: string;
  status: AmendmentStatus;
  attachments: ContractAttachment[];
  createTime: string;
  updateTime: string;
}

export enum AmendmentType {
  EXTENSION = 'extension',
  PRICE_CHANGE = 'price_change',
  SCOPE_CHANGE = 'scope_change',
  PARTY_CHANGE = 'party_change',
  TERMINATION = 'termination',
  OTHER = 'other',
}

export enum AmendmentStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export interface ContractAttachment {
  id: string;
  contractId: string;
  amendmentId?: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploaderId: string;
  uploaderName: string;
  createTime: string;
  updateTime: string;
}

export interface ContractParty {
  id: string;
  contractId: string;
  name: string;
  type: PartyType;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  address: string;
  createTime: string;
  updateTime: string;
}

export enum PartyType {
  CLIENT = 'client',
  PROVIDER = 'provider',
  PARTNER = 'partner',
  GUARANTOR = 'guarantor',
  OTHER = 'other',
} 