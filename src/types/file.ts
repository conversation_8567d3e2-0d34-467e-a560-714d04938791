/**
 * 文件管理模块类型定义
 */

// 文件类型枚举
export enum FileType {
  PDF = 'pdf',
  WORD = 'word',
  EXCEL = 'excel',
  IMAGE = 'image',
  CODE = 'code',
  ZIP = 'zip',
  OTHER = 'other'
}

// 文件分类枚举
export enum FileCategory {
  BUSINESS = 'business',       // 工商档案
  INTELLECTUAL = 'intellectual', // 知识产权
  CERTIFICATE = 'certificate', // 资质证书
  LEGAL = 'legal'              // 其他法律文件
}

// 知识产权类型枚举
export enum IntellectualPropertyType {
  PATENT = 'patent',           // 专利
  TRADEMARK = 'trademark',     // 商标
  COPYRIGHT = 'copyright',     // 版权
  SOFTWARE = 'software'        // 软件著作权
}

// 工商档案类型枚举
export enum BusinessFileType {
  BUSINESS_LICENSE = 'business_license',   // 营业执照
  TAX_REGISTRATION = 'tax_registration',   // 税务登记证
  ORGANIZATION_CODE = 'organization_code', // 组织机构代码证
  BANK_ACCOUNT = 'bank_account',          // 银行开户许可证
  OTHER = 'other'                         // 其他
}

// 权限类型枚举
export enum PermissionType {
  READ = 'read',               // 只读权限
  EDIT = 'edit',               // 编辑权限 
  DOWNLOAD = 'download',       // 下载权限
  FULL = 'full'                // 全部权限
}

// 文件状态枚举
export enum FileStatus {
  ACTIVE = 'active',           // 有效
  EXPIRED = 'expired',         // 已过期
  EXPIRING_SOON = 'expiring_soon', // 即将过期
  ARCHIVED = 'archived',       // 已归档
  DELETED = 'deleted'          // 已删除
}

// 代码审核状态枚举
export enum CodeReviewStatus {
  PENDING = 'pending',         // 待审核
  APPROVED = 'approved',       // 已通过
  REJECTED = 'rejected'        // 已拒绝
}

// 知识产权状态枚举
export enum IPStatus {
  PENDING = 'pending',         // 申请中
  GRANTED = 'granted',         // 已授权
  REJECTED = 'rejected',       // 已驳回
  EXPIRED = 'expired'          // 已过期
}

// 基础文件接口
export interface File {
  id: string;
  name: string;
  code: string;              // 文件编号
  type: FileType;            // 文件类型
  category: FileCategory;    // 文件分类
  size: number;              // 文件大小(KB)
  uploadTime: string;        // 上传时间
  path: string;              // 文件路径
  projectId?: string;        // 关联项目ID
  tags?: string[];           // 标签
  description?: string;      // 描述
  createdBy: string;         // 创建人
  updatedBy?: string;        // 更新人
  updatedTime?: string;      // 更新时间
  status: FileStatus;        // 文件状态
  versions?: FileVersion[];  // 版本历史
  permissions?: Permission[]; // 权限设置
}

// 文件版本接口
export interface FileVersion {
  id: string;
  fileId: string;            // 关联文件ID
  versionNumber: string;     // 版本号
  changeTime: string;        // 变更时间
  changeNote: string;        // 变更备注
  size: number;              // 文件大小
  path: string;              // 文件路径
  createdBy: string;         // 创建人
}

// 权限设置接口
export interface Permission {
  id: string;
  fileId: string;            // 关联文件ID
  targetType: 'user' | 'role' | 'department' | 'project'; // 目标类型
  targetId: string;          // 目标ID
  permissionType: PermissionType; // 权限类型
  expireTime?: string;       // 过期时间(临时授权)
  createdBy: string;         // 创建人
  createdTime: string;       // 创建时间
}

// 操作日志接口
export interface FileOperationLog {
  id: string;
  fileId: string;            // 关联文件ID
  operation: 'upload' | 'download' | 'view' | 'edit' | 'permission_change' | 'delete'; // 操作类型
  operationTime: string;     // 操作时间
  operatedBy: string;        // 操作人
  details: string;           // 操作详情
  ipAddress?: string;        // IP地址
}

// 工商档案文件接口
export interface BusinessFile extends File {
  businessType: BusinessFileType; // 工商档案类型
  registrationDate?: string;     // 登记日期
  registrationAuthority?: string; // 登记机关
  expiryDate?: string;           // 有效期
  subject?: string;              // 登记主体(如子公司)
}

// 知识产权文件接口
export interface IntellectualPropertyFile extends File {
  ipType: IntellectualPropertyType; // 知识产权类型
  applicationNumber?: string;      // 申请号/注册号
  applicationDate?: string;        // 申请日期
  grantDate?: string;              // 授权日期
  expiryDate?: string;             // 到期日期
  ipStatus: IPStatus;              // 知识产权状态
  inventors?: string[];            // 发明人/设计人
  annualFeeDate?: string;          // 年费缴纳日期
}

// 代码文件接口
export interface CodeFile extends File {
  language: string;               // 编程语言
  repositoryUrl?: string;         // 代码仓库URL
  branch?: string;                // 分支名称
  commitId?: string;              // 提交ID
  reviewStatus: CodeReviewStatus; // 代码审核状态
  reviewComment?: string;         // 审核意见
  lineCount?: number;             // 代码行数
}

// 资质证书文件接口
export interface CertificateFile extends File {
  certificateType: string;        // 证书类型
  certificateNumber: string;      // 证书编号
  issueDate: string;              // 颁发日期
  expiryDate: string;             // 到期日期
  issuer: string;                 // 颁发机构
  scope?: string;                 // 认证范围
}

// 法律文件接口
export interface LegalFile extends File {
  legalType: string;              // 法律文件类型
  effectiveDate?: string;         // 生效日期
  parties?: string[];             // 相关方
  isTemplate: boolean;            // 是否为模板
}

// 文件搜索筛选条件
export interface FileSearchFilter {
  keyword?: string;               // 关键字（文件名、编号）
  category?: FileCategory;        // 文件分类
  fileType?: FileType;            // 文件类型
  startDate?: string;             // 开始日期
  endDate?: string;               // 结束日期
  projectId?: string;             // 关联项目ID
  status?: FileStatus;            // 文件状态
  tags?: string[];                // 标签
  uploadBy?: string;              // 上传人
}

// 分页信息
export interface PaginationInfo {
  current: number;                // 当前页码
  pageSize: number;               // 每页条数
  total: number;                  // 总条数
  showTotal: (total: number) => string; // 显示总条数的函数
}

// 文件统计信息
export interface FileStatistics {
  totalCount: number;             // 文件总数
  totalSize: number;              // 总大小(KB)
  categoryCount: Record<FileCategory, number>; // 各分类文件数量
  typeCount: Record<FileType, number>;    // 各类型文件数量
  expiringCount: number;          // 即将过期文件数
  mostDownloaded: {id: string, name: string, count: number}[]; // 下载最多的文件
}

// 文件上传参数
export interface FileUploadParams {
  name: string;                   // 文件名称
  category: FileCategory;         // 文件分类
  projectId?: string;             // 关联项目ID
  tags?: string[];                // 标签
  description?: string;           // 描述
  metadata?: Record<string, any>; // 元数据（根据不同文件类型有不同字段）
} 