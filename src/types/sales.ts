/**
 * 销售管理模块类型定义
 */

// 订单状态枚举
export enum OrderStatus {
  PENDING_REVIEW = 'pending_review',  // 待审核
  APPROVED = 'approved',             // 已通过
  REJECTED = 'rejected',             // 已拒绝
  PENDING_EXECUTION = 'pending_execution', // 待执行
  EXECUTING = 'executing',           // 执行中
  COMPLETED = 'completed',           // 已完成
  CANCELLED = 'cancelled'            // 已取消
}

// 物流状态枚举
export enum LogisticsStatus {
  PENDING = 'pending',           // 待发货
  SHIPPING = 'shipping',         // 运输中
  DELIVERED = 'delivered',       // 已签收
  RETURNED = 'returned'          // 已退回
}

// 发票类型枚举
export enum InvoiceType {
  GENERAL = 'general',           // 普通发票
  SPECIAL = 'special'            // 增值税专用发票
}

// 发票状态枚举
export enum InvoiceStatus {
  PENDING_APPROVAL = 'pending_approval', // 待审批
  APPROVED = 'approved',                // 审批通过
  REJECTED = 'rejected',                // 审批拒绝
  ISSUED = 'issued',                    // 已开具
  DELIVERED = 'delivered',              // 已送达
  CANCELLED = 'cancelled'               // 已作废
}

// 收款状态枚举
export enum PaymentStatus {
  UNPAID = 'unpaid',             // 未支付
  PARTIAL_PAID = 'partial_paid', // 部分支付
  PAID = 'paid',                 // 已支付
  OVERDUE = 'overdue'            // 逾期
}

// 审批状态枚举
export enum ApprovalStatus {
  PENDING = 'pending',           // 待审批
  APPROVED = 'approved',         // 已通过
  REJECTED = 'rejected'          // 已拒绝
}

// 销售订单基础类型
export interface SalesOrder {
  id: string;                     // 订单编号
  orderNumber: string;            // 订单编号
  contractId: string;             // 关联合同编号
  contractNumber?: string;        // 合同编号
  customerId: string;             // 客户编号
  customerName: string;           // 客户名称
  totalAmount: number;            // 订单总金额
  status: OrderStatus;            // 订单状态
  createdTime: string;            // 创建时间
  createdBy: string;              // 创建人
  updatedTime?: string;           // 更新时间
  updatedBy?: string;             // 更新人
  remark?: string;                // 备注
  progress?: number;              // 执行进度(百分比)
  items: OrderItem[];             // 订单项目
  approvals?: OrderApproval[];    // 审批记录
  shipping?: ShippingInfo[];      // 发货信息
  invoices?: InvoiceInfo[];       // 发票信息
  payments?: PaymentInfo[];       // 收款信息
  authCode?: string;              // 软件授权码
}

// 订单项目类型
export interface OrderItem {
  id: string;                     // 项目编号
  orderId: string;                // 订单编号
  productName: string;            // 商品/服务名称
  quantity: number;               // 数量
  unitPrice: number;              // 单价
  amount: number;                 // 总金额
  specification?: string;         // 规格说明
  remark?: string;                // 备注
}

// 订单审批记录
export interface OrderApproval {
  id: string;                     // 审批记录编号
  orderId: string;                // 订单编号
  approverName: string;           // 审批人姓名
  approverId: string;             // 审批人编号
  approvalTime: string;           // 审批时间
  status: ApprovalStatus;         // 审批状态
  comment?: string;               // 审批意见
  level: number;                  // 审批级别
}

// 发货信息
export interface ShippingInfo {
  id: string;                     // 发货记录编号
  orderId: string;                // 订单编号
  shippingTime: string;           // 发货时间
  recipientName: string;          // 收货人
  recipientPhone: string;         // 收货人电话
  recipientAddress: string;       // 收货地址
  logisticsCompany?: string;      // 物流公司
  trackingNumber?: string;        // 物流单号
  status: LogisticsStatus;        // 物流状态
  items: ShippingItem[];          // 发货明细
  shippingNoteUrl?: string;       // 发货单URL
  remark?: string;                // 备注
}

// 发货明细
export interface ShippingItem {
  id: string;                     // 发货明细编号
  shippingId: string;             // 发货记录编号
  orderItemId: string;            // 订单项目编号
  productName: string;            // 商品名称
  quantity: number;               // 发货数量
  remark?: string;                // 备注
}

// 发票信息
export interface InvoiceInfo {
  id: string;                     // 发票编号
  orderId: string;                // 订单编号
  invoiceType: InvoiceType;       // 发票类型
  invoiceNumber?: string;         // 发票号码
  amount: number;                 // 发票金额
  taxRate: number;                // 税率
  status: InvoiceStatus;          // 发票状态
  applicantName: string;          // 申请人
  applicantId: string;            // 申请人编号
  applyTime: string;              // 申请时间
  approvalTime?: string;          // 审批时间
  issuedTime?: string;            // 开票时间
  customerTaxNumber?: string;     // 客户税号
  customerAddress?: string;       // 客户地址
  customerBank?: string;          // 客户开户行
  customerBankAccount?: string;   // 客户银行账号
  invoiceUrl?: string;            // 发票文件URL
  remark?: string;                // 备注
}

// 收款信息
export interface PaymentInfo {
  id: string;                     // 收款编号
  orderId: string;                // 订单编号
  planAmount: number;             // 计划收款金额
  planTime: string;               // 计划收款时间
  actualAmount?: number;          // 实际收款金额
  actualTime?: string;            // 实际收款时间
  paymentMethod?: string;         // 付款方式
  bankTransactionId?: string;     // 银行流水号
  status: PaymentStatus;          // 收款状态
  verificationStatus: 'unverified' | 'partially_verified' | 'verified'; // 核销状态
  remark?: string;                // 备注
}

// 合同简要信息
export interface ContractBrief {
  id: string;                     // 合同编号
  contractNumber: string;         // 合同编号
  contractName: string;           // 合同名称
  customerId: string;             // 客户编号
  customerName: string;           // 客户名称
  amount: number;                 // 合同金额
  signDate: string;               // 签订日期
  deliveryDate?: string;          // 交付日期
  status: string;                 // 合同状态
}

// 订单列表查询参数
export interface OrderSearchParams {
  keyword?: string;               // 关键词(订单编号/客户名称/合同编号)
  status?: OrderStatus;           // 订单状态
  startDate?: string;             // 开始日期
  endDate?: string;               // 结束日期
  customerId?: string;            // 客户编号
  pageSize?: number;              // 每页记录数
  current?: number;               // 当前页码
}

// 分页结果
export interface PaginationResult<T> {
  list: T[];                      // 数据列表
  total: number;                  // 总条数
  pageSize: number;               // 每页条数
  current: number;                // 当前页码
}

// 订单统计信息
export interface OrderStatistics {
  totalOrders: number;            // 订单总数
  totalAmount: number;            // 订单总金额
  pendingReviewCount: number;     // 待审核数量
  executingCount: number;         // 执行中数量
  completedCount: number;         // 已完成数量
  monthlyOrders: {                // 每月订单数量
    month: string;                // 月份
    count: number;                // 数量
    amount: number;               // 金额
  }[];
}

// 创建订单参数
export interface CreateOrderParams {
  contractId: string;             // 关联合同编号
  customerId: string;             // 客户编号
  totalAmount: number;            // 订单总金额
  remark?: string;                // 备注
  items: Omit<OrderItem, 'id' | 'orderId'>[];  // 订单项目
  authCode?: string;              // 软件授权码
}

// 订单审批参数
export interface ApproveOrderParams {
  orderId: string;                // 订单编号
  status: ApprovalStatus;         // 审批状态
  comment?: string;               // 审批意见
}

// 创建发货计划参数
export interface CreateShippingParams {
  orderId: string;                // 订单编号
  shippingTime: string;           // 发货时间
  recipientName: string;          // 收货人
  recipientPhone: string;         // 收货人电话
  recipientAddress: string;       // 收货地址
  items: {                        // 发货明细
    orderItemId: string;          // 订单项目编号
    quantity: number;             // 发货数量
    remark?: string;              // 备注
  }[];
  remark?: string;                // 备注
}

// 发票申请参数
export interface ApplyInvoiceParams {
  orderId: string;                // 订单编号
  invoiceType: InvoiceType;       // 发票类型
  amount: number;                 // 发票金额
  customerTaxNumber?: string;     // 客户税号
  customerAddress?: string;       // 客户地址
  customerBank?: string;          // 客户开户行
  customerBankAccount?: string;   // 客户银行账号
  remark?: string;                // 备注
}

// 创建收款计划参数
export interface CreatePaymentPlanParams {
  orderId: string;                // 订单编号
  planAmount: number;             // 计划收款金额
  planTime: string;               // 计划收款时间
  remark?: string;                // 备注
}

// 记录实际收款参数
export interface RecordPaymentParams {
  paymentId: string;              // 收款计划编号
  actualAmount: number;           // 实际收款金额
  actualTime: string;             // 实际收款时间
  paymentMethod: string;          // 付款方式
  bankTransactionId?: string;     // 银行流水号
  remark?: string;                // 备注
}

// 核销收款参数
export interface VerifyPaymentParams {
  paymentId: string;              // 收款编号
  verificationStatus: 'unverified' | 'partially_verified' | 'verified'; // 核销状态
  remark?: string;                // 备注
} 