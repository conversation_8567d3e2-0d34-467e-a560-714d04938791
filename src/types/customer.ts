// 客户类型枚举（基于文档规范）
export enum CustomerType {
  ENTERPRISE = 'C', // 企业客户
  GOVERNMENT = 'G', // 政府客户
}

// 行业分类枚举（基于文档规范）
export enum IndustryType {
  GOVERNMENT_AGENCIES = 'I01', // 政府机构
  FINANCIAL_SERVICES = 'I02', // 金融服务
  INFORMATION_TECHNOLOGY = 'I03', // 信息技术/互联网
  MANUFACTURING_INDUSTRIAL = 'I04', // 制造与工业
  RETAIL_CONSUMER_GOODS = 'I05', // 零售与消费品
  ENERGY_UTILITIES = 'I06', // 能源与公用事业
  TRANSPORTATION_LOGISTICS = 'I07', // 交通与物流
  HEALTHCARE_PHARMACEUTICALS = 'I08', // 医疗与健康
  EDUCATION_RESEARCH = 'I09', // 教育与科研
  REAL_ESTATE_CONSTRUCTION = 'I10', // 房地产与建筑
  PROFESSIONAL_SERVICES = 'I11', // 专业服务
  AGRICULTURE_FORESTRY = 'I12', // 农林牧渔
  OTHER_UNCATEGORIZED = 'I13', // 其他/未分类
}

export enum CustomerStatus {
  NEW = 'new',
  FOLLOWING = 'following',
  NEGOTIATING = 'negotiating',
  SIGNED = 'signed',
  LOST = 'lost'
}

export interface Customer {
  id: string;
  code: string;
  name: string;
  taxId: string;
  type: CustomerType;
  status: CustomerStatus;
  industry: IndustryType;
  contact: string;
  phone: string;
  email: string;
  address: string;
  source: string;
  owner: string;
  ownerName: string;
  createTime: string;
  updateTime: string;
  lastContactTime: string;
  remark: string;
}

export interface CustomerInvoiceInfo {
  id: string;
  customerId: string;
  companyName: string;
  taxId: string;
  bank: string;
  bankAccount: string;
  address: string;
  phone: string;
  remark: string;
}

export interface CustomerContact {
  id: string;
  customerId: string;
  content: string;
  method: string;
  contactTime: string;
  creator: string;
  creatorId: string;
  nextContactTime: string;
  status: string;
  createTime: string;
}

export interface CustomerStats {
  total: number;
  growthRate: number;
  newMonthly: number;
  newGrowth: number;
  formalCount: number;
  formalRate: number;
  avgConvertDays: number;
  convertDaysChange: number;
}

export type CustomerSource = 
  | 'website'
  | 'referral'
  | 'exhibition'
  | 'advertisement'
  | 'social'
  | 'other';

export type ContactMethod = 
  | 'phone'
  | 'visit'
  | 'email'
  | 'meeting'
  | 'wechat'
  | 'other'; 

 