import { CustomerType, IndustryType, SupplierStatus } from '@/constants/customer';

// 供应商基础信息
export interface Supplier {
  id: string;
  code: string; // 供应商编号，如：S250624001
  name: string; // 供应商名称
  type: CustomerType; // 供应商类型：企业/政府
  status: SupplierStatus; // 合作状态
  industry: IndustryType; // 所属行业
  taxId?: string; // 纳税人识别号
  contact: string; // 联系人
  phone: string; // 联系电话
  email?: string; // 电子邮箱
  address: string; // 详细地址
  province?: string; // 省份
  city?: string; // 城市
  website?: string; // 官网地址
  remark?: string; // 备注
  owner: string; // 负责人ID
  ownerName?: string; // 负责人姓名
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  lastContactTime?: string; // 最近联系时间
}

// 供应商统计信息
export interface SupplierStatistics {
  projectCount: number; // 参与项目数量
  contractCount: number; // 合同数量
  totalAmount: number; // 合作总金额
  qualityScore?: number; // 质量评分
}

// 供应商开票信息
export interface SupplierInvoiceInfo {
  companyName: string; // 公司名称
  taxId: string; // 纳税人识别号
  bank: string; // 开户银行
  bankAccount: string; // 银行账号
  address: string; // 开票地址
  phone: string; // 联系电话
  remark?: string; // 备注
}

// 供应商详情（包含统计和开票信息）
export interface SupplierDetail extends Supplier {
  statistics: SupplierStatistics;
  invoiceInfo: SupplierInvoiceInfo;
}

// 供应商来源类型
export enum SupplierSource {
  REFERRAL = 'referral', // 推荐
  WEBSITE = 'website', // 官网
  EXHIBITION = 'exhibition', // 展会
  PARTNER = 'partner', // 合作伙伴
  ADVERTISEMENT = 'advertisement', // 广告
  OTHER = 'other', // 其他
}

// 供应商评级
export enum SupplierRating {
  A = 'A', // 优秀
  B = 'B', // 良好
  C = 'C', // 一般
  D = 'D', // 较差
}

// 供应商能力类型
export enum SupplierCapability {
  MANUFACTURING = 'manufacturing', // 生产制造
  SERVICE = 'service', // 服务提供
  TECHNOLOGY = 'technology', // 技术支持
  LOGISTICS = 'logistics', // 物流配送
  CONSULTING = 'consulting', // 咨询服务
  OTHER = 'other', // 其他
}

// 供应商查询参数
export interface SupplierQueryParams {
  keyword?: string; // 关键词搜索
  type?: CustomerType; // 供应商类型
  status?: SupplierStatus; // 合作状态
  industry?: IndustryType; // 所属行业
  owner?: string; // 负责人
  province?: string; // 省份
  city?: string; // 城市
  dateRange?: [string, string]; // 创建日期范围
  current?: number; // 当前页
  pageSize?: number; // 页面大小
}

// 供应商表单数据
export interface SupplierFormData {
  id?: string;
  name: string;
  type: CustomerType;
  status: SupplierStatus;
  industry: IndustryType;
  taxId?: string;
  contact: string;
  phone: string;
  email?: string;
  address: string;
  province?: string;
  city?: string;
  website?: string;
  remark?: string;
  owner: string;
  source?: SupplierSource;
  capability?: SupplierCapability[];
  rating?: SupplierRating;
} 