export interface Project {
  id: string;
  name: string;
  code: string;
  type: ProjectType;
  customerId: string;
  customerName: string;
  budget: number;
  progress: number;
  status: ProjectStatus;
  startDate: string;
  endDate: string;
  managerId: string;
  managerName: string;
  description: string;
  createTime: string;
  updateTime: string;
}

export enum ProjectType {
  DEVELOPMENT = 'development',
  CONSULTING = 'consulting',
  IMPLEMENTATION = 'implementation',
  MAINTENANCE = 'maintenance',
  TRAINING = 'training',
}

export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export interface ProjectMilestone {
  id: string;
  projectId: string;
  name: string;
  description: string;
  dueDate: string;
  status: MilestoneStatus;
  progress: number;
  createTime: string;
  updateTime: string;
}

export enum MilestoneStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  DELAYED = 'delayed',
}

export interface ProjectTask {
  id: string;
  projectId: string;
  name: string;
  description: string;
  assigneeId: string;
  assigneeName: string;
  priority: TaskPriority;
  status: TaskStatus;
  startDate: string;
  dueDate: string;
  progress: number;
  createTime: string;
  updateTime: string;
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  COMPLETED = 'completed',
}

export interface ProjectMember {
  id: string;
  projectId: string;
  userId: string;
  userName: string;
  role: MemberRole;
  joinDate: string;
  workload: number;
  createTime: string;
  updateTime: string;
}

export enum MemberRole {
  MANAGER = 'manager',
  DEVELOPER = 'developer',
  DESIGNER = 'designer',
  TESTER = 'tester',
  CONSULTANT = 'consultant',
}

export interface ProjectDocument {
  id: string;
  projectId: string;
  name: string;
  type: DocumentType;
  size: number;
  url: string;
  uploaderId: string;
  uploaderName: string;
  createTime: string;
  updateTime: string;
}

export enum DocumentType {
  REQUIREMENT = 'requirement',
  DESIGN = 'design',
  DEVELOPMENT = 'development',
  TEST = 'test',
  DEPLOYMENT = 'deployment',
  OTHER = 'other',
}

export interface ProjectRisk {
  id: string;
  projectId: string;
  name: string;
  description: string;
  level: RiskLevel;
  status: RiskStatus;
  ownerId: string;
  ownerName: string;
  mitigation: string;
  createTime: string;
  updateTime: string;
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum RiskStatus {
  IDENTIFIED = 'identified',
  ANALYZING = 'analyzing',
  MITIGATING = 'mitigating',
  RESOLVED = 'resolved',
  ACCEPTED = 'accepted',
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  department?: string;
  position?: string;
} 