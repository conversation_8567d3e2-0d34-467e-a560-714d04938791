# 依赖目录
node_modules
.pnpm-store

# 构建产物
/dist
/build

# 本地环境文件
.env.local
.env.*.local

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json

# 操作系统文件
.DS_Store
Thumbs.db

# 测试覆盖率报告
/coverage

# 缓存目录
.cache
.temp
.eslintcache
.stylelintcache

# 类型声明生成文件
*.d.ts
!src/shims-vue.d.ts
!src/types/**/*.d.ts

# 临时文件
*.log
*.tmp
*.temp

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# TypeScript编译输出文件
src/**/*.js
src/**/*.js.map
!src/main.js
